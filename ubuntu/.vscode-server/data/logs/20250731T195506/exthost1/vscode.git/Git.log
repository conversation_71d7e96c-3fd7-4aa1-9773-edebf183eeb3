2025-07-31 19:55:12.893 [info] [main] Log level: Info
2025-07-31 19:55:12.893 [info] [main] Validating found git in: "git"
2025-07-31 19:55:12.893 [info] [main] Using git "2.43.0" from "git"
2025-07-31 19:55:12.893 [info] [Model][doInitialScan] Initial repository scan started
2025-07-31 19:55:12.893 [info] > git rev-parse --show-toplevel [1061ms]
2025-07-31 19:55:12.893 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:12.893 [info] > git rev-parse --show-toplevel [46ms]
2025-07-31 19:55:12.893 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:12.893 [info] > git rev-parse --show-toplevel [9ms]
2025-07-31 19:55:12.893 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:12.914 [info] > git rev-parse --show-toplevel [22ms]
2025-07-31 19:55:12.915 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:12.959 [info] > git rev-parse --show-toplevel [19ms]
2025-07-31 19:55:12.959 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:13.001 [info] > git rev-parse --show-toplevel [11ms]
2025-07-31 19:55:13.001 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:13.026 [info] > git rev-parse --show-toplevel [4ms]
2025-07-31 19:55:13.026 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:13.072 [info] > git rev-parse --show-toplevel [28ms]
2025-07-31 19:55:13.072 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:13.112 [info] > git rev-parse --show-toplevel [10ms]
2025-07-31 19:55:13.112 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:13.164 [info] > git rev-parse --show-toplevel [24ms]
2025-07-31 19:55:13.165 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:13.264 [info] > git rev-parse --show-toplevel [56ms]
2025-07-31 19:55:13.264 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:13.290 [info] > git rev-parse --show-toplevel [6ms]
2025-07-31 19:55:13.290 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:13.312 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-07-31 19:55:21.855 [info] > git rev-parse --show-toplevel [20ms]
2025-07-31 19:55:21.855 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:23.352 [info] > git rev-parse --show-toplevel [8ms]
2025-07-31 19:55:23.352 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:33.139 [info] > git rev-parse --show-toplevel [205ms]
2025-07-31 19:55:33.139 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:35.244 [info] > git rev-parse --show-toplevel [1858ms]
2025-07-31 19:55:35.244 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:37.244 [info] > git rev-parse --show-toplevel [18ms]
2025-07-31 19:55:37.244 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 19:55:38.938 [info] > git rev-parse --show-toplevel [31ms]
2025-07-31 19:55:38.939 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-31 20:36:53.474 [info] > git rev-parse --show-toplevel [5ms]
2025-07-31 20:36:53.506 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-07-31 20:36:53.574 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory/algo/instances/1009-algo-dhan-97
2025-07-31 20:36:53.574 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory/algo/instances/1009-algo-dhan-97
2025-07-31 20:36:53.617 [info] > git config --get commit.template [2ms]
2025-07-31 20:36:53.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-07-31 20:36:53.662 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:36:53.663 [info] > git rev-parse refs/remotes/origin/main [8ms]
2025-07-31 20:36:53.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-07-31 20:36:53.726 [info] > git status -z -uall [45ms]
2025-07-31 20:36:53.779 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [23ms]
2025-07-31 20:36:53.798 [info] > git config --get commit.template [22ms]
2025-07-31 20:36:53.799 [info] > git config --get --local branch.main.vscode-merge-base [3ms]
2025-07-31 20:36:53.799 [warning] [Git][config] git config failed: Failed to execute git
2025-07-31 20:36:53.833 [info] > git reflog main --grep-reflog=branch: Created from *. [18ms]
2025-07-31 20:36:53.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-07-31 20:36:53.871 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:36:53.871 [info] > git symbolic-ref --short refs/remotes/origin/HEAD [20ms]
2025-07-31 20:36:53.889 [info] > git rev-parse refs/remotes/origin/main [18ms]
2025-07-31 20:36:53.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [50ms]
2025-07-31 20:36:53.968 [info] > git status -z -uall [63ms]
2025-07-31 20:36:53.969 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [38ms]
2025-07-31 20:36:53.987 [info] > git config --add --local branch.main.vscode-merge-base origin/main [25ms]
2025-07-31 20:36:54.010 [info] > git config --get commit.template [3ms]
2025-07-31 20:36:54.031 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:36:54.048 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:36:54.049 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:36:54.096 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-07-31 20:36:54.125 [info] > git status -z -uall [59ms]
2025-07-31 20:36:54.165 [info] > git merge-base refs/heads/main refs/remotes/origin/main [20ms]
2025-07-31 20:36:54.183 [info] > git merge-base refs/heads/main refs/remotes/origin/main [21ms]
2025-07-31 20:36:54.200 [info] > git diff --name-status -z --diff-filter=ADMR e1efe3408182bd32b1a65bbee0a9987dd2970708...refs/remotes/origin/main [18ms]
2025-07-31 20:36:54.202 [info] > git diff --name-status -z --diff-filter=ADMR e1efe3408182bd32b1a65bbee0a9987dd2970708...refs/remotes/origin/main [2ms]
2025-07-31 20:36:54.304 [info] > git check-ignore -v -z --stdin [1ms]
2025-07-31 20:38:25.465 [info] > git config --get commit.template [34ms]
2025-07-31 20:38:25.477 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [18ms]
2025-07-31 20:38:25.493 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:38:25.494 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:38:25.551 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [18ms]
2025-07-31 20:38:25.999 [info] > git check-ignore -v -z --stdin [8ms]
2025-07-31 20:38:26.058 [info] > git status -z -uall [547ms]
2025-07-31 20:38:31.099 [info] > git config --get commit.template [17ms]
2025-07-31 20:38:31.101 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:38:31.116 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:38:31.118 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:38:31.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-07-31 20:38:31.179 [info] > git status -z -uall [46ms]
2025-07-31 20:38:36.214 [info] > git config --get commit.template [16ms]
2025-07-31 20:38:36.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-07-31 20:38:36.231 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:38:36.233 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:38:36.277 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-07-31 20:38:36.295 [info] > git status -z -uall [46ms]
2025-07-31 20:43:07.195 [info] > git config --get commit.template [18ms]
2025-07-31 20:43:07.207 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-07-31 20:43:07.226 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:43:07.229 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-07-31 20:43:07.287 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [18ms]
2025-07-31 20:43:07.688 [info] > git status -z -uall [438ms]
2025-07-31 20:43:12.743 [info] > git config --get commit.template [17ms]
2025-07-31 20:43:12.744 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:43:12.760 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:43:12.762 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:43:12.892 [info] > git status -z -uall [114ms]
2025-07-31 20:43:12.892 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [91ms]
2025-07-31 20:43:29.517 [info] > git config --get commit.template [19ms]
2025-07-31 20:43:29.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-07-31 20:43:29.535 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:43:29.537 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:43:29.582 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-07-31 20:43:29.600 [info] > git status -z -uall [45ms]
2025-07-31 20:45:21.725 [info] > git config --get commit.template [23ms]
2025-07-31 20:45:21.741 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-07-31 20:45:21.757 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:45:21.760 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-07-31 20:45:21.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-07-31 20:45:22.321 [info] > git status -z -uall [546ms]
2025-07-31 20:45:27.603 [info] > git config --get commit.template [10ms]
2025-07-31 20:45:27.622 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-07-31 20:45:27.638 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:45:27.639 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-07-31 20:45:27.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-07-31 20:45:27.702 [info] > git status -z -uall [47ms]
2025-07-31 20:46:07.107 [info] > git config --get commit.template [17ms]
2025-07-31 20:46:07.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-07-31 20:46:07.128 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/refs/remotes/origin/main'
2025-07-31 20:46:07.131 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-07-31 20:46:07.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [18ms]
2025-07-31 20:46:07.629 [info] > git status -z -uall [481ms]
2025-07-31 20:47:08.528 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/HEAD'
2025-07-31 20:47:08.584 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/config'
2025-07-31 20:47:08.686 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/config'
2025-07-31 20:47:08.719 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-97/.git/HEAD'
2025-07-31 20:47:26.459 [info] > git rev-parse --show-toplevel [4ms]
2025-07-31 20:47:26.503 [info] > git rev-parse --git-dir --git-common-dir [4ms]
2025-07-31 20:47:26.539 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory/algo/instances/1009-algo-dhan-98
2025-07-31 20:47:26.539 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory/algo/instances/1009-algo-dhan-98
2025-07-31 20:47:26.575 [info] > git config --get commit.template [5ms]
2025-07-31 20:47:26.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [122ms]
2025-07-31 20:47:26.738 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:47:26.740 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:47:26.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-07-31 20:47:26.814 [info] > git status -z -uall [57ms]
2025-07-31 20:47:26.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-07-31 20:47:26.900 [info] > git config --get commit.template [40ms]
2025-07-31 20:47:26.901 [info] > git config --get --local branch.main.vscode-merge-base [6ms]
2025-07-31 20:47:26.901 [warning] [Git][config] git config failed: Failed to execute git
2025-07-31 20:47:26.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-07-31 20:47:26.937 [info] > git reflog main --grep-reflog=branch: Created from *. [20ms]
2025-07-31 20:47:26.971 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:47:26.972 [info] > git symbolic-ref --short refs/remotes/origin/HEAD [18ms]
2025-07-31 20:47:26.989 [info] > git rev-parse refs/remotes/origin/main [17ms]
2025-07-31 20:47:27.029 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [41ms]
2025-07-31 20:47:27.059 [info] > git status -z -uall [54ms]
2025-07-31 20:47:27.059 [info] > git config --add --local branch.main.vscode-merge-base origin/main [2ms]
2025-07-31 20:47:27.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [33ms] (cancelled)
2025-07-31 20:47:27.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-07-31 20:47:27.133 [info] > git config --get commit.template [38ms]
2025-07-31 20:47:27.150 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:47:27.151 [info] > git check-ignore -v -z --stdin [20ms]
2025-07-31 20:47:27.152 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:47:27.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-07-31 20:47:27.228 [info] > git status -z -uall [60ms]
2025-07-31 20:47:27.264 [info] > git merge-base refs/heads/main refs/remotes/origin/main [19ms]
2025-07-31 20:47:27.284 [info] > git merge-base refs/heads/main refs/remotes/origin/main [20ms]
2025-07-31 20:47:27.305 [info] > git diff --name-status -z --diff-filter=ADMR e1efe3408182bd32b1a65bbee0a9987dd2970708...refs/remotes/origin/main [24ms]
2025-07-31 20:47:27.305 [info] > git diff --name-status -z --diff-filter=ADMR e1efe3408182bd32b1a65bbee0a9987dd2970708...refs/remotes/origin/main [4ms]
2025-07-31 20:48:39.821 [info] > git config --get commit.template [19ms]
2025-07-31 20:48:39.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-07-31 20:48:39.847 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:48:39.848 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-07-31 20:48:39.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-07-31 20:48:40.337 [info] > git status -z -uall [473ms]
2025-07-31 20:48:45.375 [info] > git config --get commit.template [17ms]
2025-07-31 20:48:45.376 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:48:45.391 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:48:45.392 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-07-31 20:48:45.427 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-07-31 20:48:45.445 [info] > git status -z -uall [37ms]
2025-07-31 20:52:20.197 [info] > git config --get commit.template [17ms]
2025-07-31 20:52:20.197 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:52:20.215 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:52:20.217 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-07-31 20:52:20.254 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-07-31 20:52:20.279 [info] > git status -z -uall [45ms]
2025-07-31 20:52:52.347 [info] > git config --get commit.template [19ms]
2025-07-31 20:52:52.348 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:52:52.364 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:52:52.366 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:52:52.418 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-07-31 20:52:52.437 [info] > git status -z -uall [54ms]
2025-07-31 20:52:57.500 [info] > git config --get commit.template [32ms]
2025-07-31 20:52:57.503 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-07-31 20:52:57.529 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:52:57.537 [info] > git rev-parse refs/remotes/origin/main [8ms]
2025-07-31 20:52:57.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-07-31 20:52:57.836 [info] > git status -z -uall [270ms]
2025-07-31 20:53:02.940 [info] > git config --get commit.template [23ms]
2025-07-31 20:53:03.030 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [23ms]
2025-07-31 20:53:03.092 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:53:03.125 [info] > git rev-parse refs/remotes/origin/main [33ms]
2025-07-31 20:53:03.849 [info] > git status -z -uall [696ms]
2025-07-31 20:53:03.849 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [596ms]
2025-07-31 20:53:29.256 [info] > git config --get commit.template [17ms]
2025-07-31 20:53:29.258 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-07-31 20:53:29.275 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:53:29.276 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:53:29.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-07-31 20:53:29.707 [info] > git status -z -uall [414ms]
2025-07-31 20:53:34.755 [info] > git config --get commit.template [16ms]
2025-07-31 20:53:34.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-07-31 20:53:34.771 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:53:34.773 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:53:34.817 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-07-31 20:53:34.835 [info] > git status -z -uall [46ms]
2025-07-31 20:54:12.829 [info] > git config --get commit.template [30ms]
2025-07-31 20:54:12.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:54:12.847 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:54:12.849 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:54:12.897 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-07-31 20:54:12.917 [info] > git status -z -uall [49ms]
2025-07-31 20:54:22.769 [info] > git config --get commit.template [18ms]
2025-07-31 20:54:22.771 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-07-31 20:54:22.789 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:54:22.792 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-07-31 20:54:22.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [18ms]
2025-07-31 20:54:23.267 [info] > git status -z -uall [458ms]
2025-07-31 20:55:37.136 [info] > git config --get commit.template [18ms]
2025-07-31 20:55:37.137 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:55:37.153 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:55:37.155 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:55:37.202 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-07-31 20:55:37.220 [info] > git status -z -uall [49ms]
2025-07-31 20:55:42.250 [info] > git config --get commit.template [7ms]
2025-07-31 20:55:42.269 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:55:42.286 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:55:42.287 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-07-31 20:55:42.334 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-07-31 20:55:42.353 [info] > git status -z -uall [48ms]
2025-07-31 20:58:55.165 [info] > git rev-parse --show-toplevel [3ms]
2025-07-31 20:58:55.207 [info] > git rev-parse --git-dir --git-common-dir [3ms]
2025-07-31 20:58:55.255 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory/algo/instances/1009-algo-dhan-99
2025-07-31 20:58:55.255 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory/algo/instances/1009-algo-dhan-99
2025-07-31 20:58:55.315 [info] > git config --get commit.template [17ms]
2025-07-31 20:58:55.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-07-31 20:58:55.376 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 20:58:55.380 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-07-31 20:58:55.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-07-31 20:58:55.496 [info] > git status -z -uall [93ms]
2025-07-31 20:58:55.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [37ms]
2025-07-31 20:58:55.599 [info] > git config --get commit.template [29ms]
2025-07-31 20:58:55.634 [info] > git config --get --local branch.main.vscode-merge-base [36ms]
2025-07-31 20:58:55.634 [warning] [Git][config] git config failed: Failed to execute git
2025-07-31 20:58:55.662 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [35ms]
2025-07-31 20:58:55.688 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 20:58:55.691 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-07-31 20:58:55.814 [info] > git reflog main --grep-reflog=branch: Created from *. [153ms]
2025-07-31 20:58:55.993 [info] > git status -z -uall [267ms]
2025-07-31 20:58:55.993 [info] > git check-ignore -v -z --stdin [76ms]
2025-07-31 20:58:55.993 [info] > git check-ignore -v -z --stdin [5ms]
2025-07-31 20:58:55.994 [info] > git symbolic-ref --short refs/remotes/origin/HEAD [134ms]
2025-07-31 20:58:56.020 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [207ms]
2025-07-31 20:58:56.030 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [10ms]
2025-07-31 20:58:56.063 [info] > git config --add --local branch.main.vscode-merge-base origin/main [7ms]
2025-07-31 20:58:56.148 [info] > git config --get commit.template [55ms]
2025-07-31 20:58:56.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-07-31 20:58:56.174 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 20:58:56.178 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-07-31 20:58:56.257 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-07-31 20:58:56.450 [info] > git status -z -uall [242ms]
2025-07-31 20:58:56.515 [info] > git merge-base refs/heads/main refs/remotes/origin/main [34ms]
2025-07-31 20:58:56.547 [info] > git merge-base refs/heads/main refs/remotes/origin/main [37ms]
2025-07-31 20:58:56.589 [info] > git diff --name-status -z --diff-filter=ADMR e1efe3408182bd32b1a65bbee0a9987dd2970708...refs/remotes/origin/main [43ms]
2025-07-31 20:58:56.592 [info] > git diff --name-status -z --diff-filter=ADMR e1efe3408182bd32b1a65bbee0a9987dd2970708...refs/remotes/origin/main [12ms]
2025-07-31 20:59:14.829 [info] > git config --get commit.template [25ms]
2025-07-31 20:59:14.884 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [29ms]
2025-07-31 20:59:14.885 [info] > git config --get commit.template [57ms]
2025-07-31 20:59:14.911 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:59:14.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [28ms]
2025-07-31 20:59:14.937 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 20:59:14.939 [info] > git rev-parse refs/remotes/origin/main [27ms]
2025-07-31 20:59:15.041 [info] > git status -z -uall [79ms]
2025-07-31 20:59:15.042 [info] > git rev-parse refs/remotes/origin/main [105ms]
2025-07-31 20:59:15.101 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [63ms]
2025-07-31 20:59:15.115 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [19ms]
2025-07-31 20:59:15.648 [info] > git status -z -uall [581ms]
2025-07-31 20:59:20.772 [info] > git config --get commit.template [27ms]
2025-07-31 20:59:20.775 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-07-31 20:59:20.797 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 20:59:20.800 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-07-31 20:59:20.882 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-07-31 20:59:20.920 [info] > git status -z -uall [97ms]
2025-07-31 20:59:37.139 [info] > git config --get commit.template [22ms]
2025-07-31 20:59:37.140 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:59:37.156 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 20:59:37.158 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 20:59:37.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-07-31 20:59:37.605 [info] > git status -z -uall [432ms]
2025-07-31 20:59:40.349 [info] > git config --get commit.template [18ms]
2025-07-31 20:59:40.350 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:59:40.366 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 20:59:40.367 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-07-31 20:59:40.408 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-07-31 20:59:40.688 [info] > git status -z -uall [305ms]
2025-07-31 20:59:42.640 [info] > git config --get commit.template [17ms]
2025-07-31 20:59:42.641 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 20:59:42.657 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 20:59:42.660 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-07-31 20:59:42.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-07-31 20:59:42.793 [info] > git status -z -uall [117ms]
2025-07-31 21:03:44.372 [info] > git config --get commit.template [195ms]
2025-07-31 21:03:44.375 [info] > git config --get commit.template [181ms]
2025-07-31 21:03:44.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-07-31 21:03:44.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [30ms]
2025-07-31 21:03:44.441 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 21:03:44.460 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 21:03:44.472 [info] > git rev-parse refs/remotes/origin/main [31ms]
2025-07-31 21:03:44.524 [info] > git rev-parse refs/remotes/origin/main [64ms]
2025-07-31 21:03:44.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [221ms]
2025-07-31 21:03:44.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [156ms]
2025-07-31 21:03:44.908 [info] > git status -z -uall [358ms]
2025-07-31 21:03:44.982 [info] > git status -z -uall [492ms]
2025-07-31 21:03:49.948 [info] > git config --get commit.template [18ms]
2025-07-31 21:03:49.949 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 21:03:49.966 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/refs/remotes/origin/main'
2025-07-31 21:03:49.968 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 21:03:50.014 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-07-31 21:03:50.031 [info] > git status -z -uall [46ms]
2025-07-31 21:08:46.665 [info] > git rev-parse --show-toplevel [2ms]
2025-07-31 21:08:46.692 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-07-31 21:08:46.737 [info] [Model][openRepository] Opened repository (path): /home/<USER>/algofactory/algo/instances/1009-algo-angel-100
2025-07-31 21:08:46.737 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/algofactory/algo/instances/1009-algo-angel-100
2025-07-31 21:08:46.774 [info] > git config --get commit.template [18ms]
2025-07-31 21:08:46.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 21:08:46.795 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-angel-100/.git/refs/remotes/origin/main'
2025-07-31 21:08:46.801 [info] > git rev-parse refs/remotes/origin/main [7ms]
2025-07-31 21:08:46.856 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-07-31 21:08:46.881 [info] > git status -z -uall [62ms]
2025-07-31 21:08:46.929 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-07-31 21:08:46.952 [info] > git config --get commit.template [24ms]
2025-07-31 21:08:46.953 [info] > git config --get --local branch.main.vscode-merge-base [3ms]
2025-07-31 21:08:46.953 [warning] [Git][config] git config failed: Failed to execute git
2025-07-31 21:08:46.992 [info] > git reflog main --grep-reflog=branch: Created from *. [21ms]
2025-07-31 21:08:47.021 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [32ms]
2025-07-31 21:08:47.022 [info] > git symbolic-ref --short refs/remotes/origin/HEAD [4ms]
2025-07-31 21:08:47.059 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-angel-100/.git/refs/remotes/origin/main'
2025-07-31 21:08:47.060 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [20ms]
2025-07-31 21:08:47.081 [info] > git rev-parse refs/remotes/origin/main [22ms]
2025-07-31 21:08:47.127 [info] > git config --add --local branch.main.vscode-merge-base origin/main [49ms]
2025-07-31 21:08:47.130 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms] (cancelled)
2025-07-31 21:08:47.165 [info] > git status -z -uall [67ms] (cancelled)
2025-07-31 21:08:47.166 [info] > git config --get commit.template [1ms]
2025-07-31 21:08:47.186 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 21:08:47.203 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-angel-100/.git/refs/remotes/origin/main'
2025-07-31 21:08:47.205 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 21:08:47.254 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-07-31 21:08:47.281 [info] > git status -z -uall [57ms]
2025-07-31 21:08:47.358 [info] > git check-ignore -v -z --stdin [20ms]
2025-07-31 21:08:47.358 [info] > git check-ignore -v -z --stdin [3ms]
2025-07-31 21:08:47.359 [info] > git merge-base refs/heads/main refs/remotes/origin/main [41ms]
2025-07-31 21:08:47.376 [info] > git merge-base refs/heads/main refs/remotes/origin/main [76ms]
2025-07-31 21:08:47.399 [info] > git diff --name-status -z --diff-filter=ADMR e1efe3408182bd32b1a65bbee0a9987dd2970708...refs/remotes/origin/main [23ms]
2025-07-31 21:08:47.400 [info] > git diff --name-status -z --diff-filter=ADMR e1efe3408182bd32b1a65bbee0a9987dd2970708...refs/remotes/origin/main [7ms]
2025-07-31 21:09:48.235 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/HEAD'
2025-07-31 21:09:48.280 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/config'
2025-07-31 21:09:48.368 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/config'
2025-07-31 21:09:48.387 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-98/.git/HEAD'
2025-07-31 21:09:48.501 [info] > git config --get commit.template [152ms]
2025-07-31 21:09:48.501 [info] > git config --get commit.template [173ms]
2025-07-31 21:09:48.576 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [36ms]
2025-07-31 21:09:48.577 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [57ms]
2025-07-31 21:09:48.597 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 21:09:48.616 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-angel-100/.git/refs/remotes/origin/main'
2025-07-31 21:09:48.618 [info] > git rev-parse refs/remotes/origin/main [20ms]
2025-07-31 21:09:48.660 [info] > git rev-parse refs/remotes/origin/main [44ms]
2025-07-31 21:09:48.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [47ms]
2025-07-31 21:09:48.721 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [19ms]
2025-07-31 21:09:49.046 [info] > git check-ignore -v -z --stdin [24ms]
2025-07-31 21:09:49.047 [info] > git check-ignore -v -z --stdin [4ms]
2025-07-31 21:09:49.287 [info] > git status -z -uall [650ms]
2025-07-31 21:09:49.287 [info] > git status -z -uall [606ms]
2025-07-31 21:09:54.329 [info] > git config --get commit.template [20ms]
2025-07-31 21:09:54.353 [info] > git config --get commit.template [25ms]
2025-07-31 21:09:54.375 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [23ms]
2025-07-31 21:09:54.626 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 21:09:54.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [256ms]
2025-07-31 21:09:54.658 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-angel-100/.git/refs/remotes/origin/main'
2025-07-31 21:09:54.659 [info] > git rev-parse refs/remotes/origin/main [33ms]
2025-07-31 21:09:54.763 [info] > git status -z -uall [78ms]
2025-07-31 21:09:54.763 [info] > git rev-parse refs/remotes/origin/main [105ms]
2025-07-31 21:09:54.848 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [122ms]
2025-07-31 21:09:54.861 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-07-31 21:09:54.900 [info] > git status -z -uall [108ms]
2025-07-31 21:12:53.197 [info] > git config --get commit.template [9ms]
2025-07-31 21:12:53.197 [info] > git config --get commit.template [29ms]
2025-07-31 21:12:53.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-07-31 21:12:53.261 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-angel-100/.git/refs/remotes/origin/main'
2025-07-31 21:12:53.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [23ms]
2025-07-31 21:12:53.282 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 21:12:53.283 [info] > git rev-parse refs/remotes/origin/main [23ms]
2025-07-31 21:12:53.328 [info] > git rev-parse refs/remotes/origin/main [46ms]
2025-07-31 21:12:53.373 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [47ms]
2025-07-31 21:12:53.379 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-07-31 21:12:53.697 [info] > git status -z -uall [392ms]
2025-07-31 21:12:53.766 [info] > git status -z -uall [417ms]
2025-07-31 21:12:58.739 [info] > git config --get commit.template [21ms]
2025-07-31 21:12:58.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-07-31 21:12:58.759 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-angel-100/.git/refs/remotes/origin/main'
2025-07-31 21:12:58.761 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 21:12:58.894 [info] > git status -z -uall [114ms]
2025-07-31 21:12:58.895 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [96ms]
2025-07-31 21:12:58.917 [info] > git config --get commit.template [94ms]
2025-07-31 21:12:58.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-07-31 21:12:58.938 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/algofactory/algo/instances/1009-algo-dhan-99/.git/refs/remotes/origin/main'
2025-07-31 21:12:58.940 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-07-31 21:12:58.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-07-31 21:12:59.009 [info] > git status -z -uall [49ms]
