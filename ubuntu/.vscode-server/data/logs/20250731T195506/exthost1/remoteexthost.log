2025-07-31 19:55:08.899 [info] Extension host with pid 178768 started
2025-07-31 19:55:08.933 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/88d3fe36c0f72fce85cf67e444be32c1/vscode.lock': Lock acquired.
2025-07-31 19:55:09.256 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-07-31 19:55:09.257 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-31 19:55:09.258 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:javascript'
2025-07-31 19:55:09.884 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-07-31 19:55:09.885 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-07-31 19:55:11.014 [info] Eager extensions activated
2025-07-31 19:55:11.015 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 19:55:11.023 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 19:55:12.319 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-07-31 19:55:22.134 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 19:55:24.118 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 19:55:37.563 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 19:55:56.357 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5CAnita%20Bhanushali%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at i8e.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:523:10721)
2025-07-31 20:00:05.082 [info] ExtensionService#_doActivateExtension vscode.npm, startup: false, activationEvent: 'onTerminalQuickFixRequest:ms-vscode.npm-command'
2025-07-31 20:00:11.482 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-07-31 20:00:11.482 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-07-31 20:00:11.483 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-07-31 20:12:14.282 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:70638)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at fy.acceptDocumentsAndEditorsDelta (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:121:11787)
    at fy.$acceptDocumentsAndEditorsDelta (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:121:10246)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:17:46.423 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:17:46.446 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:17:46.865 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:17:46.868 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at BW.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106726)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:17:47.537 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
2025-07-31 20:22:38.285 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:22:38.290 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:22:38.428 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:22:38.430 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at BW.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106726)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:25:55.972 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:25:55.981 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:25:56.249 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:25:56.252 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at BW.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106726)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:27:13.069 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:27:13.074 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:27:13.354 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:27:13.357 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at BW.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106726)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:27:47.087 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:27:47.108 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:27:47.702 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:27:47.704 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at BW.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106726)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 20:36:53.585 [error] Error: e is not iterable

TypeError: e is not iterable
    at _Z.setItems (/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1/dist/extension.js:401:21291)
    at zs._computeFn (/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1/dist/extension.js:401:21023)
    at zs._recompute (/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1/dist/extension.js:401:14747)
    at zs.get (/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1/dist/extension.js:401:14081)
    at zs.reportChanges (/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1/dist/extension.js:401:5845)
    at yZ.endUpdate (/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1/dist/extension.js:401:20808)
    at v9e (/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1/dist/extension.js:401:20527)
    at zs.recomputeInitiallyAndOnChange (/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1/dist/extension.js:401:6421)
    at F2.init (/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1/dist/extension.js:933:20532)
    at Timeout._onTimeout (/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1/dist/extension.js:366:12)
    at listOnTimeout (node:internal/timers:588:17)
    at processTimers (node:internal/timers:523:7)
2025-07-31 21:03:43.259 [info] ExtensionService#_doActivateExtension vscode.html-language-features, startup: false, activationEvent: 'onLanguage:html'
2025-07-31 21:06:02.376 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:02.446 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:02.904 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:02.912 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at BW.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106726)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:34.991 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:34.998 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:35.200 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:35.202 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at BW.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106726)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:58.696 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:58.704 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:58.990 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:06:58.992 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at BW.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106726)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:07:52.778 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:07:52.782 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:07:53.186 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:07:53.189 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at BW.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106726)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:08:12.318 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:08:12.322 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:08:12.501 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:08:12.504 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106924)
    at BW.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106726)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-31 21:11:17.781 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
    at BW.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107571)
    at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
    at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
    at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
    at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
    at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
    at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
    at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
    at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
    at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
    at file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
    at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
