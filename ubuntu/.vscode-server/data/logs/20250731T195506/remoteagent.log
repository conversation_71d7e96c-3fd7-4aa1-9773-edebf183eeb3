2025-07-31 19:55:06.713 [info] 




2025-07-31 19:55:06.713 [info] Extension host agent started.
2025-07-31 19:55:06.790 [info] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
2025-07-31 19:55:06.922 [info] [<unknown>][87bbd4ed][ManagementConnection] New connection established.
2025-07-31 19:55:06.930 [info] [<unknown>][a5776b84][ExtensionHostConnection] New connection established.
2025-07-31 19:55:06.966 [info] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
2025-07-31 19:55:07.263 [info] [<unknown>][a5776b84][ExtensionHostConnection] <178768> Launched Extension Host Process.
2025-07-31 19:55:12.078 [info] Getting Manifest... github.copilot
2025-07-31 19:55:12.089 [info] Getting Manifest... github.copilot-chat
2025-07-31 19:55:12.174 [info] Installing extension: github.copilot {"installPreReleaseVersion":false,"donotIncludePackAndDependencies":true,"donotVerifySignature":false,"context":{"clientTargetPlatform":"win32-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-29T03:00:23.339Z"}}
2025-07-31 19:55:12.175 [info] Installing the extension without checking dependencies and pack github.copilot
2025-07-31 19:55:12.260 [info] Installing extension: github.copilot-chat {"installPreReleaseVersion":false,"donotIncludePackAndDependencies":true,"donotVerifySignature":false,"context":{"clientTargetPlatform":"win32-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-29T03:00:23.339Z"}}
2025-07-31 19:55:12.266 [info] Installing the extension without checking dependencies and pack github.copilot-chat
2025-07-31 19:55:20.613 [info] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 6613ms.
2025-07-31 19:55:20.743 [info] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 6703ms.
2025-07-31 19:55:21.518 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1: github.copilot-chat
2025-07-31 19:55:21.569 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1
2025-07-31 19:55:21.618 [info] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-07-31 19:55:22.488 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.350.0: github.copilot
2025-07-31 19:55:22.518 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.350.0
2025-07-31 19:55:22.658 [info] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-07-31 19:55:26.038 [info] Getting Manifest... augment.vscode-augment
2025-07-31 19:55:26.139 [info] Installing extension: augment.vscode-augment {"installPreReleaseVersion":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"win32-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-29T03:00:23.339Z"}}
2025-07-31 19:55:30.864 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 3406ms.
2025-07-31 19:55:36.408 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.516.2: augment.vscode-augment
2025-07-31 19:55:36.837 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.516.2
2025-07-31 19:55:36.914 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-07-31 20:00:06.702 [info] New EH opened, aborting shutdown
2025-07-31 20:46:35.465 [warning] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
2025-07-31 21:08:25.464 [warning] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
