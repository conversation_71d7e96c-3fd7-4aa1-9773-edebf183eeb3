# Broker Connection and API Key Synchronization
- When brokers connect, the OpenAlgo-generated API key should be saved back to the AlgoFactory database's algo_api_key field. This synchronization is currently not working for Dhan brokers.
- OpenAlgo instances have direct database access in the algo folder, and during Dhan broker connection the generated API key should be saved back to the AlgoFactory database, but this synchronization is not working properly.

# Manual Broker Connections
- For manual broker connections, use the same system/approach as OpenAlgo instances (found in the algo folder) but maintain our own interface - don't change the underlying OpenAlgo connection mechanism.
`