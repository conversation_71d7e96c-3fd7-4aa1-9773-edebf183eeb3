{"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/test_manual_openalgo_connection.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nTest script for manual OpenAlgo-style broker connections\n\"\"\"\n\nimport sys\nimport os\nimport requests\nimport json\n\n# Add the project root to Python path\nsys.path.append('/home/<USER>/algofactory')\n\nfrom scripts.automation.broker_automation import Broker<PERSON>uto<PERSON>\n\ndef test_angel_manual_connection():\n    \"\"\"Test Angel manual connection\"\"\"\n    print(\"🧪 Testing Angel Manual OpenAlgo-Style Connection\")\n    print(\"=\" * 60)\n    \n    # Initialize broker automation\n    broker_automation = BrokerAutomation()\n    \n    # Test setup ID (replace with actual setup ID)\n    setup_id = input(\"Enter Angel setup ID to test: \").strip()\n    if not setup_id:\n        print(\"❌ Setup ID is required\")\n        return\n    \n    try:\n        setup_id = int(setup_id)\n    except ValueError:\n        print(\"❌ Setup ID must be a number\")\n        return\n    \n    # Get manual credentials\n    print(\"\\n📋 Enter Angel One credentials:\")\n    clientid = input(\"Client ID: \").strip()\n    pin = input(\"Trading PIN: \").strip()\n    totp = input(\"TOTP (6 digits): \").strip()\n    \n    if not all([clientid, pin, totp]):\n        print(\"❌ All credentials are required\")\n        return\n    \n    if len(totp) != 6 or not totp.isdigit():\n        print(\"❌ TOTP must be exactly 6 digits\")\n        return\n    \n    manual_credentials = {\n        'clientid': clientid,\n        'pin': pin,\n        'totp': totp\n    }\n    \n    print(f\"\\n🔗 Testing manual connection for setup {setup_id}...\")\n    \n    # Test the connection\n    result = broker_automation.connect_broker_manual_openalgo_style(setup_id, manual_credentials)\n    \n    print(f\"\\n📊 Result:\")\n    print(f\"Status: {result.get('status')}\")\n    print(f\"Message: {result.get('message')}\")\n    \n    if result.get('status') == 'success':\n        print(\"✅ Angel manual connection test PASSED!\")\n        data = result.get('data', {})\n        print(f\"Connection URL: {data.get('url')}\")\n        print(f\"Connection Method: {data.get('connection_method')}\")\n    else:\n        print(\"❌ Angel manual connection test FAILED!\")\n\ndef test_dhan_manual_connection():\n    \"\"\"Test Dhan manual connection\"\"\"\n    print(\"🧪 Testing Dhan Manual OpenAlgo-Style Connection\")\n    print(\"=\" * 60)\n    \n    # Initialize broker automation\n    broker_automation = BrokerAutomation()\n    \n    # Test setup ID (replace with actual setup ID)\n    setup_id = input(\"Enter Dhan setup ID to test: \").strip()\n    if not setup_id:\n        print(\"❌ Setup ID is required\")\n        return\n    \n    try:\n        setup_id = int(setup_id)\n    except ValueError:\n        print(\"❌ Setup ID must be a number\")\n        return\n    \n    print(f\"\\n🔗 Testing manual connection for setup {setup_id}...\")\n    \n    # Test the connection (no credentials needed for Dhan)\n    result = broker_automation.connect_broker_manual_openalgo_style(setup_id, None)\n    \n    print(f\"\\n📊 Result:\")\n    print(f\"Status: {result.get('status')}\")\n    print(f\"Message: {result.get('message')}\")\n    \n    if result.get('status') == 'success':\n        print(\"✅ Dhan manual connection test PASSED!\")\n        data = result.get('data', {})\n        print(f\"Connection URL: {data.get('url')}\")\n        print(f\"Connection Method: {data.get('connection_method')}\")\n    else:\n        print(\"❌ Dhan manual connection test FAILED!\")\n\ndef test_api_endpoint():\n    \"\"\"Test the API endpoint\"\"\"\n    print(\"🧪 Testing API Endpoint\")\n    print(\"=\" * 60)\n    \n    # Test setup ID\n    setup_id = input(\"Enter setup ID to test API: \").strip()\n    if not setup_id:\n        print(\"❌ Setup ID is required\")\n        return\n    \n    broker_type = input(\"Enter broker type (angel/dhan): \").strip().lower()\n    if broker_type not in ['angel', 'dhan']:\n        print(\"❌ Broker type must be 'angel' or 'dhan'\")\n        return\n    \n    # Prepare request data\n    if broker_type == 'angel':\n        print(\"\\n📋 Enter Angel One credentials:\")\n        clientid = input(\"Client ID: \").strip()\n        pin = input(\"Trading PIN: \").strip()\n        totp = input(\"TOTP (6 digits): \").strip()\n        \n        request_data = {\n            'clientid': clientid,\n            'pin': pin,\n            'totp': totp\n        }\n    else:\n        request_data = {}  # Dhan doesn't need credentials\n    \n    # Make API request\n    url = f\"http://localhost:8000/api/broker-setup/{setup_id}/connect-manual-openalgo\"\n    \n    print(f\"\\n🌐 Making API request to: {url}\")\n    print(f\"📤 Request data: {request_data}\")\n    \n    try:\n        response = requests.post(url, json=request_data, timeout=30)\n        \n        print(f\"\\n📡 Response:\")\n        print(f\"Status Code: {response.status_code}\")\n        \n        try:\n            result = response.json()\n            print(f\"Response Data: {json.dumps(result, indent=2)}\")\n            \n            if response.status_code == 200 and result.get('success'):\n                print(\"✅ API endpoint test PASSED!\")\n            else:\n                print(\"❌ API endpoint test FAILED!\")\n                \n        except json.JSONDecodeError:\n            print(f\"Response Text: {response.text}\")\n            \n    except requests.exceptions.RequestException as e:\n        print(f\"❌ API request failed: {e}\")\n\ndef main():\n    \"\"\"Main test function\"\"\"\n    print(\"🧪 Manual OpenAlgo-Style Connection Test Suite\")\n    print(\"=\" * 60)\n    \n    while True:\n        print(\"\\nSelect test to run:\")\n        print(\"1. Test Angel Manual Connection (Direct)\")\n        print(\"2. Test Dhan Manual Connection (Direct)\")\n        print(\"3. Test API Endpoint\")\n        print(\"4. Exit\")\n        \n        choice = input(\"\\nEnter choice (1-4): \").strip()\n        \n        if choice == '1':\n            test_angel_manual_connection()\n        elif choice == '2':\n            test_dhan_manual_connection()\n        elif choice == '3':\n            test_api_endpoint()\n        elif choice == '4':\n            print(\"👋 Goodbye!\")\n            break\n        else:\n            print(\"❌ Invalid choice. Please enter 1-4.\")\n        \n        input(\"\\nPress Enter to continue...\")\n\nif __name__ == \"__main__\":\n    main()\n"}