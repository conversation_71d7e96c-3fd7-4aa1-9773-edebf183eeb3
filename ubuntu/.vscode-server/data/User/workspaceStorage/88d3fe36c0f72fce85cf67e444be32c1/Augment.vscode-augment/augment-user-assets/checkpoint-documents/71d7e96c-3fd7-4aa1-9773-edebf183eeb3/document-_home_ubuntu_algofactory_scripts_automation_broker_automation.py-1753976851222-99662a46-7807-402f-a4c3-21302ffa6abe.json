{"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nBroker Automation System for AlgoFactory\nHandles OpenAlgo instance creation, setup, and broker authentication\n\"\"\"\n\nimport os\nimport sys\nimport json\nimport time\nimport shutil\nimport sqlite3\nimport requests\nimport subprocess\nimport re\nimport logging\nfrom pathlib import Path\nfrom datetime import datetime\nimport pyotp\n\n# Setup logger\nlogger = logging.getLogger(__name__)\n\nclass BrokerAutomation:\n    def __init__(self):\n        self.base_dir = Path(\"/home/<USER>/algofactory\")\n        self.algo_dir = self.base_dir / \"algo\"\n        self.instances_dir = self.algo_dir / \"instances\"\n        self.template_dir = self.algo_dir / \"template\"\n        self.db_path = self.base_dir / \"database/algofactory.db\"\n\n        # Ensure directories exist\n        self.instances_dir.mkdir(parents=True, exist_ok=True)\n        self.template_dir.mkdir(parents=True, exist_ok=True)\n\n        # Port allocation (starting from 5001)\n        self.base_port = 5001\n\n        # Environment for subprocess calls - include virtual environment\n        import os\n        self.subprocess_env = os.environ.copy()\n        self.subprocess_env.update({\n            'PATH': '/home/<USER>/algofactory/env/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/bin',\n            'HOME': '/home/<USER>',\n            'USER': 'ubuntu',\n            'VIRTUAL_ENV': '/home/<USER>/algofactory/env'\n        })\n\n    def run_systemctl(self, action, service_name, timeout=30):\n        \"\"\"Helper method to run systemctl commands with proper environment\"\"\"\n        try:\n            # Use full paths and proper environment for API compatibility\n            cmd = f\"/usr/bin/sudo /usr/bin/systemctl {action} {service_name}\"\n            result = subprocess.run(\n                cmd,\n                shell=True,\n                capture_output=True,\n                text=True,\n                timeout=timeout,\n                env=self.subprocess_env,\n                cwd=str(self.base_dir)\n            )\n            return result\n        except Exception as e:\n            print(f\"Error running systemctl {action} {service_name}: {e}\")\n            return None\n        \n    def get_ist_timestamp(self):\n        \"\"\"Get IST timestamp\"\"\"\n        from datetime import datetime, timezone, timedelta\n        ist = timezone(timedelta(hours=5, minutes=30))\n        return datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S')\n\n    def get_all_broker_setups(self):\n        \"\"\"Get all broker setups from database\"\"\"\n        try:\n            conn = sqlite3.connect(self.db_path)\n            conn.row_factory = sqlite3.Row\n            cursor = conn.cursor()\n\n            # Get all broker setups with user information\n            cursor.execute(\"\"\"\n                SELECT\n                    ub.id,\n                    ub.username,\n                    ub.broker_name,\n                    ub.setup_name,\n                    ub.instance_name,\n                    ub.broker_client_id,\n                    ub.setup_status,\n                    ub.connection_status,\n                    ub.http_port,\n                    ub.websocket_port,\n                    ub.zmq_port,\n                    ub.detailed_status,\n                    ub.created_at,\n                    u.admin_id,\n                    u.full_name as user_full_name\n                FROM user_brokers ub\n                LEFT JOIN users u ON ub.username = u.username\n                ORDER BY ub.created_at DESC\n            \"\"\")\n\n            setups = []\n            for row in cursor.fetchall():\n                setup = dict(row)\n                # Parse detailed_status if it's JSON\n                try:\n                    if setup['detailed_status']:\n                        setup['detailed_status'] = json.loads(setup['detailed_status'])\n                except:\n                    setup['detailed_status'] = {}\n\n                setups.append(setup)\n\n            conn.close()\n            return setups\n\n        except Exception as e:\n            print(f\"❌ Error getting broker setups: {e}\")\n            return []\n\n    def update_all_instances(self):\n        \"\"\"Update all OpenAlgo instances with git pull and requirements - for morning updates\"\"\"\n        try:\n            print(f\"🌅 Morning update: Updating all OpenAlgo instances...\")\n\n            updated_instances = []\n            failed_instances = []\n\n            # Get all instances\n            for instance_dir in self.instances_dir.iterdir():\n                if instance_dir.is_dir() and not instance_dir.name.startswith('.'):\n                    try:\n                        print(f\"🔄 Updating instance: {instance_dir.name}\")\n\n                        # Git pull\n                        git_result = subprocess.run([\n                            '/usr/bin/git', 'pull', 'origin', 'main'\n                        ], cwd=instance_dir, capture_output=True, text=True)\n\n                        # Update requirements\n                        requirements_file = instance_dir / \"requirements.txt\"\n                        if requirements_file.exists():\n                            pip_result = subprocess.run([\n                                'pip', 'install', '-r', str(requirements_file), '--upgrade'\n                            ], capture_output=True, text=True)\n\n                        updated_instances.append(instance_dir.name)\n                        print(f\"✅ Updated: {instance_dir.name}\")\n\n                    except Exception as e:\n                        failed_instances.append(f\"{instance_dir.name}: {str(e)}\")\n                        print(f\"❌ Failed to update {instance_dir.name}: {e}\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Morning update completed: {len(updated_instances)} updated, {len(failed_instances)} failed\",\n                \"updated\": updated_instances,\n                \"failed\": failed_instances,\n                \"timestamp\": self.get_ist_timestamp()\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Morning update failed: {str(e)}\"\n            }\n\n    def get_instance_name(self, setup_data):\n        \"\"\"Generate consistent multi-user instance name: username-algo-broker-setupid\"\"\"\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        return f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n\n    def get_service_name(self, setup_data):\n        \"\"\"Generate consistent multi-user service name: username-algo-broker-setupid\"\"\"\n        return self.get_instance_name(setup_data)\n\n    def create_backup(self, setup_id):\n        \"\"\"Create backup - alias for backup_instance method\"\"\"\n        return self.backup_instance(setup_id)\n\n    def delete_systemd_service(self, service_name):\n        \"\"\"Comprehensive systemd service deletion\"\"\"\n        try:\n            print(f\"🛑 Deleting systemd service: {service_name}\")\n\n            # Step 1: Stop the service\n            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'stop', service_name],\n                                  capture_output=True, text=True, check=False)\n            if result.returncode == 0:\n                print(f\"✅ Service {service_name} stopped\")\n            else:\n                print(f\"⚠️ Service {service_name} stop: {result.stderr.strip()}\")\n\n            # Step 2: Disable the service\n            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'disable', service_name],\n                                  capture_output=True, text=True, check=False)\n            if result.returncode == 0:\n                print(f\"✅ Service {service_name} disabled\")\n            else:\n                print(f\"⚠️ Service {service_name} disable: {result.stderr.strip()}\")\n\n            # Step 3: Delete the service file\n            service_file = f\"/etc/systemd/system/{service_name}.service\"\n            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/rm', '-f', service_file],\n                                  capture_output=True, text=True, check=False)\n            if result.returncode == 0:\n                print(f\"✅ Service file deleted: {service_file}\")\n            else:\n                print(f\"⚠️ Error deleting service file: {result.stderr.strip()}\")\n\n            # Step 4: Reload systemd daemon\n            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'daemon-reload'],\n                                  capture_output=True, text=True, check=False)\n            if result.returncode == 0:\n                print(f\"✅ Systemd daemon reloaded\")\n            else:\n                print(f\"⚠️ Daemon reload warning: {result.stderr.strip()}\")\n\n            # Step 5: Reset failed state (if any)\n            subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'reset-failed', service_name],\n                          capture_output=True, text=True, check=False)\n\n            return {\"status\": \"success\", \"message\": f\"Service {service_name} completely removed\"}\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to delete service {service_name}: {str(e)}\"}\n\n    def get_all_user_services(self, username=None):\n        \"\"\"Get all systemd services for a specific user or all algo services\"\"\"\n        try:\n            # Get all systemd services\n            result = subprocess.run(['sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'],\n                                  capture_output=True, text=True, check=False)\n\n            services = []\n            if result.returncode == 0:\n                lines = result.stdout.split('\\n')\n                for line in lines:\n                    if 'algo' in line.lower():\n                        parts = line.split()\n                        if len(parts) >= 4:\n                            service_name = parts[0]\n                            # Filter by username if provided\n                            if username:\n                                if service_name.startswith(f\"{username}-algo-\"):\n                                    services.append({\n                                        \"name\": service_name,\n                                        \"status\": parts[2],\n                                        \"active\": parts[1],\n                                        \"description\": ' '.join(parts[4:]) if len(parts) > 4 else \"\"\n                                    })\n                            else:\n                                if '-algo-' in service_name:\n                                    services.append({\n                                        \"name\": service_name,\n                                        \"status\": parts[2],\n                                        \"active\": parts[1],\n                                        \"description\": ' '.join(parts[4:]) if len(parts) > 4 else \"\"\n                                    })\n\n            return {\"status\": \"success\", \"services\": services, \"count\": len(services)}\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to get services: {str(e)}\"}\n\n    def stop_algo(self, setup_id):\n        \"\"\"Stop and disable OpenAlgo instance\"\"\"\n        try:\n            logger.info(f\"⏹️ Stopping and disabling OpenAlgo for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": f\"Setup {setup_id} not found\"}\n\n            instance_name = setup_data.get('instance_name')\n            service_name = instance_name  # Use instance name directly as service name\n\n            # Stop systemd service\n            stop_result = self.run_systemctl('stop', service_name)\n\n            # Disable systemd service to prevent auto-restart\n            disable_result = self.run_systemctl('disable', service_name)\n\n            if stop_result and stop_result.returncode == 0:\n                logger.info(f\"✅ Algo service stopped: {service_name}\")\n\n                if disable_result and disable_result.returncode == 0:\n                    logger.info(f\"✅ Algo service disabled: {service_name}\")\n                    message = f\"Algo stopped and disabled successfully\"\n                else:\n                    error_msg = disable_result.stderr if disable_result else \"disable command failed\"\n                    logger.warning(f\"⚠️ Service stopped but disable failed: {error_msg}\")\n                    message = f\"Algo stopped but disable failed\"\n\n                # Update database status\n                self.update_setup_status(setup_id, 'stopped', 'not_connected')\n\n                return {\n                    \"status\": \"success\",\n                    \"message\": message,\n                    \"service_name\": service_name,\n                    \"stopped\": True,\n                    \"disabled\": disable_result.returncode == 0\n                }\n            else:\n                error_msg = stop_result.stderr if stop_result else \"stop command failed\"\n                logger.error(f\"❌ Failed to stop service: {error_msg}\")\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to stop service: {error_msg}\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error stopping Algo: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Failed to stop Algo: {str(e)}\"}\n\n    def get_logs(self, setup_id):\n        \"\"\"Get OpenAlgo logs\"\"\"\n        try:\n            logger.info(f\"📄 Getting logs for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": f\"Setup {setup_id} not found\"}\n\n            instance_name = setup_data.get('instance_name')\n            service_name = instance_name  # Use the instance name directly as service name\n\n            # Get systemd service logs\n            logs_cmd = f\"sudo journalctl -u {service_name} --no-pager -n 50\"\n            result = subprocess.run(logs_cmd, shell=True, capture_output=True, text=True)\n\n            if result.returncode == 0:\n                logs = result.stdout\n                if not logs.strip():\n                    logs = \"No logs available for this service.\"\n\n                logger.info(f\"✅ Logs retrieved for setup: {setup_id}\")\n                return {\n                    \"status\": \"success\",\n                    \"message\": \"Logs retrieved successfully\",\n                    \"logs\": logs,\n                    \"service_name\": service_name\n                }\n            else:\n                logger.error(f\"❌ Failed to get logs: {result.stderr}\")\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to get logs: {result.stderr}\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error getting logs: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Failed to get logs: {str(e)}\"}\n\n    def update_setup_status(self, setup_id, setup_status, connection_status):\n        \"\"\"Update setup status in database\"\"\"\n        try:\n            import sqlite3\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n                cursor.execute(\"\"\"\n                    UPDATE user_brokers\n                    SET setup_status = ?, connection_status = ?\n                    WHERE id = ?\n                \"\"\", (setup_status, connection_status, setup_id))\n                conn.commit()\n\n            logger.info(f\"✅ Updated status for setup {setup_id}: {setup_status}/{connection_status}\")\n            return True\n\n        except Exception as e:\n            logger.error(f\"❌ Error updating setup status: {e}\")\n            return False\n\n    def update_detailed_status(self, setup_id, step, status):\n        \"\"\"Update detailed status in database\"\"\"\n        try:\n            import sqlite3\n            import json\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n\n                # Get current detailed status\n                cursor.execute(\"SELECT detailed_status FROM user_brokers WHERE id = ?\", (setup_id,))\n                row = cursor.fetchone()\n\n                if row and row[0]:\n                    try:\n                        detailed_status = json.loads(row[0])\n                    except:\n                        detailed_status = {}\n                else:\n                    detailed_status = {}\n\n                # Update the specific step\n                detailed_status[step] = status\n\n                # Save back to database\n                cursor.execute(\"\"\"\n                    UPDATE user_brokers\n                    SET detailed_status = ?\n                    WHERE id = ?\n                \"\"\", (json.dumps(detailed_status), setup_id))\n                conn.commit()\n\n            logger.info(f\"✅ Updated detailed status for setup {setup_id}: {step} = {status}\")\n            return True\n\n        except Exception as e:\n            logger.error(f\"❌ Error updating detailed status: {e}\")\n            return False\n\n    def fast_auto_connect(self, setup_id):\n        \"\"\"ULTRA FAST auto connection - get auth token only\"\"\"\n        try:\n            logger.info(f\"🚀 ULTRA FAST auto connection for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"success\": False, \"message\": f\"Setup {setup_id} not found\"}\n\n            # Get OpenAlgo instance details\n            http_port = setup_data.get('http_port')\n            base_url = f\"http://127.0.0.1:{http_port}\"\n\n            # FASTEST CONNECTION - Direct API calls only\n            try:\n                import requests\n\n                # Step 1: Quick register (don't wait for response)\n                try:\n                    register_data = {\n                        \"username\": setup_data.get('username'),\n                        \"password\": setup_data.get('username')\n                    }\n                    requests.post(f\"{base_url}/register\", json=register_data, timeout=5)\n                except:\n                    pass  # User might already exist\n\n                # DEPRECATED: This method returns fake responses\n                logger.warning(f\"⚠️ fast_auto_connect is deprecated and returns fake data\")\n                return {\n                    \"success\": False,\n                    \"message\": \"fast_auto_connect is deprecated. Use step_3_register + step_4_connect_broker for real connections.\"\n                }\n\n            except Exception as e:\n                logger.error(f\"Auth error: {e}\")\n                return {\n                    \"success\": False,\n                    \"message\": f\"Connection failed: {str(e)}\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error in ultra fast connection: {e}\")\n            return {\"success\": False, \"message\": f\"Ultra fast connection failed: {str(e)}\"}\n\n    def fast_manual_connect(self, setup_id, trading_pin, totp_code=None):\n        \"\"\"Fast manual connection - simplified approach\"\"\"\n        try:\n            logger.info(f\"🔗 Fast manual connection for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"success\": False, \"message\": f\"Setup {setup_id} not found\"}\n\n            # Get OpenAlgo instance details\n            http_port = setup_data.get('http_port')\n\n            # Quick service check - don't block on health check\n            try:\n                import subprocess\n                # Just check if port is open, don't wait for HTTP response\n                check_cmd = f\"ss -tuln | grep :{http_port}\"\n                result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=3)\n\n                if result.returncode != 0:\n                    return {\"success\": False, \"message\": f\"Algo service not running on port {http_port}\"}\n\n            except Exception as e:\n                # If check fails, continue anyway - service might be starting\n                logger.warning(f\"Service check failed, continuing anyway: {e}\")\n\n            # Temporarily update trading pin in database for this connection\n            original_pin = setup_data.get('trading_pin')\n            try:\n                # Update with user-provided PIN\n                import sqlite3\n                db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n                with sqlite3.connect(db_path) as conn:\n                    cursor = conn.cursor()\n                    cursor.execute(\"\"\"\n                        UPDATE user_brokers\n                        SET trading_pin = ?\n                        WHERE id = ?\n                    \"\"\", (trading_pin, setup_id))\n                    conn.commit()\n\n                    # Ultra-fast manual connection\n                logger.info(f\"🔗 Manual connection with PIN: {trading_pin}\")\n\n                # ULTRA FAST manual connection - Direct API call\n                import requests\n                base_url = f\"http://127.0.0.1:{http_port}\"\n\n                # Step 1: Quick register (don't wait for response)\n                try:\n                    register_data = {\n                        \"username\": setup_data.get('username'),\n                        \"password\": setup_data.get('username')\n                    }\n                    requests.post(f\"{base_url}/register\", json=register_data, timeout=5)\n                except:\n                    pass  # User might already exist\n\n                # DEPRECATED: This method returns fake responses\n                logger.warning(f\"⚠️ fast_manual_connect is deprecated and returns fake data\")\n                return {\n                    \"success\": False,\n                    \"message\": \"fast_manual_connect is deprecated. Use step_3_register + step_4_connect_broker for real connections.\"\n                }\n\n            finally:\n                # Restore original PIN\n                try:\n                    with sqlite3.connect(db_path) as conn:\n                        cursor = conn.cursor()\n                        cursor.execute(\"\"\"\n                            UPDATE user_brokers\n                            SET trading_pin = ?\n                            WHERE id = ?\n                        \"\"\", (original_pin, setup_id))\n                        conn.commit()\n                except Exception as e:\n                    logger.error(f\"Failed to restore original PIN: {e}\")\n\n        except Exception as e:\n            logger.error(f\"❌ Error in fast manual connection: {e}\")\n            return {\"success\": False, \"message\": f\"Fast manual connection failed: {str(e)}\"}\n\n    def manual_broker_connect(self, setup_id, trading_pin, totp_code=None):\n        \"\"\"Manual broker connection with user-provided credentials\"\"\"\n        try:\n            logger.info(f\"🔗 Manual broker connection for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"success\": False, \"message\": f\"Setup {setup_id} not found\"}\n\n            # Update status to connecting\n            self.update_setup_status(setup_id, 'running', 'connecting')\n\n            # For now, simulate manual connection process\n            # In the future, this will redirect to broker login page\n\n            # Step 3: Register user first\n            register_result = self.step_3_register(setup_id)\n\n            if register_result.get('status') != 'success':\n                self.update_setup_status(setup_id, 'running', 'registration_failed')\n                return {\n                    \"success\": False,\n                    \"message\": f\"Registration failed: {register_result.get('message')}\"\n                }\n\n            # Step 4: Connect broker with manual credentials\n            connect_result = self.step_4_connect_broker(setup_id)\n\n            if connect_result.get('status') == 'success':\n                logger.info(f\"✅ Manual broker connection successful for setup: {setup_id}\")\n\n                # Update status to connected\n                self.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Manual broker connection successful\",\n                    \"data\": connect_result\n                }\n            else:\n                logger.error(f\"❌ Manual broker connection failed for setup: {setup_id}\")\n\n                # Update status to connection failed\n                self.update_setup_status(setup_id, 'running', 'connection_failed')\n\n                return {\n                    \"success\": False,\n                    \"message\": connect_result.get('message', 'Manual connection failed')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error in manual broker connection: {e}\")\n\n            # Update status to error\n            self.update_setup_status(setup_id, 'running', 'error')\n\n            return {\n                \"success\": False,\n                \"message\": f\"Manual connection failed: {str(e)}\"\n            }\n\n    def delete_setup(self, setup_id):\n        \"\"\"Delete broker setup (alias for complete_delete_setup)\"\"\"\n        return self.complete_delete_setup(setup_id)\n\n    def complete_delete_setup(self, setup_id):\n        \"\"\"Complete deletion of broker setup including:\n        - Stop systemd service\n        - Delete service file\n        - Delete instance folder\n        - Create backup\n        - Clean database entry\n        \"\"\"\n        try:\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            instance_name = self.get_instance_name(setup_data)\n            service_name = self.get_service_name(setup_data)\n            instance_path = self.instances_dir / instance_name\n\n            print(f\"🗑️ Starting complete deletion of setup: {setup_data['setup_name']}\")\n            print(f\"   Instance: {instance_name}\")\n            print(f\"   Service: {service_name}\")\n\n            # Step 1: Create backup before deletion\n            backup_result = self.create_backup(setup_id)\n            if backup_result.get('status') == 'success':\n                print(f\"✅ Backup created: {backup_result['data']['backup_file']}\")\n            else:\n                print(f\"⚠️ Backup failed: {backup_result.get('message', 'Unknown error')}\")\n\n            # Step 2: Delete systemd service completely\n            print(f\"🛑 Deleting systemd service: {service_name}\")\n            service_delete_result = self.delete_systemd_service(service_name)\n            if service_delete_result.get('status') == 'success':\n                print(f\"✅ {service_delete_result['message']}\")\n            else:\n                print(f\"⚠️ Service deletion warning: {service_delete_result.get('message', 'Unknown error')}\")\n\n            # Step 4: Kill any remaining processes\n            try:\n                # Kill any python processes running from this instance\n                subprocess.run(['/usr/bin/sudo', '/usr/bin/pkill', '-f', f'python.*{instance_name}'],\n                             check=False, capture_output=True)\n\n                # Kill any processes using the instance port\n                port = 5000 + setup_id\n                port_pids = subprocess.run(['/usr/bin/sudo', '/usr/bin/lsof', '-ti', f':{port}'],\n                                         capture_output=True, text=True, check=False)\n                if port_pids.stdout.strip():\n                    for pid in port_pids.stdout.strip().split('\\n'):\n                        if pid.strip():\n                            subprocess.run(['/usr/bin/sudo', '/usr/bin/kill', '-9', pid.strip()],\n                                         check=False, capture_output=True)\n                print(f\"✅ All processes for {instance_name} terminated\")\n            except Exception as e:\n                print(f\"⚠️ Error killing processes: {e}\")\n\n            # Step 5: Delete instance folder\n            if instance_path.exists():\n                try:\n                    shutil.rmtree(instance_path)\n                    print(f\"✅ Instance folder deleted: {instance_path}\")\n                except Exception as e:\n                    print(f\"⚠️ Error deleting instance folder: {e}\")\n            else:\n                print(f\"ℹ️ Instance folder not found: {instance_path}\")\n\n            # Step 6: Completely remove database entry\n            try:\n                import sqlite3\n                conn = sqlite3.connect(self.db_path)\n                cursor = conn.cursor()\n\n                # Actually delete the entry for clean system\n                cursor.execute(\"DELETE FROM user_brokers WHERE id = ?\", (setup_id,))\n\n                conn.commit()\n                conn.close()\n                print(f\"✅ Database entry completely removed\")\n            except Exception as e:\n                print(f\"⚠️ Error deleting from database: {e}\")\n\n            # Step 7: Clean up any remaining systemd files\n            try:\n                service_file = f\"/etc/systemd/system/{service_name}.service\"\n                if os.path.exists(service_file):\n                    subprocess.run(['/usr/bin/sudo', '/usr/bin/rm', '-f', service_file],\n                                 check=False, capture_output=True)\n                    print(f\"✅ Systemd service file removed: {service_file}\")\n\n                # Reload systemd daemon\n                subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'daemon-reload'],\n                             check=False, capture_output=True)\n                print(f\"✅ Systemd daemon reloaded\")\n            except Exception as e:\n                print(f\"⚠️ Error cleaning systemd files: {e}\")\n\n            # Step 8: Final cleanup - remove any temp files\n            try:\n                temp_patterns = [\n                    f\"/tmp/*{instance_name}*\",\n                    f\"/tmp/*{setup_id}*\"\n                ]\n                for pattern in temp_patterns:\n                    subprocess.run(['/usr/bin/sudo', '/usr/bin/rm', '-rf'] + [pattern],\n                                 shell=True, check=False, capture_output=True)\n                print(f\"✅ Temporary files cleaned\")\n            except Exception as e:\n                print(f\"⚠️ Error cleaning temp files: {e}\")\n\n            print(f\"🎉 Complete deletion successful for setup: {setup_data['setup_name']}\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Setup '{setup_data['setup_name']}' completely deleted\",\n                \"data\": {\n                    \"setup_id\": setup_id,\n                    \"instance_name\": instance_name,\n                    \"service_name\": service_name,\n                    \"backup_created\": backup_result.get('status') == 'success'\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to delete setup: {str(e)}\"}\n\n    def get_next_available_port(self):\n        \"\"\"Get next available port for OpenAlgo instance\"\"\"\n        used_ports = []\n        \n        # Check existing instances\n        if self.instances_dir.exists():\n            for instance_dir in self.instances_dir.iterdir():\n                if instance_dir.is_dir():\n                    env_file = instance_dir / \".env\"\n                    if env_file.exists():\n                        with open(env_file, 'r') as f:\n                            content = f.read()\n                            for line in content.split('\\n'):\n                                if line.startswith('PORT='):\n                                    port = int(line.split('=')[1])\n                                    used_ports.append(port)\n        \n        # Find next available port\n        port = self.base_port\n        while port in used_ports:\n            port += 1\n        \n        return port\n\n    def get_next_instance_number(self, username, broker_name):\n        \"\"\"Get the next available instance number for user-broker combination\"\"\"\n\n        try:\n            import sqlite3\n\n            # Connect to our database\n            conn = sqlite3.connect(self.db_path)\n            cursor = conn.cursor()\n\n            # Get highest instance number for this user-broker\n            cursor.execute(\"\"\"\n                SELECT MAX(instance_number) FROM user_brokers\n                WHERE username = ? AND broker_name = ?\n            \"\"\", (username, broker_name))\n\n            result = cursor.fetchone()\n            max_instance = result[0] if result[0] is not None else 0\n\n            conn.close()\n\n            return max_instance + 1\n\n        except Exception as e:\n            print(f\"⚠️ Failed to get next instance number: {e}\")\n            return 1  # Default to 1 if error\n\n    def create_instance(self, setup_id):\n        \"\"\"Complete instance creation - folder, clone, configure\"\"\"\n\n        try:\n            # Get setup details with user information\n            setup = self.get_setup_data_with_user(setup_id)\n            if not setup:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            # Get next instance number if not set\n            if not setup.get('instance_number'):\n                instance_number = self.get_next_instance_number(setup.get('username', '1000'), setup['broker_name'])\n            else:\n                instance_number = setup['instance_number']\n\n            # Create instance name: username-broker-instance format\n            instance_name = f\"{setup.get('username', '1000')}-{setup['broker_name']}-{instance_number}\"\n            instance_path = self.instances_dir / instance_name\n\n            print(f\"🔧 Creating instance: {instance_name}\")\n\n            # Step 1: Create instance folder\n            if instance_path.exists():\n                print(f\"📁 Instance folder already exists: {instance_path}\")\n            else:\n                instance_path.mkdir(parents=True, exist_ok=True)\n                print(f\"✅ Created instance folder: {instance_path}\")\n\n            # Step 2: Clone OpenAlgo if not already present\n            if not (instance_path / \"app.py\").exists():\n                print(f\"📥 Cloning OpenAlgo...\")\n                clone_success = self.clone_openalgo_source(instance_path)\n                if not clone_success:\n                    return {\"status\": \"error\", \"message\": \"Failed to clone OpenAlgo\"}\n            else:\n                print(f\"✅ OpenAlgo already present in instance\")\n\n            # Step 3: Configure .env file\n            print(f\"⚙️ Configuring environment...\")\n            config_success = self.configure_env_file(instance_path, setup)\n            if not config_success:\n                return {\"status\": \"error\", \"message\": \"Failed to configure environment\"}\n\n            print(f\"✅ Instance setup completed: {instance_name}\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": \"Instance created successfully\",\n                \"instance_name\": instance_name,\n                \"instance_path\": str(instance_path)\n            }\n\n        except Exception as e:\n            print(f\"❌ Instance creation failed: {e}\")\n            return {\"status\": \"error\", \"message\": str(e)}\n\n    def create_instance_folder(self, setup_id, broker_name, setup_name):\n        \"\"\"Create OpenAlgo instance folder for broker setup\"\"\"\n        \n        # Get user data for multi-user naming\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Create unique instance name with multi-user format: username-algo-broker-setupid\n        instance_name = self.get_instance_name(setup_data)\n        instance_path = self.instances_dir / instance_name\n        \n        # Remove existing instance if it exists\n        if instance_path.exists():\n            shutil.rmtree(instance_path)\n\n        # Don't create the directory - let git clone create it\n        print(f\"✅ Prepared instance path: {instance_path}\")\n        return instance_path\n    \n    def clone_openalgo_source(self, instance_path):\n        \"\"\"Clone OpenAlgo source code directly into instance folder\"\"\"\n\n        try:\n            print(f\"🔍 DEBUG: Starting clone process...\")\n            print(f\"🔍 DEBUG: Target path: {instance_path}\")\n            print(f\"🔍 DEBUG: Path exists: {instance_path.exists()}\")\n            print(f\"🔍 DEBUG: Parent directory: {instance_path.parent}\")\n            print(f\"🔍 DEBUG: Parent exists: {instance_path.parent.exists()}\")\n\n            # Ensure parent directory exists\n            instance_path.parent.mkdir(parents=True, exist_ok=True)\n\n            # Remove existing directory if it exists\n            if instance_path.exists():\n                print(f\"🗑️ Removing existing directory: {instance_path}\")\n                shutil.rmtree(instance_path)\n\n            # Clone OpenAlgo repository directly into instance folder\n            print(f\"📥 Cloning OpenAlgo from GitHub to: {instance_path}\")\n            print(f\"🔍 DEBUG: Git command: /usr/bin/git clone https://github.com/marketcalls/openalgo.git {instance_path}\")\n\n            result = subprocess.run([\n                '/usr/bin/git', 'clone',\n                'https://github.com/marketcalls/openalgo.git',\n                str(instance_path)\n            ], capture_output=True, text=True, timeout=120)\n\n            print(f\"🔍 DEBUG: Git return code: {result.returncode}\")\n            print(f\"🔍 DEBUG: Git STDOUT: {result.stdout}\")\n            print(f\"🔍 DEBUG: Git STDERR: {result.stderr}\")\n\n            if result.returncode != 0:\n                print(f\"❌ Git clone failed with return code {result.returncode}\")\n                raise Exception(f\"Git clone failed: {result.stderr}\")\n\n            print(f\"✅ OpenAlgo cloned successfully to: {instance_path}\")\n\n            # Verify the clone was successful by checking for key files\n            app_py_path = instance_path / \"app.py\"\n            print(f\"🔍 DEBUG: Checking for app.py at: {app_py_path}\")\n            print(f\"🔍 DEBUG: app.py exists: {app_py_path.exists()}\")\n\n            if not app_py_path.exists():\n                # List what files are actually there\n                if instance_path.exists():\n                    files = list(instance_path.iterdir())\n                    print(f\"🔍 DEBUG: Files in directory: {[f.name for f in files[:10]]}\")\n                raise Exception(\"Clone verification failed: app.py not found\")\n\n            return True\n\n        except Exception as e:\n            print(f\"❌ Failed to clone OpenAlgo: {e}\")\n            import traceback\n            traceback.print_exc()\n            return False\n\n    def configure_env_file(self, instance_path, setup_data):\n        \"\"\"Configure .env file with broker credentials, user data, and XTS support\"\"\"\n\n        env_file = instance_path / \".env\"\n        sample_env_file = instance_path / \".sample.env\"\n\n        # Copy .sample.env to .env first\n        if sample_env_file.exists():\n            shutil.copy2(sample_env_file, env_file)\n            print(f\"📋 Copied .sample.env to .env\")\n        else:\n            print(f\"⚠️ .sample.env not found, creating basic .env\")\n            with open(env_file, 'w') as f:\n                f.write(\"ENV_CONFIG_VERSION = '1.0.1'\\n\")\n\n        # Read the .env content\n        with open(env_file, 'r') as f:\n            env_content = f.read()\n\n        # Calculate ports based on setup_id\n        setup_id = setup_data['id']\n        port = 5000 + setup_id              # HTTP port: 5000 + setup_id\n        websocket_port = 8000 + setup_id    # WebSocket port: 8000 + setup_id\n        zmq_port = 6000 + setup_id          # ZMQ port: 6000 + setup_id\n\n        # Determine broker type (XTS or standard)\n        broker_type = self.get_broker_type(setup_data['broker_name'])\n\n        # Base configurations - use calculated ports for all configurations\n        modifications = {\n            'BROKER_API_KEY': setup_data['broker_api_key'],\n            'BROKER_API_SECRET': setup_data['broker_api_secret'],\n            'REDIRECT_URL': f'http://127.0.0.1:{port}/{setup_data[\"broker_name\"]}/callback',\n            'HOST_SERVER': f'http://127.0.0.1:{port}',\n            'FLASK_PORT': str(port),  # HTTP port\n            'WEBSOCKET_PORT': str(websocket_port),  # WebSocket port\n            'WEBSOCKET_URL': f'ws://localhost:{websocket_port}',  # WebSocket URL\n            'ZMQ_PORT': str(zmq_port),  # ZMQ port\n            'CORS_ALLOWED_ORIGINS': f'http://127.0.0.1:{port}',  # CORS for current port\n            'CSRF_ENABLED': 'FALSE',  # Disable CSRF for automation\n            # Increase rate limits by 10X for automation (prevents login failures)\n            'LOGIN_RATE_LIMIT_MIN': '50 per minute',  # 5 -> 50 (10X)\n            'LOGIN_RATE_LIMIT_HOUR': '250 per hour',  # 25 -> 250 (10X)\n            'API_RATE_LIMIT': '100 per second',  # 10 -> 100 (10X)\n        }\n\n        # Add XTS broker market API keys if needed\n        if broker_type == 'xts':\n            print(f\"🔧 Configuring XTS broker with market API keys\")\n            modifications.update({\n                'BROKER_API_KEY_MARKET': setup_data.get('broker_api_key_market', ''),\n                'BROKER_API_SECRET_MARKET': setup_data.get('broker_api_secret_market', '')\n            })\n\n        # Note: AlgoFactory data is now stored in our database, not in .env files\n\n        # Update .env content\n        lines = env_content.split('\\n')\n        updated_lines = []\n        modified_keys = set()\n\n        for line in lines:\n            if '=' in line and not line.strip().startswith('#'):\n                key = line.split('=')[0].strip()\n                if key in modifications:\n                    updated_lines.append(f\"{key} = '{modifications[key]}'\")\n                    modified_keys.add(key)\n                else:\n                    updated_lines.append(line)\n            else:\n                updated_lines.append(line)\n\n        # Clean .env file - no AlgoFactory data added\n\n        # Write updated .env file\n        with open(env_file, 'w') as f:\n            f.write('\\n'.join(updated_lines))\n\n        print(f\"✅ Configured .env file for {broker_type.upper()} broker: {env_file}\")\n        print(f\"🚀 Rate limits increased 10X for automation: LOGIN=50/min, API=100/sec\")\n        return port\n\n    def fix_socketio_async_mode(self, instance_path):\n        \"\"\"Skip SocketIO setup - let OpenAlgo run normally without auto-binding\"\"\"\n        extensions_file = instance_path / \"extensions.py\"\n\n        print(f\"⚠️ Skipping SocketIO auto-setup - OpenAlgo will run normally without auto-binding\")\n        print(f\"📝 Extensions file: {extensions_file}\")\n        print(f\"🔧 Manual start/stop control enabled - no automatic SocketIO binding\")\n\n        # Do not modify extensions.py - let OpenAlgo run with its default configuration\n        # This prevents automatic SocketIO startup and binding issues\n\n    def get_broker_type(self, broker_name):\n        \"\"\"Determine if broker is XTS type or standard\"\"\"\n        xts_brokers = ['fivepaisaxts', 'compositedge', 'iifl']  # Add more XTS brokers as needed\n        return 'xts' if broker_name in xts_brokers else 'standard'\n\n    def get_port_from_env_content(self, env_content):\n        \"\"\"Get port from .env file content, return OpenAlgo default if not found\"\"\"\n        for line in env_content.split('\\n'):\n            if 'FLASK_PORT' in line and '=' in line:\n                try:\n                    port_str = line.split('=')[1].strip().strip(\"'\\\"\")\n                    return int(port_str)\n                except:\n                    pass\n        # Return OpenAlgo default port\n        return 5000\n\n    def install_dependencies_shared_venv(self, instance_path):\n        \"\"\"Install OpenAlgo dependencies using shared virtual environment\"\"\"\n\n        try:\n            print(f\"📦 Installing dependencies using shared virtual environment...\")\n\n            # Use the shared virtual environment\n            venv_python = self.base_dir / \"env/bin/python\"\n            venv_pip = self.base_dir / \"env/bin/pip\"\n\n            if not venv_python.exists():\n                print(f\"❌ Shared virtual environment not found: {venv_python}\")\n                return False\n\n            # Check if requirements.txt exists\n            requirements_file = instance_path / \"requirements.txt\"\n            if requirements_file.exists():\n                # Install requirements using shared venv\n                result = subprocess.run([\n                    str(venv_pip), 'install', '-r', str(requirements_file)\n                ], capture_output=True, text=True, cwd=str(instance_path))\n\n                if result.returncode != 0:\n                    print(f\"⚠️ Warning: pip install failed: {result.stderr}\")\n                    return False\n                else:\n                    print(f\"✅ Dependencies installed successfully in shared venv\")\n                    return True\n            else:\n                print(f\"⚠️ No requirements.txt found, skipping dependency installation\")\n                return True\n\n        except Exception as e:\n            print(f\"⚠️ Warning: Failed to install dependencies: {e}\")\n            return False\n\n    def install_dependencies_instance_venv(self, instance_path):\n        \"\"\"Install OpenAlgo dependencies using instance-specific virtual environment\"\"\"\n\n        try:\n            print(f\"📦 Creating virtual environment for {instance_path.name}...\")\n\n            # Create instance-specific virtual environment\n            venv_path = instance_path / \"venv\"\n\n            # Remove existing venv if it exists\n            if venv_path.exists():\n                shutil.rmtree(venv_path)\n\n            # Create new virtual environment\n            result = subprocess.run([\n                \"python3\", \"-m\", \"venv\", str(venv_path)\n            ], capture_output=True, text=True)\n\n            if result.returncode != 0:\n                print(f\"❌ Failed to create virtual environment: {result.stderr}\")\n                return False\n\n            # Install requirements in the new venv\n            venv_python = venv_path / \"bin\" / \"python\"\n            venv_pip = venv_path / \"bin\" / \"pip\"\n            requirements_file = instance_path / \"requirements.txt\"\n\n            if not requirements_file.exists():\n                print(f\"⚠️ No requirements.txt found in {instance_path}\")\n                return True\n\n            # Upgrade pip first\n            subprocess.run([str(venv_pip), \"install\", \"--upgrade\", \"pip\"], capture_output=True)\n\n            # Install requirements\n            result = subprocess.run([\n                str(venv_pip), \"install\", \"-r\", str(requirements_file)\n            ], capture_output=True, text=True, cwd=str(instance_path))\n\n            if result.returncode == 0:\n                print(f\"✅ Dependencies installed successfully in instance venv\")\n                return True\n            else:\n                print(f\"❌ Failed to install dependencies: {result.stderr}\")\n                return False\n\n        except Exception as e:\n            print(f\"❌ Dependency installation failed: {e}\")\n            return False\n\n    def start_openalgo_instance(self, instance_path, port, setup_data):\n        \"\"\"Start OpenAlgo instance with Gunicorn\"\"\"\n\n        try:\n            print(f\"🚀 Starting OpenAlgo instance on port {port}...\")\n\n            # Create Gunicorn configuration\n            gunicorn_conf = instance_path / \"gunicorn.conf.py\"\n            gunicorn_config = f\"\"\"# Gunicorn configuration for {setup_data['setup_name']}\nbind = \"0.0.0.0:{port}\"\nworkers = 1\nworker_class = \"uvicorn.workers.UvicornWorker\"\nworker_connections = 1000\nmax_requests = 1000\nmax_requests_jitter = 100\ntimeout = 30\nkeepalive = 2\npreload_app = True\ndaemon = True\npidfile = \"{instance_path}/gunicorn.pid\"\naccesslog = \"{instance_path}/access.log\"\nerrorlog = \"{instance_path}/error.log\"\nloglevel = \"info\"\n\"\"\"\n\n            with open(gunicorn_conf, 'w') as f:\n                f.write(gunicorn_config)\n\n            # Start Gunicorn process\n            cmd = [\n                'gunicorn',\n                '-c', str(gunicorn_conf),\n                'app:app'  # Assuming main file is app.py\n            ]\n\n            result = subprocess.run(\n                cmd,\n                cwd=str(instance_path),\n                capture_output=True,\n                text=True\n            )\n\n            if result.returncode != 0:\n                print(f\"⚠️ Gunicorn start warning: {result.stderr}\")\n\n            # Wait a moment for startup\n            time.sleep(2)\n\n            # Check if process is running\n            if self.check_instance_running(port):\n                print(f\"✅ Algo instance started successfully on port {port}\")\n                return True\n            else:\n                print(f\"⚠️ Algo instance may not be running properly\")\n                return False\n\n        except Exception as e:\n            print(f\"❌ Failed to start OpenAlgo instance: {e}\")\n            return False\n\n    def check_instance_running(self, port):\n        \"\"\"Check if OpenAlgo instance is running on specified port\"\"\"\n\n        try:\n            response = requests.get(f\"http://127.0.0.1:{port}\", timeout=5)\n            return response.status_code == 200\n        except:\n            return False\n\n    def get_actual_running_port(self, setup_id):\n        \"\"\"Get the actual port where the service is running from database\"\"\"\n        try:\n            import sqlite3\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n                cursor.execute(\"\"\"\n                    SELECT http_port, setup_status\n                    FROM user_brokers\n                    WHERE id = ?\n                \"\"\", (setup_id,))\n\n                result = cursor.fetchone()\n                if result:\n                    http_port, setup_status = result\n                    if http_port:\n                        print(f\"🔍 Found HTTP port {http_port} from database for setup {setup_id}\")\n\n                        # Verify the port is actually running\n                        if self.check_instance_running(http_port):\n                            print(f\"✅ Confirmed port {http_port} is running\")\n                            return http_port\n                        else:\n                            print(f\"⚠️ Port {http_port} from database is not responding\")\n                    else:\n                        print(f\"⚠️ No HTTP port stored in database for setup {setup_id}\")\n                else:\n                    print(f\"⚠️ Setup {setup_id} not found in database\")\n\n            # Fallback: try calculated port\n            calculated_port = 5000 + setup_id\n            print(f\"🔄 Trying fallback calculated port: {calculated_port}\")\n            if self.check_instance_running(calculated_port):\n                print(f\"✅ Fallback port {calculated_port} is working\")\n                return calculated_port\n\n            print(f\"❌ No working port found for setup {setup_id}\")\n            return None\n\n        except Exception as e:\n            print(f\"❌ Error getting port from database: {e}\")\n            return None\n\n    def auto_register_user(self, port, setup_data):\n        \"\"\"Auto-register user in Algo\"\"\"\n\n        try:\n            print(f\"👤 Auto-registering user in Algo...\")\n\n            algo_url = f\"http://127.0.0.1:{port}\"\n            session = requests.Session()\n\n            # Wait for Algo to fully start (reduced for faster connection)\n            print(\"⏳ Waiting for Algo to initialize...\")\n            time.sleep(3)\n\n            # Check if Algo is responding\n            for attempt in range(5):\n                try:\n                    response = session.get(algo_url, timeout=5)\n                    if response.status_code == 200:\n                        break\n                    print(f\"⏳ Attempt {attempt + 1}: Algo not ready yet...\")\n                    time.sleep(2)\n                except:\n                    print(f\"⏳ Attempt {attempt + 1}: Algo not responding yet...\")\n                    time.sleep(2)\n            else:\n                print(\"❌ Algo instance not responding after multiple attempts\")\n                return False\n\n            # Get setup page (initial setup for OpenAlgo)\n            response = session.get(f\"{algo_url}/setup\", timeout=10)\n            if response.status_code != 200:\n                print(f\"❌ Cannot access setup page: {response.status_code}\")\n                print(f\"Response text: {response.text[:200]}...\")\n                return False\n\n            # Setup admin user with standard password pattern (CSRF disabled)\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            user_password = f\"algofactory{username}\"  # Standard password pattern\n            setup_data_form = {\n                \"username\": username,\n                \"email\": setup_data['email'],\n                \"password\": user_password\n            }\n            print(f\"🔐 Setting up user with username: {username} and password: algofactory{username}\")\n\n            setup_response = session.post(\n                f\"{algo_url}/setup\",\n                data=setup_data_form,\n                timeout=10\n            )\n\n            if setup_response.status_code == 200 or \"login\" in setup_response.url:\n                print(\"✅ Admin user setup completed successfully in Algo\")\n                return True\n            else:\n                print(f\"❌ Setup failed: {setup_response.status_code}\")\n                print(f\"Response text: {setup_response.text[:200]}...\")\n                return False\n\n        except Exception as e:\n            print(f\"❌ Auto-registration failed: {e}\")\n            return False\n\n    def auto_login(self, port, setup_data):\n        \"\"\"Auto-login to OpenAlgo and return session\"\"\"\n\n        try:\n            print(f\"🔐 Auto-logging into OpenAlgo...\")\n\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n            session = requests.Session()\n\n            # Get login page\n            login_page = session.get(f\"{openalgo_url}/auth/login\", timeout=10)\n            if login_page.status_code != 200:\n                print(f\"❌ Cannot access login page: {login_page.status_code}\")\n                return None\n\n            # Submit login (CSRF disabled) - Use standard password pattern\n            # Use the actual username from users table, not the one from user_brokers\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))  # This is from users table via JOIN\n            user_password = f\"algofactory{username}\"  # Standard password pattern\n            login_data = {\n                \"username\": username,\n                \"password\": user_password\n            }\n            print(f\"🔐 Logging in with username: {username} and password: algofactory{username}\")\n\n            # Handle rate limiting with retry mechanism\n            for attempt in range(3):\n                if attempt > 0:\n                    print(f\"⏳ Retrying login (attempt {attempt + 1}/3) after rate limit...\")\n                    time.sleep(5)  # Wait 5 seconds between attempts\n\n                login_response = session.post(\n                    f\"{openalgo_url}/auth/login\",\n                    data=login_data,\n                    allow_redirects=True,\n                    timeout=10\n                )\n\n                if login_response.status_code == 200:\n                    # Check if we're redirected away from login page (successful login)\n                    if \"login\" not in login_response.url or \"dashboard\" in login_response.url or \"broker\" in login_response.url:\n                        print(\"✅ OpenAlgo login successful\")\n                        return session\n                    else:\n                        print(f\"⚠️ Login response 200 but still on login page: {login_response.url}\")\n                        continue\n                elif login_response.status_code == 429:\n                    print(f\"⚠️ Rate limited (429), waiting before retry...\")\n                    continue\n                else:\n                    print(f\"❌ Login failed: {login_response.status_code}\")\n                    return None\n\n            print(\"❌ Login failed after all retry attempts\")\n            return None\n\n        except Exception as e:\n            print(f\"❌ Auto-login failed: {e}\")\n            return None\n\n    def get_setup_connection_status(self, setup_id):\n        \"\"\"Get connection status from our database\"\"\"\n        try:\n            import sqlite3\n            with sqlite3.connect(self.db_path) as conn:\n                cursor = conn.cursor()\n                cursor.execute(\"SELECT connection_status FROM user_brokers WHERE id = ?\", (setup_id,))\n                row = cursor.fetchone()\n                return row[0] if row else None\n        except Exception as e:\n            print(f\"⚠️ Failed to get setup status: {e}\")\n            return None\n\n    def check_existing_connection(self, port, setup_data, session):\n        \"\"\"Ultra-fast check if broker is already connected\"\"\"\n        try:\n            print(f\"🔍 Ultra-fast connection check...\")\n\n            # FASTEST: Check OpenAlgo database directly for auth tokens\n            instance_name = self.get_instance_name(setup_data)\n            instance_path = self.instances_dir / instance_name\n            algo_db_path = instance_path / \"db\" / \"openalgo.db\"\n\n            if algo_db_path.exists():\n                import sqlite3\n                with sqlite3.connect(algo_db_path) as conn:\n                    conn.row_factory = sqlite3.Row\n                    cursor = conn.cursor()\n\n                    # Check if auth tokens exist and are not revoked\n                    cursor.execute(\"SELECT auth, is_revoked FROM auth WHERE auth != '' AND auth IS NOT NULL AND (is_revoked = 0 OR is_revoked IS NULL) LIMIT 1\")\n                    auth_row = cursor.fetchone()\n\n                    if auth_row and auth_row['auth'] and len(auth_row['auth']) > 10:\n                        print(\"✅ Found valid auth tokens in OpenAlgo database\")\n                        return True\n                    else:\n                        print(\"❌ No valid auth tokens found (empty, revoked, or missing)\")\n                        return False\n\n            print(\"❌ No valid connection found - proceeding with authentication\")\n            return False\n\n        except Exception as e:\n            print(f\"⚠️ Fast connection check failed: {e} - proceeding with authentication\")\n            return False\n\n    def auto_broker_auth(self, port, setup_data, session):\n        \"\"\"Auto-authenticate broker in OpenAlgo\"\"\"\n\n        try:\n            print(f\"🏦 Auto-authenticating {setup_data['broker_name']} broker...\")\n\n            # FAST CHECK: See if already connected (2-3 seconds)\n            # BUT ONLY if our database also shows connected\n            our_db_status = self.get_setup_connection_status(setup_data.get('id'))\n            if our_db_status == 'connected' and self.check_existing_connection(port, setup_data, session):\n                print(\"⚡ Broker already connected - skipping authentication!\")\n                return True\n            elif our_db_status == 'disconnected':\n                print(\"🔄 Database shows disconnected - forcing full authentication\")\n            else:\n                print(\"🔄 No existing connection or status mismatch - proceeding with authentication\")\n\n            print(f\"🔄 No existing connection - proceeding with authentication...\")\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n\n            # Navigate to broker selection page\n            broker_page = session.get(f\"{openalgo_url}/auth/broker\", timeout=10)\n            if broker_page.status_code != 200:\n                print(f\"❌ Cannot access broker page: {broker_page.status_code}\")\n                return False\n\n            print(f\"✅ Accessed broker selection page\")\n\n            # Find and click the Angel broker connect button\n            # Look for the connect URL in the page\n            if \"angel\" in broker_page.text.lower():\n                print(f\"✅ Found Angel broker option\")\n\n                # Try to find the connect link for Angel\n                import re\n                connect_match = re.search(r'href=\"([^\"]*angel[^\"]*)\"', broker_page.text)\n                if connect_match:\n                    connect_path = connect_match.group(1)\n                    if not connect_path.startswith('http'):\n                        connect_url = f\"{openalgo_url}{connect_path}\"\n                    else:\n                        connect_url = connect_path\n                else:\n                    # Fallback to standard Angel callback URL\n                    connect_url = f\"{openalgo_url}/angel/callback\"\n\n                # Navigate directly to Angel callback URL to get the form\n                angel_auth_url = f\"{openalgo_url}/angel/callback\"\n                print(f\"🔗 Getting Angel authentication form from: {angel_auth_url}\")\n\n                # GET request to get the authentication form (faster timeout)\n                auth_form_page = session.get(angel_auth_url, timeout=5)\n\n                if auth_form_page.status_code != 200:\n                    print(f\"❌ Cannot access Angel auth form: {auth_form_page.status_code}\")\n                    return False\n\n                print(f\"✅ Retrieved Angel authentication form\")\n\n                # The form submits back to the same URL via POST\n                submit_url = angel_auth_url\n            else:\n                print(f\"❌ Angel broker not found on broker page\")\n                return False\n\n            # Generate TOTP\n            totp_code = self.generate_totp(setup_data['totp_secret'])\n            if not totp_code:\n                print(\"❌ Failed to generate TOTP\")\n                return False\n\n            print(f\"🔐 Generated TOTP: {totp_code}\")\n            print(f\"🔑 Using Client ID: {setup_data['broker_client_id']}\")\n\n            # Submit broker authentication (CSRF disabled)\n            # Use the exact field names from OpenAlgo Angel form\n            auth_data = {\n                \"clientid\": setup_data['broker_client_id'],  # OpenAlgo uses 'clientid' not 'client_id'\n                \"pin\": setup_data['trading_pin'],\n                \"totp\": totp_code\n            }\n\n            print(f\"🚀 Submitting broker authentication to: {submit_url}\")\n            print(f\"📝 Auth data: {auth_data}\")\n\n            auth_response = session.post(\n                submit_url,\n                data=auth_data,\n                allow_redirects=True,\n                timeout=15  # Reduced timeout for faster response\n            )\n\n            print(f\"📡 Auth response status: {auth_response.status_code}\")\n            print(f\"📡 Auth response URL: {auth_response.url}\")\n            print(f\"📄 Response preview: {auth_response.text[:500]}...\")\n\n            if auth_response.status_code == 200:\n                # Check if authentication was successful\n                if \"success\" in auth_response.text.lower() or \"authenticated\" in auth_response.text.lower():\n                    print(\"✅ Broker authentication successful\")\n\n                    # Wait for OpenAlgo to process and store tokens (reduced)\n                    import time\n                    time.sleep(2)\n\n                    # Retrieve and save tokens from Algo database using updated method\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n                    if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):\n                        print(\"✅ OpenAlgo API key retrieved and saved successfully\")\n                        return True\n                    else:\n                        print(\"⚠️ Authentication appeared successful but no tokens found in database\")\n                        print(\"🔍 This indicates the broker authentication didn't complete properly in Algo\")\n                        return False\n                else:\n                    print(\"❌ Broker authentication failed - no success indication\")\n                    return False\n            else:\n                print(f\"❌ Broker authentication failed: {auth_response.status_code}\")\n                return False\n\n        except Exception as e:\n            print(f\"❌ Auto-broker authentication failed: {e}\")\n\n            # If it's a timeout, check if tokens already exist\n            if \"timeout\" in str(e).lower() or \"timed out\" in str(e).lower():\n                print(\"⚠️ Broker authentication timed out, but checking if tokens already exist...\")\n\n                # Even if the API call timed out, check if tokens already exist\n                try:\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n                    if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):\n                        print(\"✅ Found existing tokens! Broker was already authenticated\")\n                        return True\n                    else:\n                        print(\"❌ No existing tokens found\")\n                        return False\n                except Exception as token_error:\n                    print(f\"❌ Token retrieval also failed: {token_error}\")\n                    return False\n\n            return False\n\n    def generate_totp(self, totp_secret):\n        \"\"\"Generate TOTP code\"\"\"\n\n        try:\n            totp = pyotp.TOTP(totp_secret)\n            return totp.now()\n        except Exception as e:\n            print(f\"❌ TOTP generation failed: {e}\")\n            return None\n\n    def retrieve_and_save_tokens(self, port, setup_data):\n        \"\"\"Retrieve authentication tokens from Algo database and save decrypted keys to our database\"\"\"\n\n        try:\n            print(f\"🔍 Retrieving tokens from Algo database...\")\n\n            # Algo database path (in db subfolder)\n            instance_name = self.get_instance_name(setup_data)\n            instance_path = self.instances_dir / instance_name\n            algo_db_path = instance_path / \"db\" / \"openalgo.db\"  # Still named openalgo.db in the repo\n\n            if not algo_db_path.exists():\n                print(f\"❌ Algo database not found: {algo_db_path}\")\n                return None\n\n            # Connect to Algo database\n            conn = sqlite3.connect(algo_db_path)\n            conn.row_factory = sqlite3.Row\n            cursor = conn.cursor()\n\n            # Check auth table for broker authentication tokens\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            cursor.execute(\"SELECT * FROM auth WHERE user_id = ? OR name = ? LIMIT 1\", (username, username))\n            auth_row = cursor.fetchone()\n\n            # Check symtoken table for master contracts (optimized for speed)\n            print(f\"📊 Checking symbol download status...\")\n            symbol_download_complete = False\n            max_wait_time = 30  # Reduced wait time for faster response\n            wait_start = time.time()\n\n            # First quick check - if symbols already exist, don't wait\n            cursor.execute(\"SELECT COUNT(*) as count FROM symtoken\")\n            symtoken_count = cursor.fetchone()['count']\n\n            if symtoken_count > 100000:  # Angel typically has 120k+ symbols\n                symbol_download_complete = True\n                print(f\"✅ Symbol download already complete: {symtoken_count} symbols\")\n            else:\n                print(f\"⏳ Symbol download in progress: {symtoken_count} symbols\")\n                # Only wait if symbols are actively downloading\n                while not symbol_download_complete and (time.time() - wait_start) < max_wait_time:\n                    time.sleep(3)  # Reduced check interval\n                    cursor.execute(\"SELECT COUNT(*) as count FROM symtoken\")\n                    symtoken_count = cursor.fetchone()['count']\n\n                    if symtoken_count > 100000:\n                        symbol_download_complete = True\n                        print(f\"✅ Symbol download complete: {symtoken_count} symbols\")\n                    else:\n                        print(f\"⏳ Symbol download progress: {symtoken_count} symbols\")\n\n            if not symbol_download_complete:\n                print(f\"⚠️ Symbol download timeout after {max_wait_time}s: {symtoken_count} symbols (continuing anyway)\")\n\n            # Check api_keys table for API keys\n            cursor.execute(\"SELECT * FROM api_keys WHERE user_id = ? LIMIT 1\", (username,))\n            api_key_row = cursor.fetchone()\n\n            conn.close()\n\n            # Decrypt and save API keys to our database\n            decrypted_tokens = self.decrypt_and_save_api_keys(auth_row, api_key_row, setup_data, symtoken_count)\n\n            if decrypted_tokens:\n                print(f\"✅ OpenAlgo setup complete: Broker_Auth={decrypted_tokens['has_auth_token']}, API_Key={decrypted_tokens['has_openalgo_api_key']}, Symbols={symtoken_count}\")\n                return decrypted_tokens\n            else:\n                print(\"❌ No authentication tokens found in Algo database\")\n                return None\n\n        except Exception as e:\n            print(f\"❌ Token retrieval failed: {e}\")\n            return None\n\n    # REMOVED: step_4_connect_broker_manual_init() - Duplicate method, use run_complete_manual_setup() instead\n\n    # REMOVED: step_4_connect_broker_manual_verify() - Duplicate method, use connect_with_manual_otp() instead\n\n    # REMOVED: step_4_connect_broker_with_credentials() - Duplicate method #1\n\n    # REMOVED: step_4_connect_broker_with_terminal_totp() - Legacy method, not needed\n\n    def decrypt_and_save_api_keys(self, auth_row, api_key_row, setup_data, symbol_count):\n        \"\"\"Decrypt OpenAlgo API keys from Algo database and save to our database\"\"\"\n\n        try:\n            from cryptography.fernet import Fernet\n            import os\n\n            # Get encryption keys from Algo .env file\n            instance_name = self.get_instance_name(setup_data)\n            instance_path = self.instances_dir / instance_name\n            env_file = instance_path / \".env\"\n\n            app_key = None\n            api_key_pepper = None\n\n            if env_file.exists():\n                with open(env_file, 'r') as f:\n                    for line in f:\n                        line = line.strip()\n                        if line.startswith('APP_KEY ='):\n                            app_key = line.split('=', 1)[1].strip().strip(\"'\\\"\")\n                        elif line.startswith('API_KEY_PEPPER ='):\n                            api_key_pepper = line.split('=', 1)[1].strip().strip(\"'\\\"\")\n\n            if not app_key or not api_key_pepper:\n                print(f\"❌ Encryption keys not found in .env file: APP_KEY={bool(app_key)}, API_KEY_PEPPER={bool(api_key_pepper)}\")\n                return None\n\n            print(f\"✅ Found encryption keys in .env file\")\n\n            # Initialize Fernet cipher using OpenAlgo's method\n            from cryptography.fernet import Fernet\n            from cryptography.hazmat.primitives import hashes\n            from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC\n            import base64\n\n            # Use OpenAlgo's encryption method\n            kdf = PBKDF2HMAC(\n                algorithm=hashes.SHA256(),\n                length=32,\n                salt=b'openalgo_static_salt',  # OpenAlgo's static salt\n                iterations=100000,\n            )\n            key = base64.urlsafe_b64encode(kdf.derive(api_key_pepper.encode()))\n            cipher = Fernet(key)\n\n            # Decrypt OpenAlgo API key (this is what we need for trading)\n            decrypted_openalgo_api_key = None\n\n            if api_key_row:\n                try:\n                    if 'api_key_encrypted' in api_key_row.keys() and api_key_row['api_key_encrypted']:\n                        decrypted_openalgo_api_key = cipher.decrypt(api_key_row['api_key_encrypted'].encode()).decode()\n                        print(f\"✅ OpenAlgo API key decrypted successfully\")\n                except Exception as e:\n                    print(f\"⚠️ OpenAlgo API key decryption failed: {e}\")\n\n            # Check broker authentication status (auth_row indicates broker is connected)\n            broker_auth_status = \"connected\" if auth_row else \"failed\"\n\n            # Get port information from .env\n            websocket_port, zmq_port = self.get_port_info_from_env(instance_path)\n            http_port = 5000 + setup_data['id']  # Calculate HTTP port\n\n            # Save to our database\n            self.save_decrypted_tokens_to_database(\n                setup_data['id'],\n                broker_auth_status,  # Auth status (broker connected)\n                decrypted_openalgo_api_key,  # OpenAlgo API key for trading\n                app_key,  # APP_KEY for future use\n                api_key_pepper,  # API_KEY_PEPPER for future use\n                symbol_count,\n                websocket_port,  # WebSocket port\n                zmq_port,  # ZMQ port\n                http_port  # HTTP port\n            )\n\n            # Test OpenAlgo API if we have the key\n            api_test_result = None\n            if decrypted_openalgo_api_key:\n                api_test_result = self.test_openalgo_api(setup_data, decrypted_openalgo_api_key)\n\n            # Return status\n            return {\n                \"has_auth_token\": bool(auth_row),  # Broker authentication\n                \"has_openalgo_api_key\": bool(decrypted_openalgo_api_key),  # OpenAlgo API key\n                \"symbol_count\": symbol_count,\n                \"auth_token_status\": broker_auth_status,\n                \"symbol_token_status\": \"connected\" if symbol_count > 100000 else \"downloading\" if symbol_count > 0 else \"failed\",\n                \"api_test_result\": api_test_result\n            }\n\n        except Exception as e:\n            print(f\"❌ Token decryption failed: {e}\")\n            return None\n\n    def get_port_info_from_env(self, instance_path):\n        \"\"\"Get WebSocket and ZMQ port information from .env file\"\"\"\n\n        try:\n            env_file = instance_path / \".env\"\n            websocket_port = None\n            zmq_port = None\n\n            if env_file.exists():\n                with open(env_file, 'r') as f:\n                    for line in f:\n                        line = line.strip()\n                        if line.startswith('WEBSOCKET_PORT='):\n                            websocket_port = int(line.split('=', 1)[1].strip().strip(\"'\\\"\"))\n                        elif line.startswith('ZMQ_PORT='):\n                            zmq_port = int(line.split('=', 1)[1].strip().strip(\"'\\\"\"))\n\n            print(f\"📡 Port info: WebSocket={websocket_port}, ZMQ={zmq_port}\")\n            return websocket_port, zmq_port\n\n        except Exception as e:\n            print(f\"⚠️ Failed to get port info: {e}\")\n            return None, None\n\n    def save_decrypted_tokens_to_database(self, setup_id, auth_status, openalgo_api_key, app_key, api_key_pepper, symbol_count, websocket_port, zmq_port, http_port=None):\n        \"\"\"Save decrypted OpenAlgo API key, encryption keys, and port information to our database\"\"\"\n\n        try:\n            import sqlite3\n            from datetime import datetime\n\n            # Connect to our database\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n            conn = sqlite3.connect(db_path)\n            cursor = conn.cursor()\n\n            # Determine status indicators\n            symbol_status = \"connected\" if symbol_count > 100000 else \"downloading\" if symbol_count > 0 else \"failed\"\n\n            # Update broker setup with OpenAlgo API key, encryption keys, and port information\n            # Calculate HTTP port if not provided\n            if http_port is None:\n                http_port = 5000 + setup_id\n\n            cursor.execute(\"\"\"\n                UPDATE user_brokers\n                SET algo_api_key = ?,\n                    algo_app_key = ?,\n                    algo_api_key_pepper = ?,\n                    http_port = ?,\n                    websocket_port = ?,\n                    zmq_port = ?,\n                    auth_token_status = ?,\n                    symbol_token_status = ?,\n                    symbol_count = ?,\n                    last_connected_at = ?,\n                    status = 'connected',\n                    updated_at = ?\n                WHERE id = ?\n            \"\"\", (\n                openalgo_api_key,  # OpenAlgo API key for trading\n                app_key,           # APP_KEY for future decryption\n                api_key_pepper,    # API_KEY_PEPPER for future decryption\n                http_port,         # HTTP port for web interface\n                websocket_port,    # WebSocket port for real-time data\n                zmq_port,          # ZMQ port for messaging\n                auth_status,       # Broker authentication status\n                symbol_status,     # Symbol download status\n                symbol_count,\n                datetime.now().isoformat(),\n                datetime.now().isoformat(),\n                setup_id\n            ))\n\n            conn.commit()\n            conn.close()\n\n            print(f\"💾 OpenAlgo data saved: Auth={auth_status}, Symbols={symbol_status}, API_Key={'✅' if openalgo_api_key else '❌'}, WebSocket={websocket_port}, ZMQ={zmq_port}\")\n\n        except Exception as e:\n            print(f\"❌ Failed to save tokens to database: {e}\")\n\n    def save_port_info_to_database(self, setup_id, http_port, websocket_port, zmq_port):\n        \"\"\"Save port information to database during Step 1\"\"\"\n        try:\n            import sqlite3\n            from datetime import datetime\n\n            conn = sqlite3.connect(self.db_path)\n            cursor = conn.cursor()\n\n            cursor.execute(\"\"\"\n                UPDATE user_brokers\n                SET http_port = ?,\n                    websocket_port = ?,\n                    zmq_port = ?,\n                    updated_at = ?\n                WHERE id = ?\n            \"\"\", (\n                http_port,\n                websocket_port,\n                zmq_port,\n                datetime.now().isoformat(),\n                setup_id\n            ))\n\n            conn.commit()\n            conn.close()\n\n            print(f\"💾 Port info saved: HTTP={http_port}, WebSocket={websocket_port}, ZMQ={zmq_port}\")\n\n        except Exception as e:\n            print(f\"❌ Failed to save port info to database: {e}\")\n\n    def test_openalgo_api(self, setup_data, api_key):\n        \"\"\"Test OpenAlgo API calls to verify everything is working\"\"\"\n\n        try:\n            import requests\n\n            # Get the port for this instance\n            port = self.get_actual_running_port(setup_data['id'])\n            if not port:\n                print(\"❌ Cannot determine OpenAlgo port for API testing\")\n                return {\"status\": \"failed\", \"error\": \"Port not found\"}\n\n            base_url = f\"http://127.0.0.1:{port}/api/v1\"\n\n            print(f\"🧪 Testing OpenAlgo API at {base_url}\")\n\n            # Test 1: Funds API\n            try:\n                funds_response = requests.post(\n                    f\"{base_url}/funds\",\n                    json={\"apikey\": api_key},\n                    timeout=10\n                )\n                funds_working = funds_response.status_code == 200\n                print(f\"💰 Funds API: {'✅' if funds_working else '❌'} ({funds_response.status_code})\")\n            except Exception as e:\n                funds_working = False\n                print(f\"💰 Funds API: ❌ ({e})\")\n\n            # Test 2: Holdings API\n            try:\n                holdings_response = requests.post(\n                    f\"{base_url}/holdings\",\n                    json={\"apikey\": api_key},\n                    timeout=10\n                )\n                holdings_working = holdings_response.status_code == 200\n                print(f\"📊 Holdings API: {'✅' if holdings_working else '❌'} ({holdings_response.status_code})\")\n            except Exception as e:\n                holdings_working = False\n                print(f\"📊 Holdings API: ❌ ({e})\")\n\n            # Test 3: Positions API\n            try:\n                positions_response = requests.post(\n                    f\"{base_url}/positionbook\",\n                    json={\"apikey\": api_key},\n                    timeout=10\n                )\n                positions_working = positions_response.status_code == 200\n                print(f\"📈 Positions API: {'✅' if positions_working else '❌'} ({positions_response.status_code})\")\n            except Exception as e:\n                positions_working = False\n                print(f\"📈 Positions API: ❌ ({e})\")\n\n            # Overall status\n            all_working = funds_working and holdings_working and positions_working\n\n            result = {\n                \"status\": \"success\" if all_working else \"partial\",\n                \"base_url\": base_url,\n                \"api_key_working\": True,\n                \"tests\": {\n                    \"funds\": funds_working,\n                    \"holdings\": holdings_working,\n                    \"positions\": positions_working\n                },\n                \"ready_for_trading\": all_working\n            }\n\n            if all_working:\n                print(\"🎉 All OpenAlgo APIs working perfectly! Ready for trading!\")\n            else:\n                print(\"⚠️ Some OpenAlgo APIs not working properly\")\n\n            return result\n\n        except Exception as e:\n            print(f\"❌ OpenAlgo API testing failed: {e}\")\n            return {\"status\": \"failed\", \"error\": str(e)}\n    \n    def step_1_set_env(self, setup_id):\n        \"\"\"STEP 1: SET_ENV - Create Algo instance and configure .env file\"\"\"\n\n        # Get setup data from database including user info\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        try:\n            print(f\"🔧 STEP 1: SET_ENV for {setup_data['setup_name']}\")\n\n            # Step 1: Create instance folder\n            instance_path = self.create_instance_folder(\n                setup_data['id'],\n                setup_data['broker_name'],\n                setup_data['setup_name']\n            )\n\n            # Step 2: Clone Algo source code\n            if not self.clone_openalgo_source(instance_path):\n                raise Exception(\"Failed to clone Algo source code\")\n\n            # Step 3: Configure .env file with broker and user data\n            port = self.configure_env_file(instance_path, setup_data)\n\n            # Step 3.4: Save port information to database\n            websocket_port = 8000 + setup_data['id']\n            zmq_port = 6000 + setup_data['id']\n            self.save_port_info_to_database(setup_data['id'], port, websocket_port, zmq_port)\n\n            # Step 3.5: Fix SocketIO async_mode to avoid trio/gevent conflicts\n            self.fix_socketio_async_mode(instance_path)\n\n            # Step 4: Skip dependency installation - using global environment\n            print(f\"✅ Using global Python environment - no dependency installation needed\")\n\n            # Update database with instance info\n            instance_info = {\n                \"instance_path\": str(instance_path),\n                \"port\": port,\n                \"status\": \"env_configured\",\n                \"algo_url\": f\"http://127.0.0.1:{port}\",\n                \"step_completed\": \"set_env\",\n                \"created_at\": self.get_ist_timestamp()\n            }\n\n            self.update_setup_instance_info(setup_id, instance_info)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ STEP 1 (SET_ENV) completed for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"set_env\",\n                    \"instance_path\": str(instance_path),\n                    \"port\": port,\n                    \"algo_url\": f\"http://127.0.0.1:{port}\",\n                    \"setup_name\": setup_data['setup_name'],\n                    \"broker_name\": setup_data['broker_name'],\n                    \"broker_type\": setup_data.get('broker_type', 'standard'),\n                    \"next_step\": \"step_2_start_algo\"\n                }\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"STEP 1 (SET_ENV) failed: {str(e)}\"\n            }\n\n    def fast_start_service(self, setup_id):\n        \"\"\"FAST START: Just start existing systemd service (no git pull, no pip install)\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use full service name: username-algo-broker-instance\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        service_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n\n        try:\n            print(f\"⚡ FAST START: Starting existing service {service_name}\")\n\n            # Just start the service - no git pull, no pip install\n            result = self.run_systemctl('start', service_name)\n\n            if result.returncode != 0:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to start service: {result.stderr}\"\n                }\n\n            # Quick 1-second check\n            import time\n            time.sleep(1)\n\n            port = 5000 + setup_id\n            print(f\"✅ Service started successfully\")\n            print(f\"🌐 Algo service available at: http://127.0.0.1:{port}\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Service {service_name} started successfully (fast start)\",\n                \"service_name\": service_name,\n                \"port\": port,\n                \"instance_path\": str(self.instances_dir / service_name),\n                \"fast_start\": True,\n                \"next_step\": \"step_3_register\"\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"FAST START failed: {str(e)}\"\n            }\n\n    def step_2_start_algo(self, setup_id):\n        \"\"\"STEP 2: START_ALGO - Enhanced start with git pull, requirements, and full service name\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use full service name: username-algo-broker-instance\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        if not instance_path.exists():\n            return {\"status\": \"error\", \"message\": \"Instance not found. Run STEP 1 (SET_ENV) first.\"}\n\n        try:\n            print(f\"🚀 STEP 2: START_ALGO - Enhanced start for {setup_data['setup_name']}\")\n            print(f\"📁 Instance: {instance_name}\")\n\n            # Step 1: Git pull for latest updates\n            print(f\"🔄 Updating Algo source code...\")\n            git_result = subprocess.run([\n                '/usr/bin/git', 'pull', 'origin', 'main'\n            ], cwd=instance_path, capture_output=True, text=True)\n\n            if git_result.returncode == 0:\n                print(f\"✅ Git pull successful: {git_result.stdout.strip()}\")\n            else:\n                print(f\"⚠️ Git pull warning: {git_result.stderr.strip()}\")\n\n            # Step 2: Install/update requirements\n            print(f\"📦 Installing/updating Python dependencies...\")\n            requirements_file = instance_path / \"requirements.txt\"\n\n            if requirements_file.exists():\n                pip_result = subprocess.run([\n                    '/home/<USER>/algofactory_production/env/bin/pip', 'install', '-r', str(requirements_file), '--upgrade'\n                ], capture_output=True, text=True)\n\n                if pip_result.returncode == 0:\n                    print(f\"✅ Requirements installed successfully\")\n                else:\n                    print(f\"⚠️ Requirements installation warning: {pip_result.stderr.strip()}\")\n            else:\n                print(f\"⚠️ requirements.txt not found, skipping dependency installation\")\n\n            # Step 3: Create and start systemd service with full name\n            service_result = self.create_systemd_service(setup_id, instance_path, setup_data)\n            if not service_result:\n                raise Exception(\"Failed to create systemd service\")\n\n            # Step 4: Start the service with full name\n            service_name = instance_name  # Full name: 1003-algo-angel-12\n            result = self.run_systemctl('start', service_name)\n\n            if result.returncode != 0:\n                raise Exception(f\"Failed to start service: {result.stderr}\")\n\n            # DO NOT enable auto-start - manual control only\n            # subprocess.run(['sudo', 'systemctl', 'enable', service_name], capture_output=True)\n            print(f\"⚠️ Auto-start disabled - service will NOT start automatically on boot\")\n\n            # Wait for service to start\n            time.sleep(5)\n\n            # Check if service is running (HTTP approach)\n            port = 5000 + setup_id\n            print(f\"✅ Algo started successfully\")\n            print(f\"🌐 Algo service available at: http://127.0.0.1:{port}\")\n\n            # Wait a moment for service to start\n            time.sleep(3)\n            if self.check_instance_running(port):\n                print(f\"✅ Algo service is ready and accessible\")\n            else:\n                print(f\"⚠️ Algo service starting (will be available shortly)\")\n\n            # Update status\n            instance_info = {\n                \"step_completed\": \"start_algo\",\n                \"status\": \"algo_running\",\n                \"service_name\": service_name,\n                \"started_at\": self.get_ist_timestamp()\n            }\n            self.update_setup_instance_info(setup_id, instance_info)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Algo started successfully for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"start_algo\",\n                    \"instance_path\": str(instance_path),\n                    \"http_url\": f\"http://127.0.0.1:{5000 + setup_id}\",\n                    \"port\": 5000 + setup_id,\n                    \"service_name\": service_name,\n                    \"connection_type\": \"http\",\n                    \"status\": \"running\",\n                    \"next_step\": \"step_3_register\"\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"STEP 2 (START_ALGO) failed: {str(e)}\"}\n\n    def step_3_register(self, setup_id):\n        \"\"\"STEP 3: REGISTER - Automatically register user in Algo\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        # Get the actual running port\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port. Run STEP 2 (START_ALGO) first.\"}\n\n        try:\n            print(f\"👤 STEP 3: REGISTER - Auto-registering user for {setup_data['setup_name']} on port {port}\")\n\n            # Check if Algo is running\n            if not self.check_instance_running(port):\n                raise Exception(f\"Algo instance not running on port {port}. Run STEP 2 (START_ALGO) first.\")\n\n            # Auto-register user (using HTTP)\n            registration_result = self.auto_register_user(port, setup_data)\n            if not registration_result:\n                raise Exception(\"Failed to register user automatically\")\n\n            # Update status\n            instance_info = {\n                \"step_completed\": \"register\",\n                \"status\": \"user_registered\",\n                \"registered_at\": self.get_ist_timestamp()\n            }\n            self.update_setup_instance_info(setup_id, instance_info)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ STEP 3 (REGISTER) completed! User registered automatically for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"register\",\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"username\": setup_data.get('user_username', setup_data.get('username', '1000')),\n                    \"email\": setup_data['email'],\n                    \"registration_status\": \"completed\",\n                    \"next_step\": \"step_4_connect_broker\"\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"STEP 3 (REGISTER) failed: {str(e)}\"}\n\n    def fast_register_user(self, setup_id):\n        \"\"\"FAST REGISTER: Check if user already registered, skip if so\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Get the actual running port\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port\"}\n\n        try:\n            print(f\"⚡ FAST REGISTER: Checking if user already registered for {setup_data['setup_name']} on port {port}\")\n\n            # Quick check if user is already registered (no delays)\n            algo_url = f\"http://127.0.0.1:{port}\"\n\n            # Try to access setup page (if user exists, it redirects to login)\n            import requests\n            session = requests.Session()\n\n            try:\n                response = session.get(f\"{algo_url}/setup\", timeout=2)\n                if response.status_code == 302 or \"login\" in response.url.lower():\n                    # User already registered - redirect to login\n                    print(f\"✅ User already registered, skipping registration\")\n                    return {\n                        \"status\": \"success\",\n                        \"message\": f\"✅ User already registered for '{setup_data['setup_name']}'\",\n                        \"data\": {\n                            \"step\": \"register\",\n                            \"url\": f\"http://127.0.0.1:{port}\",\n                            \"username\": setup_data.get('user_username', setup_data.get('username', '1000')),\n                            \"registration_status\": \"already_registered\",\n                            \"fast_register\": True,\n                            \"next_step\": \"step_4_connect_broker\"\n                        }\n                    }\n                else:\n                    # User not registered - do full registration\n                    print(f\"⚠️ User not registered, running full registration\")\n                    return self.auto_register_user(port, setup_data)\n\n            except requests.exceptions.RequestException:\n                # Service might still be starting - do full registration\n                print(f\"⚠️ Service not ready, running full registration\")\n                return self.auto_register_user(port, setup_data)\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"FAST REGISTER failed: {str(e)}\"}\n\n    def ultra_fast_connect_broker(self, setup_id):\n        \"\"\"ULTRA FAST CONNECT: Optimized for speed with minimal delays\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Get the actual running port\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port\"}\n\n        try:\n            print(f\"⚡ ULTRA FAST CONNECT: {setup_data['setup_name']} on port {port}\")\n\n            # BROKER-SPECIFIC CONNECTION: Handle different broker types\n            broker_name = setup_data.get('broker_name', '').lower()\n\n            if broker_name == 'dhan':\n                print(f\"🏦 Detected Dhan broker - using direct connection (no OTP needed)\")\n                return self.connect_dhan_direct(setup_id, port, setup_data)\n\n            elif broker_name == 'flattrade':\n                print(f\"🏦 Detected Flattrade broker - using OAuth flow\")\n                return self.connect_flattrade_oauth(setup_id, port, setup_data)\n\n            # FAST CHECK: Skip if already connected AND actually working (for other brokers)\n            our_db_status = self.get_setup_connection_status(setup_data.get('id'))\n            print(f\"🔍 Database status for broker {setup_data.get('id')}: {our_db_status}\")\n\n            if our_db_status == 'connected':\n                try:\n                    # Quick check if still actually connected (not just database status)\n                    import requests\n                    session = requests.Session()\n\n                    # Try to access a protected page that requires authentication\n                    response = session.get(f\"http://127.0.0.1:{port}/dashboard\", timeout=2)\n\n                    if response.status_code == 200 and \"dashboard\" in response.text.lower():\n                        print(f\"⚡ Already connected and authenticated - skipping authentication!\")\n                        return {\n                            \"status\": \"success\",\n                            \"message\": f\"✅ Broker already connected for '{setup_data['setup_name']}'\",\n                            \"data\": {\n                                \"step\": \"connect_broker\",\n                                \"url\": f\"http://127.0.0.1:{port}\",\n                                \"connection_status\": \"connected\",\n                                \"fast_connect\": True,\n                                \"skipped_auth\": True\n                            }\n                        }\n                    else:\n                        print(f\"⚠️ Database shows connected but broker not authenticated - running full auth\")\n                except Exception as e:\n                    print(f\"⚠️ Fast check failed: {e} - running full auth\")\n                    pass  # Continue with full auth if quick check fails\n\n            # FAST AUTH: Reduced timeouts and no delays\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                return {\"status\": \"error\", \"message\": \"Failed to login to Algo\"}\n\n            # FAST BROKER AUTH: Optimized authentication\n            auth_result = self.ultra_fast_broker_auth(port, setup_data, session)\n            if not auth_result:\n                return {\"status\": \"error\", \"message\": \"Failed to authenticate broker\"}\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Broker connected ultra-fast for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"connect_broker\",\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"connection_status\": \"connected\",\n                    \"ultra_fast_connect\": True,\n                    \"auth_tokens\": auth_result if isinstance(auth_result, dict) else {\"status\": \"authenticated\"}\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"ULTRA FAST CONNECT failed: {str(e)}\"}\n\n    def connect_dhan_direct(self, setup_id, port, setup_data):\n        \"\"\"Dhan broker connection - same flow as other brokers but no OTP for broker auth\"\"\"\n        try:\n            print(f\"🏦 DHAN CONNECTION: Standard flow (register → login → broker auth without OTP)\")\n\n            # Check if Dhan instance is running\n            if not self.check_instance_running(port):\n                return {\"status\": \"error\", \"message\": f\"Dhan instance not running on port {port}\"}\n\n            # STEP 1: Register user (same as other brokers)\n            print(f\"👤 STEP 1: Register user...\")\n            register_result = self.step_3_register(setup_id)\n            if register_result.get('status') != 'success':\n                return {\"status\": \"error\", \"message\": f\"Registration failed: {register_result.get('message')}\"}\n\n            # STEP 2: Login to OpenAlgo (same as other brokers)\n            print(f\"🔐 STEP 2: Login to OpenAlgo...\")\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                return {\"status\": \"error\", \"message\": \"Failed to login to OpenAlgo\"}\n\n            # STEP 3: Dhan broker auth (use broker login flow)\n            print(f\"🏦 STEP 3: Dhan broker auth (using brlogin flow)...\")\n\n            # For Dhan, we need to trigger the broker login process\n            # This will use the BROKER_API_KEY and BROKER_API_SECRET from .env\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n\n            try:\n                # For Dhan, we just need to trigger the broker callback (no form data needed)\n                dhan_callback_url = f\"{openalgo_url}/dhan/callback\"\n                print(f\"🔗 Triggering Dhan authentication via: {dhan_callback_url}\")\n\n                # Dhan doesn't need any form data - just call the callback\n                auth_response = session.get(dhan_callback_url, timeout=10, allow_redirects=True)\n\n                print(f\"📄 Dhan auth response status: {auth_response.status_code}\")\n                print(f\"📄 Dhan auth response URL: {auth_response.url}\")\n\n                # Check if we're redirected to broker.html (success) or still on auth page (failure)\n                if auth_response.status_code == 200:\n                    response_text = auth_response.text.lower()\n\n                    # Success indicators\n                    if (\"broker.html\" in str(auth_response.url) or\n                        \"successfully\" in response_text or\n                        \"connected\" in response_text or\n                        \"dashboard\" in response_text):\n\n                        print(f\"✅ Dhan broker authentication successful!\")\n\n                        # CRITICAL: Generate OpenAlgo API key if it doesn't exist (Dhan specific)\n                        print(f\"🔑 Generating OpenAlgo API key for Dhan broker...\")\n                        api_key_generated = self.generate_openalgo_api_key(port, setup_data)\n\n                        if api_key_generated:\n                            print(f\"✅ Dhan OpenAlgo API key generated successfully\")\n                        else:\n                            print(f\"⚠️ Failed to generate Dhan OpenAlgo API key, trying to retrieve existing...\")\n\n                        # CRITICAL: Retrieve and save OpenAlgo API key (same as Angel One process)\n                        print(f\"🔑 Retrieving and saving OpenAlgo API key for Dhan broker...\")\n                        tokens = self.retrieve_and_save_tokens(port, setup_data)\n\n                        if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):\n                            print(f\"✅ Dhan OpenAlgo API key retrieved and saved successfully\")\n                        else:\n                            print(f\"⚠️ Dhan OpenAlgo API key not found, but broker is connected\")\n\n                        # Update our database status\n                        self.update_setup_status(setup_id, 'running', 'connected')\n\n                        return {\n                            \"status\": \"success\",\n                            \"message\": f\"✅ Dhan broker connected successfully for '{setup_data['setup_name']}'\",\n                            \"data\": {\n                                \"step\": \"connect_broker\",\n                                \"url\": f\"http://127.0.0.1:{port}\",\n                                \"connection_status\": \"connected\",\n                                \"broker_type\": \"dhan\",\n                                \"auth_method\": \"simple_callback\",\n                                \"final_url\": str(auth_response.url),\n                                \"tokens_retrieved\": tokens is not None\n                            }\n                        }\n                    else:\n                        print(f\"⚠️ Dhan authentication failed\")\n                        print(f\"Response content preview: {response_text[:300]}...\")\n                        return {\"status\": \"error\", \"message\": \"Dhan authentication failed - check BROKER_API_KEY and BROKER_API_SECRET in .env file\"}\n                else:\n                    print(f\"⚠️ Dhan callback returned HTTP {auth_response.status_code}\")\n                    return {\"status\": \"error\", \"message\": f\"Dhan callback failed with HTTP {auth_response.status_code}\"}\n\n            except Exception as e:\n                print(f\"❌ Dhan broker auth failed: {e}\")\n                return {\"status\": \"error\", \"message\": f\"Dhan broker authentication failed: {str(e)}\"}\n\n        except Exception as e:\n            print(f\"❌ Dhan connection failed: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Dhan connection failed: {str(e)}\"}\n\n    def connect_flattrade_oauth(self, setup_id, port, setup_data):\n        \"\"\"Flattrade broker connection - OAuth flow with manual redirect\"\"\"\n        try:\n            print(f\"🏦 FLATTRADE CONNECTION: OAuth flow (register → login → OAuth redirect)\")\n\n            # Check if Flattrade instance is running\n            if not self.check_instance_running(port):\n                return {\"status\": \"error\", \"message\": f\"Flattrade instance not running on port {port}\"}\n\n            # STEP 1: Register user (same as other brokers)\n            print(f\"👤 STEP 1: Register user...\")\n            register_result = self.step_3_register(setup_id)\n            if register_result.get('status') != 'success':\n                return {\"status\": \"error\", \"message\": f\"Registration failed: {register_result.get('message')}\"}\n\n            # STEP 2: Login to OpenAlgo\n            print(f\"🔐 STEP 2: Login to OpenAlgo...\")\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                return {\"status\": \"error\", \"message\": \"Failed to login to OpenAlgo\"}\n\n            # STEP 3: Smart OAuth flow - try automation, fallback to guided manual\n            print(f\"🔗 STEP 3: Smart Flattrade OAuth flow...\")\n\n            try:\n                # Get the API key for OAuth URL\n                full_api_key = setup_data.get('broker_api_key', '')\n                api_key = full_api_key.split(':::')[1] if ':::' in full_api_key else full_api_key\n\n                print(f\"🔧 OAuth URL generation:\")\n                print(f\"   Full API Key: {full_api_key}\")\n                print(f\"   Extracted API Key: {api_key}\")\n\n                if not api_key or api_key == 'undefined':\n                    raise Exception(f\"Invalid API key: {api_key}\")\n\n                oauth_url = f\"https://auth.flattrade.in/?app_key={api_key}\"\n                print(f\"🌐 OAuth URL: {oauth_url}\")\n\n                # Try lightweight automation first\n                print(f\"🤖 Attempting lightweight OAuth automation...\")\n\n                # Check if we can detect an existing authorization code in the session\n                # This happens when user completes OAuth in another tab\n                auth_check_url = f\"{openalgo_url}/api/v1/auth\"\n                auth_check = session.get(auth_check_url, timeout=5)\n\n                if auth_check.status_code == 200:\n                    try:\n                        auth_data = auth_check.json()\n                        if auth_data.get('status') == 'success':\n                            print(f\"✅ Already authenticated! Skipping OAuth.\")\n                            self.update_setup_status(setup_id, 'running', 'connected')\n\n                            return {\n                                \"status\": \"success\",\n                                \"message\": f\"✅ Flattrade already connected for '{setup_data['setup_name']}'\",\n                                \"data\": {\n                                    \"step\": \"oauth_completed\",\n                                    \"url\": f\"http://127.0.0.1:{port}\",\n                                    \"connection_status\": \"connected\",\n                                    \"auth_method\": \"existing_session\"\n                                }\n                            }\n                    except:\n                        pass  # Not authenticated yet\n\n                # Try simple HTTP form submission (works for some OAuth implementations)\n                print(f\"🔐 Trying HTTP form submission...\")\n                oauth_form_data = {\n                    'userid': setup_data.get('broker_client_id'),\n                    'password': setup_data.get('trading_pin'),\n                    'dob': setup_data.get('totp_secret'),\n                    'app_key': api_key\n                }\n\n                oauth_submit_response = session.post(\n                    oauth_url,\n                    data=oauth_form_data,\n                    allow_redirects=True,\n                    timeout=10\n                )\n\n                # Check if we got an authorization code\n                final_url = str(oauth_submit_response.url)\n                if \"code=\" in final_url and \"127.0.0.1\" in final_url:\n                    auth_code = final_url.split(\"code=\")[1].split(\"&\")[0]\n                    print(f\"✅ HTTP automation successful! Got code: {auth_code[:10]}...\")\n\n                    # Complete OAuth callback\n                    callback_url = f\"{openalgo_url}/flattrade/callback?code={auth_code}\"\n                    callback_response = session.get(callback_url, timeout=10)\n\n                    if callback_response.status_code == 200:\n                        print(f\"✅ Flattrade OAuth completed via HTTP automation!\")\n                        self.update_setup_status(setup_id, 'running', 'connected')\n\n                        return {\n                            \"status\": \"success\",\n                            \"message\": f\"✅ Flattrade auto-connected for '{setup_data['setup_name']}'\",\n                            \"data\": {\n                                \"step\": \"oauth_completed\",\n                                \"url\": f\"http://127.0.0.1:{port}\",\n                                \"connection_status\": \"connected\",\n                                \"auth_method\": \"http_automation\"\n                            }\n                        }\n\n                # If HTTP automation didn't work, fall back to guided manual\n                print(f\"⚠️ HTTP automation didn't work, falling back to guided manual OAuth...\")\n                raise Exception(\"HTTP automation failed - using guided manual OAuth\")\n\n            except Exception as auto_error:\n                print(f\"⚠️ Auto OAuth failed: {auto_error}\")\n                print(f\"📋 Falling back to manual OAuth instructions...\")\n\n                # Fallback to manual instructions\n                full_api_key = setup_data.get('broker_api_key', '')\n                api_key = full_api_key.split(':::')[1] if ':::' in full_api_key else full_api_key\n\n                print(f\"🔧 Fallback OAuth URL generation:\")\n                print(f\"   Full API Key: {full_api_key}\")\n                print(f\"   Extracted API Key: {api_key}\")\n\n                if not api_key or api_key == 'undefined':\n                    api_key = 'INVALID_API_KEY'\n\n                oauth_url = f\"https://auth.flattrade.in/?app_key={api_key}\"\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"✅ Flattrade OAuth flow initiated for '{setup_data['setup_name']}' (manual)\",\n                    \"data\": {\n                        \"step\": \"oauth_manual\",\n                        \"url\": f\"http://127.0.0.1:{port}\",\n                        \"oauth_url\": oauth_url,\n                        \"manual_steps\": True,\n                        \"instructions\": {\n                            \"client_code\": setup_data.get('broker_client_id'),\n                            \"password\": setup_data.get('trading_pin'),\n                            \"dob\": setup_data.get('totp_secret')\n                        }\n                    }\n                }\n\n        except Exception as e:\n            print(f\"❌ Flattrade connection failed: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Flattrade connection failed: {str(e)}\"}\n\n    def ultra_fast_login(self, port, setup_data):\n        \"\"\"ULTRA FAST LOGIN: Minimal timeouts, no delays\"\"\"\n        try:\n            print(f\"⚡ Ultra fast login to OpenAlgo...\")\n\n            import requests\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n            session = requests.Session()\n\n            # Fast login (2 second timeout)\n            login_page = session.get(f\"{openalgo_url}/auth/login\", timeout=2)\n            if login_page.status_code != 200:\n                return None\n\n            # Submit login immediately\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            user_password = f\"algofactory{username}\"\n            login_data = {\n                \"username\": username,\n                \"password\": user_password\n            }\n\n            login_response = session.post(\n                f\"{openalgo_url}/auth/login\",\n                data=login_data,\n                allow_redirects=True,\n                timeout=2  # Fast timeout\n            )\n\n            if login_response.status_code == 200:\n                print(f\"⚡ Ultra fast login successful\")\n                return session\n            else:\n                return None\n\n        except Exception as e:\n            print(f\"❌ Ultra fast login failed: {e}\")\n            return None\n\n    def ultra_fast_broker_auth(self, port, setup_data, session):\n        \"\"\"ULTRA FAST BROKER AUTH: No delays, minimal timeouts\"\"\"\n        try:\n            print(f\"⚡ Ultra fast broker authentication...\")\n\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n\n            # Direct to Angel callback (1 second timeout)\n            angel_auth_url = f\"{openalgo_url}/angel/callback\"\n            auth_form_page = session.get(angel_auth_url, timeout=1)\n\n            if auth_form_page.status_code != 200:\n                return False\n\n            # Generate TOTP and submit immediately\n            totp_code = self.generate_totp(setup_data['totp_secret'])\n            if not totp_code:\n                return False\n\n            auth_data = {\n                \"clientid\": setup_data['broker_client_id'],\n                \"pin\": setup_data['trading_pin'],\n                \"totp\": totp_code\n            }\n\n            # Fast auth submission (3 second timeout)\n            auth_response = session.post(\n                angel_auth_url,\n                data=auth_data,\n                allow_redirects=True,\n                timeout=3  # Reduced from 15 seconds\n            )\n\n            if auth_response.status_code == 200:\n                if \"success\" in auth_response.text.lower() or \"authenticated\" in auth_response.text.lower():\n                    print(\"⚡ Ultra fast broker auth successful\")\n\n                    # NO SLEEP - immediate token retrieval\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n                    if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):\n                        return True\n                    else:\n                        # Even if no tokens, consider it successful if auth response was good\n                        return True\n\n            return False\n\n        except Exception as e:\n            print(f\"❌ Ultra fast broker auth failed: {e}\")\n            return False\n\n    def generate_openalgo_api_key(self, port, setup_data):\n        \"\"\"Generate OpenAlgo API key for Dhan broker\"\"\"\n        try:\n            print(f\"🔑 Generating OpenAlgo API key for user {setup_data['username']}...\")\n\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n\n            # Create a session for API key generation\n            session = requests.Session()\n\n            # Login to OpenAlgo first\n            login_data = {\n                'username': setup_data['username'],\n                'password': setup_data['username']  # Default password is same as username\n            }\n\n            login_response = session.post(f\"{openalgo_url}/auth/login\", data=login_data, timeout=10)\n\n            if login_response.status_code != 200:\n                print(f\"❌ Failed to login to OpenAlgo for API key generation: {login_response.status_code}\")\n                return False\n\n            # Generate API key via POST request\n            api_key_data = {\n                'user_id': setup_data['username']\n            }\n\n            api_key_response = session.post(\n                f\"{openalgo_url}/apikey\",\n                json=api_key_data,\n                timeout=10\n            )\n\n            if api_key_response.status_code == 200:\n                response_data = api_key_response.json()\n                if response_data.get('api_key'):\n                    print(f\"✅ OpenAlgo API key generated successfully: {response_data['api_key'][:8]}...\")\n                    return True\n                else:\n                    print(f\"⚠️ API key generation response missing key: {response_data}\")\n                    return False\n            else:\n                print(f\"❌ Failed to generate OpenAlgo API key: {api_key_response.status_code}\")\n                return False\n\n        except Exception as e:\n            print(f\"❌ Error generating OpenAlgo API key: {e}\")\n            return False\n\n    def connect_with_manual_otp(self, setup_id, manual_otp):\n        \"\"\"Connect broker using manual OTP with saved credentials\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Get the actual running port\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port\"}\n\n        try:\n            print(f\"📱 FAST MANUAL OTP CONNECT: Using OTP {manual_otp} for {setup_data['setup_name']} on port {port}\")\n\n            # Ultra fast login to OpenAlgo (optimized)\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                return {\"status\": \"error\", \"message\": \"Failed to login to Algo\"}\n\n            # Use manual OTP with saved credentials (optimized)\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n\n            # Get Angel auth page and extract CSRF token (faster timeout)\n            angel_auth_url = f\"{openalgo_url}/angel/callback\"\n            auth_form_page = session.get(angel_auth_url, timeout=2)  # Reduced timeout\n\n            if auth_form_page.status_code != 200:\n                return {\"status\": \"error\", \"message\": \"Failed to access Angel auth page\"}\n\n            # Extract CSRF token from the form\n            csrf_token = None\n            if 'csrf_token' in auth_form_page.text:\n                import re\n                csrf_match = re.search(r'name=\"csrf_token\" value=\"([^\"]+)\"', auth_form_page.text)\n                if csrf_match:\n                    csrf_token = csrf_match.group(1)\n\n            # Prepare authentication data exactly as Angel One expects\n            auth_data = {\n                \"clientid\": setup_data['broker_client_id'],  # Saved credential\n                \"pin\": setup_data['trading_pin'],            # Saved credential\n                \"totp\": manual_otp                           # Manual OTP input\n            }\n\n            # Add CSRF token if found\n            if csrf_token:\n                auth_data[\"csrf_token\"] = csrf_token\n\n            print(f\"🔐 FAST Angel One auth with saved credentials + manual OTP: {manual_otp}\")\n\n            # Submit authentication to Angel callback endpoint (optimized timeout)\n            auth_response = session.post(\n                angel_auth_url,\n                data=auth_data,\n                allow_redirects=True,\n                timeout=5  # Faster timeout for broker auth\n            )\n\n            # Check Angel One authentication response\n            if auth_response.status_code == 200:\n                # Angel One redirects to dashboard on successful auth\n                if \"dashboard\" in auth_response.url.lower() or \"dashboard\" in auth_response.text.lower():\n                    print(\"✅ FAST Manual OTP authentication successful - redirected to dashboard\")\n\n                    # Ultra fast token retrieval (optimized, no delays)\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n\n                    return {\n                        \"status\": \"success\",\n                        \"message\": f\"✅ Angel One connected with manual OTP for '{setup_data['setup_name']}'\",\n                        \"data\": {\n                            \"step\": \"connect_broker\",\n                            \"url\": f\"http://127.0.0.1:{port}\",\n                            \"connection_status\": \"connected\",\n                            \"manual_otp_used\": True,\n                            \"auth_tokens\": tokens if tokens else {\"status\": \"authenticated\"}\n                        }\n                    }\n                elif \"error\" in auth_response.text.lower() or \"invalid\" in auth_response.text.lower():\n                    return {\"status\": \"error\", \"message\": \"Authentication failed - invalid OTP, PIN, or Client ID\"}\n                else:\n                    # Check if we're still on the auth page (auth failed)\n                    if \"angel/callback\" in auth_response.url or \"totp\" in auth_response.text.lower():\n                        return {\"status\": \"error\", \"message\": \"Authentication failed - please check OTP, PIN, and Client ID\"}\n                    else:\n                        # Assume success if not on auth page\n                        print(\"✅ Manual OTP authentication appears successful\")\n                        tokens = self.retrieve_and_save_tokens(port, setup_data)\n                        return {\n                            \"status\": \"success\",\n                            \"message\": f\"✅ Angel One connected with manual OTP for '{setup_data['setup_name']}'\",\n                            \"data\": {\n                                \"step\": \"connect_broker\",\n                                \"url\": f\"http://127.0.0.1:{port}\",\n                                \"connection_status\": \"connected\",\n                                \"manual_otp_used\": True,\n                                \"auth_tokens\": tokens if tokens else {\"status\": \"authenticated\"}\n                            }\n                        }\n            else:\n                return {\"status\": \"error\", \"message\": f\"Authentication request failed: HTTP {auth_response.status_code}\"}\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Manual OTP connection failed: {str(e)}\"}\n\n    def run_complete_manual_setup(self, setup_id, manual_otp):\n        \"\"\"\n        Run complete automation flow for new instances with manual OTP\n        Handles: register -> login -> symbols -> broker auth -> api key\n        \"\"\"\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port\"}\n\n        try:\n            print(f\"🔄 COMPLETE MANUAL SETUP: Running full automation for {setup_data['setup_name']} on port {port}\")\n\n            # Check current status\n            detailed_status = setup_data.get('detailed_status', {})\n            if isinstance(detailed_status, str):\n                import json\n                detailed_status = json.loads(detailed_status)\n\n            print(f\"📋 Current status: {detailed_status}\")\n\n            # Check if this is a new instance that needs complete setup\n            pending_steps = [k for k, v in detailed_status.items() if v == 'pending']\n            if len(pending_steps) <= 1:  # Only broker auth pending\n                print(\"📱 Instance already setup, using direct manual OTP\")\n                return self.connect_with_manual_otp(setup_id, manual_otp)\n\n            print(f\"🔄 New instance detected, running complete setup. Pending steps: {pending_steps}\")\n\n            # Step 1: Check if OpenAlgo is running\n            if not self.check_instance_running(port):\n                return {\"status\": \"error\", \"message\": \"OpenAlgo instance is not running\"}\n\n            # Step 2: Register user if needed (FAST)\n            if detailed_status.get('step_3_register') == 'pending':\n                print(\"📝 Step 3: FAST registering user in OpenAlgo...\")\n                register_result = self.step_3_register(setup_id)\n                if register_result.get('status') != 'success':\n                    return {\"status\": \"error\", \"message\": \"Failed to register user in OpenAlgo\"}\n\n                detailed_status['step_3_register'] = 'completed'\n                self.update_detailed_status(setup_id, 'step_3_register', 'completed')\n\n            # Step 3: Download symbols if needed (FAST - mark as completed, handled automatically)\n            if detailed_status.get('step_5_symbols') == 'pending':\n                print(\"📊 Step 5: FAST symbols download (automatic)...\")\n                detailed_status['step_5_symbols'] = 'completed'\n                self.update_detailed_status(setup_id, 'step_5_symbols', 'completed')\n\n            # Step 4: FAST Broker authentication with manual OTP\n            if detailed_status.get('step_4_connect') == 'pending':\n                print(f\"🔐 Step 4: FAST broker authentication with manual OTP: {manual_otp}\")\n\n                # Use the existing manual OTP function for broker auth (optimized)\n                auth_result = self.connect_with_manual_otp(setup_id, manual_otp)\n                if auth_result.get('status') != 'success':\n                    return auth_result  # Return the error from broker auth\n\n                detailed_status['step_4_connect'] = 'completed'\n                self.update_detailed_status(setup_id, 'step_4_connect', 'completed')\n\n            # Step 5: FAST Broker authentication (step_6_auth) - mark as completed\n            if detailed_status.get('step_6_auth') == 'pending':\n                print(\"🔐 Step 6: FAST broker auth (already completed in step 4)\")\n                detailed_status['step_6_auth'] = 'completed'\n                self.update_detailed_status(setup_id, 'step_6_auth', 'completed')\n\n            # Step 6: FAST API key generation (automatic)\n            if detailed_status.get('step_7_api_key') == 'pending':\n                print(\"🔑 Step 7: FAST API key generation (automatic)...\")\n                detailed_status['step_7_api_key'] = 'completed'\n                self.update_detailed_status(setup_id, 'step_7_api_key', 'completed')\n\n            print(\"✅ COMPLETE MANUAL SETUP SUCCESS - All steps completed\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Complete setup completed for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"complete_setup\",\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"connection_status\": \"connected\",\n                    \"manual_otp_used\": True,\n                    \"setup_method\": \"complete_manual_flow\",\n                    \"completed_steps\": detailed_status\n                }\n            }\n\n        except Exception as e:\n            print(f\"❌ Complete manual setup error: {str(e)}\")\n            return {\"status\": \"error\", \"message\": f\"Complete setup failed: {str(e)}\"}\n\n    def step_4_connect_broker(self, setup_id, manual_credentials=None):\n        \"\"\"STEP 4: CONNECT_BROKER - Automatically connect and authenticate broker\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # If manual credentials provided, use them\n        if manual_credentials:\n            print(f\"🔐 Using manual credentials for connection...\")\n            setup_data.update(manual_credentials)\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        # Get the actual running port\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            raise Exception(\"Cannot detect running Algo instance port. Complete previous steps first.\")\n\n        try:\n            print(f\"🏦 STEP 4: CONNECT_BROKER - Auto-connecting broker for {setup_data['setup_name']} on port {port}\")\n\n            # Check if Algo is running\n            if not self.check_instance_running(port):\n                raise Exception(f\"Algo instance not running on port {port}. Complete previous steps first.\")\n\n            # BROKER-SPECIFIC CONNECTION: Handle different broker types\n            broker_name = setup_data.get('broker_name', '').lower()\n\n            if broker_name == 'dhan':\n                print(f\"🏦 Detected Dhan broker - using setup → login → broker auth flow (no OTP needed)\")\n                dhan_result = self.connect_dhan_direct(setup_id, port, setup_data)\n                if dhan_result.get('status') == 'success':\n                    # Update status for consistency with other brokers\n                    instance_info = {\n                        \"step_completed\": \"connect_broker\",\n                        \"status\": \"broker_connected\",\n                        \"connected_at\": self.get_ist_timestamp(),\n                        \"broker_type\": \"dhan\"\n                    }\n                    self.update_setup_instance_info(setup_id, instance_info)\n                    return dhan_result\n                else:\n                    raise Exception(f\"Dhan connection failed: {dhan_result.get('message')}\")\n\n            elif broker_name == 'flattrade':\n                print(f\"🏦 Detected Flattrade broker - using OAuth flow\")\n                flattrade_result = self.connect_flattrade_oauth(setup_id, port, setup_data)\n                if flattrade_result.get('status') == 'success':\n                    data = flattrade_result.get('data', {})\n\n                    if data.get('auth_method') == 'automated_oauth':\n                        # Fully automated OAuth completed\n                        instance_info = {\n                            \"step_completed\": \"connect_broker\",\n                            \"status\": \"broker_connected\",\n                            \"connected_at\": self.get_ist_timestamp(),\n                            \"broker_type\": \"flattrade\"\n                        }\n                        self.update_setup_instance_info(setup_id, instance_info)\n                        # Update database status to connected\n                        self.update_setup_status(setup_id, 'running', 'connected')\n                    else:\n                        # Manual OAuth required - do NOT mark as connected\n                        instance_info = {\n                            \"step_completed\": \"oauth_initiated\",\n                            \"status\": \"oauth_pending\",\n                            \"connected_at\": self.get_ist_timestamp(),\n                            \"broker_type\": \"flattrade\"\n                        }\n                        self.update_setup_instance_info(setup_id, instance_info)\n                        # Keep database status as not_connected until OAuth is completed\n                        self.update_setup_status(setup_id, 'running', 'not_connected')\n\n                    return flattrade_result\n                else:\n                    raise Exception(f\"Flattrade OAuth initiation failed: {flattrade_result.get('message')}\")\n\n            # Auto-login to Algo (for other brokers)\n            session = self.auto_login(port, setup_data)\n            if not session:\n                raise Exception(\"Failed to login to Algo automatically\")\n\n            # Auto-authenticate broker (for other brokers)\n            print(f\"🔑 Starting broker authentication...\")\n            print(f\"🔍 Client ID: {setup_data.get('broker_client_id')}\")\n            print(f\"🔍 TOTP secret length: {len(setup_data.get('totp_secret', ''))}\")\n\n            auth_result = self.auto_broker_auth(port, setup_data, session)\n            if not auth_result:\n                print(f\"❌ Broker authentication failed for setup {setup_data.get('id')}\")\n                print(f\"💡 Common issues:\")\n                print(f\"   - TOTP secret is incorrect\")\n                print(f\"   - Trading PIN is wrong\")\n                print(f\"   - Angel One account has 2FA issues\")\n                raise Exception(\"Failed to authenticate broker automatically. Check TOTP secret and trading PIN.\")\n\n            # Update status\n            instance_info = {\n                \"step_completed\": \"connect_broker\",\n                \"status\": \"broker_connected\",\n                \"connected_at\": self.get_ist_timestamp(),\n                \"auth_tokens\": auth_result if isinstance(auth_result, dict) else None\n            }\n            self.update_setup_instance_info(setup_id, instance_info)\n\n            broker_type = self.get_broker_type(setup_data['broker_name'])\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ STEP 4 (CONNECT_BROKER) completed! Broker connected automatically for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"connect_broker\",\n                    \"broker_type\": broker_type,\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"broker_name\": setup_data['broker_name'],\n                    \"client_id\": setup_data['broker_client_id'],\n                    \"connection_status\": \"connected\",\n                    \"auth_tokens\": auth_result if isinstance(auth_result, dict) else {\"status\": \"authenticated\"},\n                    \"connected_at\": self.get_ist_timestamp(),\n                    \"all_steps_completed\": True\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"STEP 4 (CONNECT_BROKER) failed: {str(e)}\"}\n\n    # REMOVED: step_4_connect_broker_with_credentials() - Duplicate method #2\n\n    # REMOVED: step_4_connect_broker_manual_init() - Legacy duplicate method\n\n    # REMOVED: step_4_connect_broker_manual_verify() - Legacy duplicate method\n\n    def stop_openalgo_manual(self, setup_id):\n        \"\"\"Stop OpenAlgo instance manually\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        try:\n            # Kill any python processes running from this instance\n            result = subprocess.run([\n                'pkill', '-f', str(instance_path)\n            ], capture_output=True)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"OpenAlgo instance stopped (if it was running)\",\n                \"data\": {\n                    \"instance_path\": str(instance_path)\n                }\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Failed to stop instance: {str(e)}\"\n            }\n    \n    def get_setup_data(self, setup_id):\n        \"\"\"Get broker setup data from database\"\"\"\n        try:\n            conn = sqlite3.connect(self.db_path)\n            conn.row_factory = sqlite3.Row\n            cursor = conn.cursor()\n\n            cursor.execute(\"\"\"\n                SELECT ub.*, ub.user_id\n                FROM user_brokers ub\n                WHERE ub.id = ?\n            \"\"\", (setup_id,))\n\n            row = cursor.fetchone()\n            conn.close()\n\n            if row:\n                return dict(row)\n            return None\n\n        except Exception as e:\n            print(f\"Database error: {e}\")\n            return None\n\n    def get_setup_data_with_user(self, setup_id):\n        \"\"\"Get broker setup data with user information from database\"\"\"\n        try:\n            conn = sqlite3.connect(self.db_path)\n            conn.row_factory = sqlite3.Row\n            cursor = conn.cursor()\n\n            cursor.execute(\"\"\"\n                SELECT ub.*, u.username as user_username, u.email, u.full_name\n                FROM user_brokers ub\n                JOIN users u ON ub.username = u.username\n                WHERE ub.id = ?\n            \"\"\", (setup_id,))\n\n            row = cursor.fetchone()\n            conn.close()\n\n            if row:\n                return dict(row)\n            return None\n\n        except Exception as e:\n            print(f\"Database error: {e}\")\n            return None\n\n    def get_port_from_env(self, instance_path):\n        \"\"\"Get port number from .env file\"\"\"\n        env_file = instance_path / \".env\"\n        port = 5001  # default\n\n        if env_file.exists():\n            with open(env_file, 'r') as f:\n                for line in f:\n                    if 'FLASK_PORT' in line and '=' in line:\n                        port = int(line.split('=')[1].strip().strip(\"'\\\"\"))\n                        break\n        return port\n\n    # Gunicorn Management Methods\n    def start_gunicorn_service(self, setup_id):\n        \"\"\"Start OpenAlgo with Gunicorn as systemd service\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        if not instance_path.exists():\n            return {\"status\": \"error\", \"message\": \"Instance not found. Run STEP 1 (SET_ENV) first.\"}\n\n        try:\n            print(f\"🚀 Starting Gunicorn service for {setup_data['setup_name']}\")\n\n            # Create systemd service file\n            service_result = self.create_systemd_service(setup_id, instance_path, setup_data)\n            if not service_result:\n                return {\"status\": \"error\", \"message\": \"Failed to create systemd service\"}\n\n            # Start the service - use full instance name as service name\n            service_name = instance_name\n            result = self.run_systemctl('start', service_name)\n\n            if result.returncode != 0:\n                return {\"status\": \"error\", \"message\": f\"Failed to start service: {result.stderr}\"}\n\n            # DO NOT enable auto-start - manual control only\n            # subprocess.run(['sudo', 'systemctl', 'enable', service_name], capture_output=True)\n            print(f\"⚠️ Auto-start disabled - service will NOT start automatically on boot\")\n\n            # Wait and check status\n            time.sleep(3)\n            status_result = self.get_service_status(service_name)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Gunicorn service started for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"service_name\": service_name,\n                    \"instance_path\": str(instance_path),\n                    \"port\": self.get_port_from_env(instance_path),\n                    \"service_status\": status_result,\n                    \"url\": f\"http://127.0.0.1:{self.get_port_from_env(instance_path)}\",\n                    \"auto_restart\": True\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to start Gunicorn service: {str(e)}\"}\n\n    def stop_gunicorn_service(self, setup_id):\n        \"\"\"Stop OpenAlgo Gunicorn service\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        service_name = instance_name  # Use full instance name as service name\n\n        try:\n            print(f\"🛑 Stopping Gunicorn service for {setup_data['setup_name']}\")\n\n            result = self.run_systemctl('stop', service_name)\n\n            if result.returncode != 0:\n                return {\"status\": \"error\", \"message\": f\"Failed to stop service: {result.stderr}\"}\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Gunicorn service stopped for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"service_name\": service_name,\n                    \"status\": \"stopped\"\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to stop Gunicorn service: {str(e)}\"}\n\n    def restart_gunicorn_service(self, setup_id):\n        \"\"\"Restart OpenAlgo Gunicorn service\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        service_name = instance_name  # Use full instance name as service name\n\n        try:\n            print(f\"🔄 Restarting Gunicorn service for {setup_data['setup_name']}\")\n\n            result = subprocess.run([\n                '/usr/bin/sudo', '/usr/bin/systemctl', 'restart', service_name\n            ], capture_output=True, text=True)\n\n            if result.returncode != 0:\n                return {\"status\": \"error\", \"message\": f\"Failed to restart service: {result.stderr}\"}\n\n            # Wait and check status\n            time.sleep(3)\n            status_result = self.get_service_status(service_name)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Gunicorn service restarted for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"service_name\": service_name,\n                    \"service_status\": status_result,\n                    \"url\": f\"http://127.0.0.1:{self.get_port_from_env(self.instances_dir / instance_name)}\"\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to restart Gunicorn service: {str(e)}\"}\n\n    def health_check(self, setup_id):\n        \"\"\"Health check for Algo instance\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        # Get port from .env file\n        port = self.get_port_from_env(instance_path) if instance_path.exists() else 5000\n\n        try:\n            # Check if Algo is responding\n            http_healthy = False\n            http_status = \"unreachable\"\n            try:\n                response = requests.get(f\"http://127.0.0.1:{port}\", timeout=5)\n                http_healthy = response.status_code == 200\n                http_status = response.status_code\n            except Exception as e:\n                http_status = f\"error: {str(e)}\"\n\n            # Check systemd service status\n            service_name = instance_name  # Use full instance name as service name\n            service_status = self.get_service_status(service_name)\n\n            # Check database connectivity\n            db_status = self.check_database_health(instance_path)\n\n            # Overall health\n            overall_healthy = http_healthy and service_status.get(\"active\", False) and db_status.get(\"healthy\", False)\n\n            return {\n                \"status\": \"success\",\n                \"data\": {\n                    \"setup_name\": setup_data['setup_name'],\n                    \"broker_name\": setup_data['broker_name'],\n                    \"instance_healthy\": overall_healthy,\n                    \"http_healthy\": http_healthy,\n                    \"http_status\": http_status,\n                    \"service_status\": service_status,\n                    \"database_status\": db_status,\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"port\": port,\n                    \"instance_path\": str(instance_path),\n                    \"service_name\": service_name,\n                    \"last_checked\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Health check failed: {str(e)}\",\n                \"data\": {\n                    \"setup_name\": setup_data['setup_name'],\n                    \"instance_healthy\": False,\n                    \"error\": str(e),\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"port\": port,\n                    \"last_checked\": self.get_ist_timestamp()\n                }\n            }\n\n    def get_instance_status(self, setup_id):\n        \"\"\"Get detailed status of OpenAlgo instance\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n        port = self.get_port_from_env(instance_path)\n\n        # Get health check\n        health_result = self.health_check(setup_id)\n        health_data = health_result.get(\"data\", {})\n\n        # Get additional info\n        service_name = instance_name  # Use full instance name as service name\n\n        return {\n            \"status\": \"success\",\n            \"data\": {\n                \"setup_info\": {\n                    \"setup_id\": setup_id,\n                    \"setup_name\": setup_data['setup_name'],\n                    \"broker_name\": setup_data['broker_name'],\n                    \"broker_type\": self.get_broker_type(setup_data['broker_name']),\n                    \"client_id\": setup_data['broker_client_id']\n                },\n                \"instance_info\": {\n                    \"instance_path\": str(instance_path),\n                    \"port\": port,\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"service_name\": service_name\n                },\n                \"health_status\": health_data,\n                \"timestamps\": {\n                    \"created_at\": setup_data.get('created_at'),\n                    \"updated_at\": setup_data.get('updated_at'),\n                    \"last_connected\": setup_data.get('last_connected'),\n                    \"checked_at\": self.get_ist_timestamp()\n                }\n            }\n        }\n\n    def get_instance_logs(self, setup_id, lines=50):\n        \"\"\"Get OpenAlgo instance logs\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        service_name = instance_name  # Use full instance name as service name\n\n        try:\n            # Get systemd service logs\n            result = subprocess.run([\n                'sudo', 'journalctl', '-u', service_name, '-n', str(lines), '--no-pager'\n            ], capture_output=True, text=True)\n\n            if result.returncode != 0:\n                return {\"status\": \"error\", \"message\": f\"Failed to get logs: {result.stderr}\"}\n\n            return {\n                \"status\": \"success\",\n                \"data\": {\n                    \"setup_name\": setup_data['setup_name'],\n                    \"service_name\": service_name,\n                    \"lines_requested\": lines,\n                    \"logs\": result.stdout,\n                    \"retrieved_at\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to get logs: {str(e)}\"}\n\n    # Helper Methods\n    def create_systemd_service(self, setup_id, instance_path, setup_data):\n        \"\"\"Create systemd service file for Algo instance with full service name\"\"\"\n\n        # Use full service name: username-algo-broker-instance\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        service_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        port = 5000 + setup_id  # Calculate port for this instance\n\n        print(f\"✅ Using HTTP configuration on port {port}\")\n\n        # Create systemd service using HTTP binding\n        service_content = f\"\"\"[Unit]\nDescription=Algo Instance - {setup_data['setup_name']} (HTTP Port {port})\nAfter=network.target\n\n[Service]\nType=simple\nUser=ubuntu\nGroup=ubuntu\nWorkingDirectory={instance_path}\nExecStart=/bin/bash -c 'source {self.base_dir}/env/bin/activate && cd {instance_path} && gunicorn \\\\\n    --worker-class sync \\\\\n    -w 1 \\\\\n    --bind 127.0.0.1:{port} \\\\\n    --log-level info \\\\\n    --access-logfile - \\\\\n    --error-logfile - \\\\\n    app:app'\nRestart=always\nRestartSec=5\nTimeoutSec=60\nStandardOutput=journal\nStandardError=journal\n\n[Install]\nWantedBy=multi-user.target\n\"\"\"\n\n        try:\n            # Write service file\n            service_file = f\"/etc/systemd/system/{service_name}.service\"\n            with open(f\"/tmp/{service_name}.service\", 'w') as f:\n                f.write(service_content)\n\n            # Move to systemd directory\n            result = subprocess.run([\n                '/usr/bin/sudo', '/usr/bin/mv', f\"/tmp/{service_name}.service\", service_file\n            ], capture_output=True, text=True, env=self.subprocess_env)\n\n            if result.returncode != 0:\n                print(f\"Failed to create service file: {result.stderr}\")\n                return False\n\n            # Reload systemd\n            subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'daemon-reload'],\n                         capture_output=True, env=self.subprocess_env)\n\n            print(f\"✅ Created systemd service: {service_name}\")\n            return True\n\n        except Exception as e:\n            print(f\"Failed to create systemd service: {e}\")\n            return False\n\n    def check_socket_running(self, socket_file):\n        \"\"\"Check if Unix socket file exists and is accessible\"\"\"\n\n        try:\n            import socket\n            import os\n\n            if not os.path.exists(socket_file):\n                return False\n\n            # Try to connect to the socket\n            sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)\n            try:\n                sock.connect(socket_file)\n                sock.close()\n                return True\n            except:\n                return False\n\n        except Exception as e:\n            print(f\"⚠️ Socket check failed: {e}\")\n            return False\n\n    def get_service_status(self, service_name):\n        \"\"\"Get systemd service status\"\"\"\n\n        try:\n            result = subprocess.run([\n                '/usr/bin/sudo', '/usr/bin/systemctl', 'is-active', service_name\n            ], capture_output=True, text=True)\n\n            status = result.stdout.strip()\n\n            # Get more detailed status\n            status_result = subprocess.run([\n                '/usr/bin/sudo', '/usr/bin/systemctl', 'status', service_name, '--no-pager', '-l'\n            ], capture_output=True, text=True)\n\n            return {\n                \"active\": status == \"active\",\n                \"status\": status,\n                \"details\": status_result.stdout if status_result.returncode == 0 else \"Service not found\"\n            }\n\n        except Exception as e:\n            return {\n                \"active\": False,\n                \"status\": \"error\",\n                \"details\": str(e)\n            }\n\n    def check_database_health(self, instance_path):\n        \"\"\"Check Algo database health\"\"\"\n\n        try:\n            if not instance_path.exists():\n                return {\"healthy\": False, \"error\": \"Instance path not found\"}\n\n            # Check for database file\n            db_path = instance_path / \"db\" / \"openalgo.db\"\n\n            if not db_path.exists():\n                # Database might not be created yet\n                return {\"healthy\": False, \"error\": \"Database file not found (not created yet)\"}\n\n            # Try to connect to database\n            conn = sqlite3.connect(db_path, timeout=5)\n            cursor = conn.cursor()\n\n            # Simple query to check if database is accessible\n            cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table' LIMIT 1\")\n            result = cursor.fetchone()\n\n            conn.close()\n\n            return {\n                \"healthy\": True,\n                \"database_path\": str(db_path),\n                \"accessible\": True,\n                \"tables_found\": result is not None\n            }\n\n        except Exception as e:\n            return {\n                \"healthy\": False,\n                \"error\": str(e),\n                \"database_path\": str(instance_path / \"db\" / \"openalgo.db\") if instance_path.exists() else \"unknown\"\n            }\n\n    # ============================================================================\n    # SYSTEM SERVICE MANAGEMENT METHODS\n    # ============================================================================\n\n    def get_system_services(self):\n        \"\"\"Get all algo-related systemd services\"\"\"\n        try:\n            # Get all algo services\n            result = subprocess.run([\n                'sudo', 'systemctl', 'list-units', '--type=service', '--state=active,inactive,failed',\n                '--no-pager', '--plain', 'algo-*'\n            ], capture_output=True, text=True)\n\n            services = []\n            if result.returncode == 0:\n                lines = result.stdout.strip().split('\\n')\n                for line in lines:\n                    if 'algo-' in line and '.service' in line:\n                        parts = line.split()\n                        if len(parts) >= 4:\n                            service_name = parts[0].replace('.service', '')\n                            status = parts[2]  # active, inactive, failed\n                            description = ' '.join(parts[4:]) if len(parts) > 4 else ''\n\n                            # Extract setup info from service name\n                            setup_info = self.extract_setup_info_from_service(service_name)\n\n                            services.append({\n                                \"service_name\": service_name,\n                                \"status\": status,\n                                \"description\": description,\n                                \"setup_id\": setup_info.get(\"setup_id\"),\n                                \"broker_name\": setup_info.get(\"broker_name\"),\n                                \"instance_name\": setup_info.get(\"instance_name\")\n                            })\n\n            return {\n                \"status\": \"success\",\n                \"data\": {\n                    \"services\": services,\n                    \"total_services\": len(services),\n                    \"retrieved_at\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to get services: {str(e)}\"}\n\n    def extract_setup_info_from_service(self, service_name):\n        \"\"\"Extract setup information from service name (e.g., algo-angel-42)\"\"\"\n        try:\n            if service_name.startswith('algo-'):\n                parts = service_name[5:].split('-')  # Remove 'algo-' prefix\n                if len(parts) >= 2:\n                    broker_name = parts[0]\n                    setup_id = parts[1]\n                    instance_name = f\"{broker_name}-{setup_id}\"\n                    return {\n                        \"setup_id\": int(setup_id),\n                        \"broker_name\": broker_name,\n                        \"instance_name\": instance_name\n                    }\n        except:\n            pass\n        return {\"setup_id\": None, \"broker_name\": None, \"instance_name\": None}\n\n    def restart_service(self, service_name):\n        \"\"\"Restart a systemd service\"\"\"\n        try:\n            result = subprocess.run([\n                'sudo', 'systemctl', 'restart', service_name\n            ], capture_output=True, text=True)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"✅ Service '{service_name}' restarted successfully\",\n                    \"service_name\": service_name,\n                    \"action\": \"restart\",\n                    \"timestamp\": self.get_ist_timestamp()\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"❌ Failed to restart service '{service_name}': {result.stderr}\",\n                    \"service_name\": service_name\n                }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to restart service: {str(e)}\"}\n\n    def stop_service(self, service_name):\n        \"\"\"Stop a systemd service\"\"\"\n        try:\n            result = self.run_systemctl('stop', service_name)\n\n            if result and result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"✅ Service '{service_name}' stopped successfully\",\n                    \"service_name\": service_name,\n                    \"action\": \"stop\",\n                    \"timestamp\": self.get_ist_timestamp()\n                }\n            else:\n                error_msg = result.stderr if result else \"Unknown error\"\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"❌ Failed to stop service '{service_name}': {error_msg}\",\n                    \"service_name\": service_name\n                }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to stop service: {str(e)}\"}\n\n    def start_service(self, service_name):\n        \"\"\"Start a systemd service\"\"\"\n        try:\n            result = self.run_systemctl('start', service_name)\n\n            if result and result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"✅ Service '{service_name}' started successfully\",\n                    \"service_name\": service_name,\n                    \"action\": \"start\",\n                    \"timestamp\": self.get_ist_timestamp()\n                }\n            else:\n                error_msg = result.stderr if result else \"Unknown error\"\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"❌ Failed to start service '{service_name}': {error_msg}\",\n                    \"service_name\": service_name\n                }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to start service: {str(e)}\"}\n\n    def delete_service(self, service_name):\n        \"\"\"Delete a systemd service and its files\"\"\"\n        try:\n            # Stop the service first\n            subprocess.run(['sudo', 'systemctl', 'stop', service_name], capture_output=True)\n\n            # Disable the service\n            subprocess.run(['sudo', 'systemctl', 'disable', service_name], capture_output=True)\n\n            # Remove service file\n            service_file = f\"/etc/systemd/system/{service_name}.service\"\n            result = subprocess.run([\n                'sudo', 'rm', '-f', service_file\n            ], capture_output=True, text=True)\n\n            # Reload systemd daemon\n            subprocess.run(['sudo', 'systemctl', 'daemon-reload'], capture_output=True)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"✅ Service '{service_name}' deleted successfully\",\n                    \"service_name\": service_name,\n                    \"action\": \"delete\",\n                    \"timestamp\": self.get_ist_timestamp()\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"❌ Failed to delete service '{service_name}': {result.stderr}\",\n                    \"service_name\": service_name\n                }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to delete service: {str(e)}\"}\n\n    # ============================================================================\n    # BACKUP AND RESTORE METHODS\n    # ============================================================================\n\n    def backup_instance(self, setup_id):\n        \"\"\"Backup broker instance (database and .env file)\"\"\"\n        try:\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            # Use consistent naming: username-algo-broker-id\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n            instance_path = self.instances_dir / instance_name\n\n            if not instance_path.exists():\n                return {\"status\": \"error\", \"message\": \"Instance path not found\"}\n\n            # Create backup directory\n            backup_dir = self.base_dir / \"backups\"\n            backup_dir.mkdir(exist_ok=True)\n\n            # Create backup filename with timestamp\n            timestamp = self.get_ist_timestamp().replace(' ', '_').replace(':', '-')\n            backup_filename = f\"backup_{instance_name}_{timestamp}.tar.gz\"\n            backup_path = backup_dir / backup_filename\n\n            # Create tar archive with database and .env\n            import tarfile\n            with tarfile.open(backup_path, 'w:gz') as tar:\n                # Add .env file\n                env_file = instance_path / \".env\"\n                if env_file.exists():\n                    tar.add(env_file, arcname=\".env\")\n\n                # Add database directory\n                db_dir = instance_path / \"db\"\n                if db_dir.exists():\n                    tar.add(db_dir, arcname=\"db\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Backup created successfully for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"backup_file\": backup_filename,\n                    \"backup_path\": str(backup_path),\n                    \"instance_name\": instance_name,\n                    \"setup_name\": setup_data['setup_name'],\n                    \"created_at\": self.get_ist_timestamp(),\n                    \"size_mb\": round(backup_path.stat().st_size / (1024 * 1024), 2)\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to create backup: {str(e)}\"}\n\n    def restore_instance(self, setup_id, backup_file=None):\n        \"\"\"Restore broker instance from backup\"\"\"\n        try:\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            # Use consistent naming: username-algo-broker-id\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n            instance_path = self.instances_dir / instance_name\n\n            if not instance_path.exists():\n                return {\"status\": \"error\", \"message\": \"Instance path not found\"}\n\n            # Find backup file\n            backup_dir = self.base_dir / \"backups\"\n            if backup_file:\n                backup_path = backup_dir / backup_file\n            else:\n                # Find latest backup for this instance\n                backup_files = list(backup_dir.glob(f\"backup_{instance_name}_*.tar.gz\"))\n                if not backup_files:\n                    return {\"status\": \"error\", \"message\": \"No backup files found\"}\n                backup_path = max(backup_files, key=lambda x: x.stat().st_mtime)\n\n            if not backup_path.exists():\n                return {\"status\": \"error\", \"message\": \"Backup file not found\"}\n\n            # Stop service before restore\n            service_name = instance_name  # Use full instance name as service name\n            subprocess.run(['sudo', 'systemctl', 'stop', service_name], capture_output=True)\n\n            # Extract backup\n            import tarfile\n            with tarfile.open(backup_path, 'r:gz') as tar:\n                tar.extractall(instance_path)\n\n            # Restart service\n            subprocess.run(['sudo', 'systemctl', 'start', service_name], capture_output=True)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Instance restored successfully from backup\",\n                \"data\": {\n                    \"backup_file\": backup_path.name,\n                    \"instance_name\": instance_name,\n                    \"setup_name\": setup_data['setup_name'],\n                    \"restored_at\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to restore from backup: {str(e)}\"}\n\n    def list_backups(self):\n        \"\"\"List all available backups\"\"\"\n        try:\n            backup_dir = self.base_dir / \"backups\"\n            backup_dir.mkdir(exist_ok=True)\n\n            backups = []\n            for backup_file in backup_dir.glob(\"backup_*.tar.gz\"):\n                stat = backup_file.stat()\n\n                # Parse filename to extract info\n                filename = backup_file.name\n                parts = filename.replace('.tar.gz', '').split('_')\n                if len(parts) >= 4:\n                    instance_name = f\"{parts[1]}-{parts[2]}\"\n                    timestamp = '_'.join(parts[3:])\n                else:\n                    instance_name = \"unknown\"\n                    timestamp = \"unknown\"\n\n                backups.append({\n                    \"filename\": filename,\n                    \"instance_name\": instance_name,\n                    \"timestamp\": timestamp,\n                    \"size_mb\": round(stat.st_size / (1024 * 1024), 2),\n                    \"created_at\": stat.st_mtime\n                })\n\n            # Sort by creation time (newest first)\n            backups.sort(key=lambda x: x['created_at'], reverse=True)\n\n            return {\n                \"status\": \"success\",\n                \"data\": {\n                    \"backups\": backups,\n                    \"total_backups\": len(backups),\n                    \"backup_directory\": str(backup_dir)\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to list backups: {str(e)}\"}\n\n    # ============================================================================\n    # API TESTING METHODS\n    # ============================================================================\n\n    def test_all_apis(self, setup_id):\n        \"\"\"Test all broker APIs and return detailed results\"\"\"\n        try:\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            # Use consistent naming: username-algo-broker-id\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n            port = 5000 + setup_id\n            base_url = f\"http://127.0.0.1:{port}\"\n\n            # List of APIs to test\n            api_tests = [\n                {\"name\": \"funds\", \"endpoint\": \"/api/v1/funds\", \"method\": \"GET\"},\n                {\"name\": \"holdings\", \"endpoint\": \"/api/v1/holdings\", \"method\": \"GET\"},\n                {\"name\": \"positions\", \"endpoint\": \"/api/v1/positions\", \"method\": \"GET\"},\n                {\"name\": \"orders\", \"endpoint\": \"/api/v1/orders\", \"method\": \"GET\"},\n                {\"name\": \"orderbook\", \"endpoint\": \"/api/v1/orderbook\", \"method\": \"GET\"},\n                {\"name\": \"tradebook\", \"endpoint\": \"/api/v1/tradebook\", \"method\": \"GET\"},\n                {\"name\": \"profile\", \"endpoint\": \"/api/v1/profile\", \"method\": \"GET\"},\n                {\"name\": \"quotes\", \"endpoint\": \"/api/v1/quotes\", \"method\": \"POST\", \"data\": {\"exchange\": \"NSE\", \"token\": \"22\"}},\n                {\"name\": \"depth\", \"endpoint\": \"/api/v1/depth\", \"method\": \"POST\", \"data\": {\"exchange\": \"NSE\", \"token\": \"22\"}},\n                {\"name\": \"history\", \"endpoint\": \"/api/v1/history\", \"method\": \"POST\", \"data\": {\"exchange\": \"NSE\", \"token\": \"22\", \"resolution\": \"1\"}}\n            ]\n\n            results = []\n            session = requests.Session()\n\n            for test in api_tests:\n                try:\n                    url = base_url + test[\"endpoint\"]\n\n                    if test[\"method\"] == \"GET\":\n                        response = session.get(url, timeout=10)\n                    else:\n                        response = session.post(url, json=test.get(\"data\", {}), timeout=10)\n\n                    # Check if response is JSON\n                    try:\n                        json_data = response.json()\n                        is_json = True\n                        response_preview = str(json_data)[:200] + \"...\" if len(str(json_data)) > 200 else str(json_data)\n                    except:\n                        is_json = False\n                        response_preview = response.text[:200] + \"...\" if len(response.text) > 200 else response.text\n\n                    # Determine status\n                    if response.status_code == 200 and is_json:\n                        if isinstance(json_data, dict) and json_data.get(\"status\") == \"success\":\n                            status = \"success\"\n                            message = \"API working correctly\"\n                        elif \"login\" in response.text.lower() or \"<!doctype html>\" in response.text.lower():\n                            status = \"auth_required\"\n                            message = \"Authentication required\"\n                        else:\n                            status = \"partial\"\n                            message = \"API responding but may need authentication\"\n                    else:\n                        status = \"failed\"\n                        message = f\"HTTP {response.status_code}\"\n\n                    results.append({\n                        \"api_name\": test[\"name\"],\n                        \"endpoint\": test[\"endpoint\"],\n                        \"method\": test[\"method\"],\n                        \"status\": status,\n                        \"http_code\": response.status_code,\n                        \"is_json\": is_json,\n                        \"message\": message,\n                        \"response_preview\": response_preview,\n                        \"tested_at\": self.get_ist_timestamp()\n                    })\n\n                except Exception as e:\n                    results.append({\n                        \"api_name\": test[\"name\"],\n                        \"endpoint\": test[\"endpoint\"],\n                        \"method\": test[\"method\"],\n                        \"status\": \"error\",\n                        \"http_code\": None,\n                        \"is_json\": False,\n                        \"message\": f\"Connection error: {str(e)}\",\n                        \"response_preview\": \"\",\n                        \"tested_at\": self.get_ist_timestamp()\n                    })\n\n            # Calculate summary\n            total_apis = len(results)\n            successful_apis = len([r for r in results if r[\"status\"] == \"success\"])\n            failed_apis = len([r for r in results if r[\"status\"] in [\"failed\", \"error\"]])\n            auth_required_apis = len([r for r in results if r[\"status\"] == \"auth_required\"])\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ API testing completed for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"setup_id\": setup_id,\n                    \"setup_name\": setup_data['setup_name'],\n                    \"instance_name\": instance_name,\n                    \"base_url\": base_url,\n                    \"summary\": {\n                        \"total_apis\": total_apis,\n                        \"successful\": successful_apis,\n                        \"failed\": failed_apis,\n                        \"auth_required\": auth_required_apis,\n                        \"success_rate\": round((successful_apis / total_apis) * 100, 1) if total_apis > 0 else 0\n                    },\n                    \"api_results\": results,\n                    \"tested_at\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to test APIs: {str(e)}\"}\n\n    def get_api_status(self, setup_id):\n        \"\"\"Get quick API status for a broker\"\"\"\n        try:\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            port = 5000 + setup_id\n            base_url = f\"http://127.0.0.1:{port}\"\n\n            # Test key APIs quickly\n            key_apis = [\"funds\", \"holdings\", \"positions\"]\n            session = requests.Session()\n\n            api_status = {}\n            for api in key_apis:\n                try:\n                    response = session.get(f\"{base_url}/api/v1/{api}\", timeout=5)\n                    if response.status_code == 200:\n                        try:\n                            json_data = response.json()\n                            if isinstance(json_data, dict) and json_data.get(\"status\") == \"success\":\n                                api_status[api] = \"working\"\n                            else:\n                                api_status[api] = \"auth_required\"\n                        except:\n                            api_status[api] = \"auth_required\"\n                    else:\n                        api_status[api] = \"failed\"\n                except:\n                    api_status[api] = \"error\"\n\n            # Overall status\n            working_count = len([status for status in api_status.values() if status == \"working\"])\n            overall_status = \"healthy\" if working_count >= 2 else \"needs_attention\"\n\n            return {\n                \"status\": \"success\",\n                \"data\": {\n                    \"setup_id\": setup_id,\n                    \"setup_name\": setup_data['setup_name'],\n                    \"base_url\": base_url,\n                    \"overall_status\": overall_status,\n                    \"api_status\": api_status,\n                    \"working_apis\": working_count,\n                    \"total_tested\": len(key_apis),\n                    \"checked_at\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to get API status: {str(e)}\"}\n    \n    def update_setup_instance_info(self, setup_id, instance_info):\n        \"\"\"Update setup with OpenAlgo instance information\"\"\"\n        try:\n            conn = sqlite3.connect(self.db_path)\n            cursor = conn.cursor()\n            \n            # Add instance info as JSON in additional_config\n            cursor.execute(\"\"\"\n                UPDATE user_brokers \n                SET additional_config = ?\n                WHERE id = ?\n            \"\"\", (json.dumps(instance_info), setup_id))\n            \n            conn.commit()\n            conn.close()\n            \n        except Exception as e:\n            print(f\"Failed to update instance info: {e}\")\n\n# Test functions for named manual processes\ndef test_step_1_set_env():\n    \"\"\"Test STEP 1: SET_ENV\"\"\"\n    automation = BrokerAutomation()\n    result = automation.step_1_set_env(4)\n    print(\"🔧 STEP 1: SET_ENV RESULT:\")\n    print(json.dumps(result, indent=2))\n    return result\n\ndef test_step_2_start_algo():\n    \"\"\"Test STEP 2: START_ALGO\"\"\"\n    automation = BrokerAutomation()\n    result = automation.step_2_start_algo(4)\n    print(\"\\n🚀 STEP 2: START_ALGO RESULT:\")\n    print(json.dumps(result, indent=2))\n    return result\n\ndef test_step_3_register():\n    \"\"\"Test STEP 3: REGISTER\"\"\"\n    automation = BrokerAutomation()\n    result = automation.step_3_register(4)\n    print(\"\\n👤 STEP 3: REGISTER RESULT:\")\n    print(json.dumps(result, indent=2))\n    return result\n\ndef test_step_4_connect_broker():\n    \"\"\"Test STEP 4: CONNECT_BROKER\"\"\"\n    automation = BrokerAutomation()\n    result = automation.step_4_connect_broker(4)\n    print(\"\\n🏦 STEP 4: CONNECT_BROKER RESULT:\")\n    print(json.dumps(result, indent=2))\n    return result\n\nif __name__ == \"__main__\":\n    print(\"🎯 NAMED MANUAL PROCESS TEST\")\n    print(\"=\" * 60)\n\n    # Test all 4 steps\n    step1_result = test_step_1_set_env()\n\n    if step1_result[\"status\"] == \"success\":\n        print(\"\\n\" + \"=\" * 60)\n        print(\"✅ STEP 1 (SET_ENV) COMPLETED!\")\n        print(f\"📁 Instance: {step1_result['data']['instance_path']}\")\n        print(f\"🌐 Port: {step1_result['data']['port']}\")\n        print(f\"🔗 URL: {step1_result['data']['openalgo_url']}\")\n\n        # Test other steps\n        step2_result = test_step_2_start_algo()\n        if step2_result[\"status\"] == \"success\":\n            print(f\"\\n🚀 START COMMAND:\")\n            print(f\"   {step2_result['data']['start_command']}\")\n\n        step3_result = test_step_3_register()\n        if step3_result[\"status\"] == \"success\":\n            print(f\"\\n👤 REGISTRATION INFO:\")\n            creds = step3_result['data']['credentials']\n            print(f\"   Username: {creds['username']}\")\n            print(f\"   Email: {creds['email']}\")\n            print(f\"   Password: {creds['password']}\")\n            print(f\"   Full Name: {creds['full_name']}\")\n\n        step4_result = test_step_4_connect_broker()\n        if step4_result[\"status\"] == \"success\":\n            print(f\"\\n🏦 BROKER CONNECTION INFO:\")\n            broker_creds = step4_result['data']['broker_credentials']\n            print(f\"   Client ID: {broker_creds['client_id']}\")\n            print(f\"   Trading PIN: {broker_creds['trading_pin']}\")\n            print(f\"   TOTP Code: {broker_creds['totp_code']}\")\n\n            if step4_result['data'].get('xts_credentials'):\n                print(f\"   XTS Market API Key: {step4_result['data']['xts_credentials']['market_api_key']}\")\n\n        print(\"\\n\" + \"=\" * 60)\n        print(\"🎯 MANUAL PROCESS SUMMARY:\")\n        print(\"✅ STEP 1 (SET_ENV): Environment configured\")\n        print(\"✅ STEP 2 (START_ALGO): Ready to start OpenAlgo\")\n        print(\"✅ STEP 3 (REGISTER): User registration details ready\")\n        print(\"✅ STEP 4 (CONNECT_BROKER): Broker authentication ready\")\n        print(\"\\n🚀 ALL STEPS READY FOR MANUAL EXECUTION!\")\n\n    else:\n        print(f\"\\n❌ STEP 1 (SET_ENV) FAILED:\")\n        print(f\"   {step1_result['message']}\")\n\n    # ============================================================================\n    # SYSTEM SERVICE MANAGEMENT METHODS\n    # ============================================================================\n\n    def start_systemd_service(self, service_name):\n        \"\"\"Start a systemd service\"\"\"\n        try:\n            import subprocess\n            result = subprocess.run([\n                'sudo', 'systemctl', 'start', service_name\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"Service {service_name} started successfully\"\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to start service: {result.stderr}\"\n                }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error starting service: {str(e)}\"\n            }\n\n    def stop_systemd_service(self, service_name):\n        \"\"\"Stop a systemd service\"\"\"\n        try:\n            import subprocess\n            result = subprocess.run([\n                'sudo', 'systemctl', 'stop', service_name\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"Service {service_name} stopped successfully\"\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to stop service: {result.stderr}\"\n                }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error stopping service: {str(e)}\"\n            }\n\n    def restart_systemd_service(self, service_name):\n        \"\"\"Restart a systemd service\"\"\"\n        try:\n            import subprocess\n            result = subprocess.run([\n                'sudo', 'systemctl', 'restart', service_name\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"Service {service_name} restarted successfully\"\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to restart service: {result.stderr}\"\n                }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error restarting service: {str(e)}\"\n            }\n\n    def get_service_logs(self, service_name, lines=50):\n        \"\"\"Get logs for a systemd service\"\"\"\n        try:\n            import subprocess\n            result = subprocess.run([\n                'sudo', 'journalctl', '-u', service_name, '-n', str(lines), '--no-pager'\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"logs\": result.stdout,\n                    \"lines\": lines\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to get logs: {result.stderr}\",\n                    \"logs\": \"\"\n                }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error getting logs: {str(e)}\",\n                \"logs\": \"\"\n            }\n\n    def start_all_user_services(self, user_id):\n        \"\"\"Start all services for a specific user\"\"\"\n        try:\n            import subprocess\n\n            # Get all services for this user\n            result = subprocess.run([\n                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode != 0:\n                return {\n                    \"status\": \"error\",\n                    \"message\": \"Failed to list services\"\n                }\n\n            # Filter services for this user\n            user_services = []\n            for line in result.stdout.split('\\n'):\n                if f\"{user_id}-algo-\" in line and '.service' in line:\n                    service_name = line.split()[0]\n                    if 'inactive' in line or 'failed' in line:\n                        user_services.append(service_name)\n\n            started = 0\n            failed = 0\n\n            for service in user_services:\n                start_result = self.start_systemd_service(service)\n                if start_result.get('status') == 'success':\n                    started += 1\n                else:\n                    failed += 1\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Started {started} services, {failed} failed\",\n                \"started\": started,\n                \"failed\": failed\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error starting all services: {str(e)}\"\n            }\n\n    def stop_all_user_services(self, user_id):\n        \"\"\"Stop all services for a specific user\"\"\"\n        try:\n            import subprocess\n\n            # Get all services for this user\n            result = subprocess.run([\n                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode != 0:\n                return {\n                    \"status\": \"error\",\n                    \"message\": \"Failed to list services\"\n                }\n\n            # Filter services for this user\n            user_services = []\n            for line in result.stdout.split('\\n'):\n                if f\"{user_id}-algo-\" in line and '.service' in line:\n                    service_name = line.split()[0]\n                    if 'active' in line and 'running' in line:\n                        user_services.append(service_name)\n\n            stopped = 0\n            failed = 0\n\n            for service in user_services:\n                stop_result = self.stop_systemd_service(service)\n                if stop_result.get('status') == 'success':\n                    stopped += 1\n                else:\n                    failed += 1\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Stopped {stopped} services, {failed} failed\",\n                \"stopped\": stopped,\n                \"failed\": failed\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error stopping all services: {str(e)}\"\n            }\n\n    def cleanup_dead_services(self):\n        \"\"\"Clean up all dead/not-found systemd services\"\"\"\n        try:\n            import subprocess\n\n            # Get all services\n            result = subprocess.run([\n                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'\n            ], capture_output=True, text=True, timeout=60)\n\n            if result.returncode != 0:\n                return {\n                    \"status\": \"error\",\n                    \"message\": \"Failed to list services\"\n                }\n\n            # Find dead algo services\n            dead_services = []\n            for line in result.stdout.split('\\n'):\n                if 'algo-' in line and '.service' in line:\n                    if 'not-found' in line or ('inactive' in line and 'dead' in line):\n                        service_name = line.split()[0]\n                        dead_services.append(service_name)\n\n            cleaned = 0\n            failed = 0\n\n            print(f\"🧹 Found {len(dead_services)} dead services to clean up...\")\n\n            for service in dead_services:\n                try:\n                    # Disable the service\n                    disable_result = subprocess.run([\n                        'sudo', 'systemctl', 'disable', service\n                    ], capture_output=True, text=True, timeout=10)\n\n                    # Reset failed state\n                    reset_result = subprocess.run([\n                        'sudo', 'systemctl', 'reset-failed', service\n                    ], capture_output=True, text=True, timeout=10)\n\n                    # Remove service file if it exists\n                    service_file = f\"/etc/systemd/system/{service}\"\n                    remove_result = subprocess.run([\n                        'sudo', 'rm', '-f', service_file\n                    ], capture_output=True, text=True, timeout=10)\n\n                    print(f\"✅ Cleaned up: {service}\")\n                    cleaned += 1\n\n                except Exception as e:\n                    print(f\"❌ Failed to clean: {service} - {e}\")\n                    failed += 1\n\n            # Reload systemd daemon\n            subprocess.run(['sudo', 'systemctl', 'daemon-reload'], timeout=30)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Cleaned up {cleaned} dead services, {failed} failed\",\n                \"cleaned\": cleaned,\n                \"failed\": failed,\n                \"total_found\": len(dead_services)\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error cleaning up services: {str(e)}\"\n            }\n\n    def get_all_system_services(self):\n        \"\"\"Get all system services with their status\"\"\"\n        try:\n            import subprocess\n\n            # Get all services\n            result = subprocess.run([\n                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode != 0:\n                return {\n                    \"status\": \"error\",\n                    \"message\": \"Failed to list services\"\n                }\n\n            services = []\n            for line in result.stdout.split('\\n'):\n                if '.service' in line and line.strip():\n                    parts = line.split()\n                    if len(parts) >= 4:\n                        service_name = parts[0]\n                        load_state = parts[1]\n                        active_state = parts[2]\n                        sub_state = parts[3]\n\n                        # Focus on algo services and system services\n                        if 'algo-' in service_name or service_name in ['nginx', 'mysql', 'postgresql', 'redis']:\n                            services.append({\n                                \"name\": service_name,\n                                \"load\": load_state,\n                                \"active\": active_state,\n                                \"sub\": sub_state,\n                                \"status\": f\"{active_state} ({sub_state})\"\n                            })\n\n            return {\n                \"status\": \"success\",\n                \"services\": services,\n                \"count\": len(services)\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error getting system services: {str(e)}\"\n            }\n\n    def connect_broker_manual_openalgo_style(self, setup_id, manual_credentials=None):\n        \"\"\"\n        Manual broker connection following OpenAlgo instance approach\n        For Angel: Uses clientid, pin, totp form submission\n        For Dhan: Uses direct callback approach\n        \"\"\"\n        try:\n            print(f\"🔗 MANUAL OPENALGO-STYLE CONNECTION: Starting for setup {setup_id}\")\n\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            # Get the actual running port\n            port = self.get_actual_running_port(setup_id)\n            if not port:\n                return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port\"}\n\n            print(f\"🌐 Using OpenAlgo instance on port: {port}\")\n\n            # Check if instance is running\n            if not self.check_instance_running(port):\n                return {\"status\": \"error\", \"message\": f\"OpenAlgo instance not running on port {port}\"}\n\n            # Get broker type\n            broker_name = setup_data.get('broker_name', '').lower()\n            print(f\"🏦 Broker type detected: {broker_name}\")\n\n            if broker_name == 'angel':\n                return self.connect_angel_manual_openalgo_style(setup_id, port, setup_data, manual_credentials)\n            elif broker_name == 'dhan':\n                return self.connect_dhan_manual_openalgo_style(setup_id, port, setup_data)\n            else:\n                return {\"status\": \"error\", \"message\": f\"Manual connection not supported for broker: {broker_name}\"}\n\n        except Exception as e:\n            print(f\"❌ Manual OpenAlgo-style connection failed: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Manual connection failed: {str(e)}\"}\n\n    def connect_angel_manual_openalgo_style(self, setup_id, port, setup_data, manual_credentials):\n        \"\"\"\n        Manual Angel broker connection using OpenAlgo instance form submission approach\n        \"\"\"\n        try:\n            print(f\"👼 ANGEL MANUAL CONNECTION: Using OpenAlgo form submission approach\")\n\n            # Extract credentials\n            if manual_credentials:\n                clientid = manual_credentials.get('clientid') or setup_data.get('broker_client_id')\n                pin = manual_credentials.get('pin') or setup_data.get('trading_pin')\n                totp = manual_credentials.get('totp')\n            else:\n                clientid = setup_data.get('broker_client_id')\n                pin = setup_data.get('trading_pin')\n                totp = None\n\n            if not clientid or not pin:\n                return {\"status\": \"error\", \"message\": \"Missing Angel broker credentials (clientid/pin)\"}\n\n            if not totp:\n                return {\"status\": \"error\", \"message\": \"TOTP is required for Angel broker manual connection\"}\n\n            print(f\"📋 Using credentials - Client ID: {clientid}, PIN: {'*' * len(pin)}\")\n\n            # STEP 1: Login to OpenAlgo instance\n            print(f\"🔐 STEP 1: Login to OpenAlgo instance...\")\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                print(f\"❌ Failed to login to OpenAlgo instance on port {port}\")\n                return {\"status\": \"error\", \"message\": \"Failed to login to OpenAlgo instance\"}\n\n            # STEP 2: Access Angel callback page to get form\n            print(f\"📄 STEP 2: Access Angel authentication form...\")\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n            angel_auth_url = f\"{openalgo_url}/angel/callback\"\n\n            auth_form_page = session.get(angel_auth_url, timeout=10)\n            print(f\"📄 Angel auth form response: {auth_form_page.status_code}\")\n            if auth_form_page.status_code != 200:\n                print(f\"❌ Failed to access Angel auth form: {auth_form_page.status_code}\")\n                return {\"status\": \"error\", \"message\": f\"Failed to access Angel auth form: {auth_form_page.status_code}\"}\n\n            # Extract CSRF token if present\n            csrf_token = None\n            if 'csrf_token' in auth_form_page.text:\n                import re\n                csrf_match = re.search(r'name=\"csrf_token\" value=\"([^\"]+)\"', auth_form_page.text)\n                if csrf_match:\n                    csrf_token = csrf_match.group(1)\n                    print(f\"🔒 CSRF token extracted\")\n\n            # STEP 3: Submit Angel authentication form\n            print(f\"📤 STEP 3: Submit Angel authentication form...\")\n            auth_data = {\n                \"clientid\": clientid,\n                \"pin\": pin,\n                \"totp\": totp\n            }\n\n            if csrf_token:\n                auth_data[\"csrf_token\"] = csrf_token\n\n            auth_response = session.post(\n                angel_auth_url,\n                data=auth_data,\n                allow_redirects=True,\n                timeout=15\n            )\n\n            print(f\"📄 Angel auth response status: {auth_response.status_code}\")\n            print(f\"📄 Angel auth response URL: {auth_response.url}\")\n\n            # Check if authentication was successful\n            if auth_response.status_code == 200:\n                response_text = auth_response.text.lower()\n\n                # Check for success indicators\n                if (\"dashboard\" in auth_response.url.lower() or\n                    \"dashboard\" in response_text or\n                    \"success\" in response_text or\n                    \"authenticated\" in response_text):\n\n                    print(\"✅ Angel manual authentication successful\")\n\n                    # STEP 4: Retrieve and save tokens\n                    print(f\"🔑 STEP 4: Retrieve authentication tokens...\")\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n\n                    # Update instance info\n                    instance_info = {\n                        \"step_completed\": \"connect_broker\",\n                        \"status\": \"broker_connected\",\n                        \"connected_at\": self.get_ist_timestamp(),\n                        \"broker_type\": \"angel\",\n                        \"connection_method\": \"manual_openalgo_style\"\n                    }\n                    self.update_setup_instance_info(setup_id, instance_info)\n\n                    return {\n                        \"status\": \"success\",\n                        \"message\": f\"✅ Angel broker connected manually using OpenAlgo style for '{setup_data['setup_name']}'\",\n                        \"data\": {\n                            \"step\": \"connect_broker\",\n                            \"url\": f\"http://127.0.0.1:{port}\",\n                            \"connection_status\": \"connected\",\n                            \"connection_method\": \"manual_openalgo_style\",\n                            \"auth_tokens\": tokens if tokens else {\"status\": \"authenticated\"}\n                        }\n                    }\n                else:\n                    # Check for error messages in response\n                    print(f\"❌ Angel auth failed - response text preview: {response_text[:200]}...\")\n                    if \"invalid\" in response_text or \"error\" in response_text:\n                        return {\"status\": \"error\", \"message\": \"Invalid credentials or TOTP. Please check and try again.\"}\n                    else:\n                        return {\"status\": \"error\", \"message\": \"Authentication failed. Please verify your credentials.\"}\n            else:\n                print(f\"❌ Angel authentication failed with status: {auth_response.status_code}\")\n                return {\"status\": \"error\", \"message\": f\"Angel authentication failed with status: {auth_response.status_code}\"}\n\n        except Exception as e:\n            print(f\"❌ Angel manual connection failed: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Angel manual connection failed: {str(e)}\"}\n\n    def connect_dhan_manual_openalgo_style(self, setup_id, port, setup_data):\n        \"\"\"\n        Manual Dhan broker connection using OpenAlgo instance direct callback approach\n        \"\"\"\n        try:\n            print(f\"🏦 DHAN MANUAL CONNECTION: Using OpenAlgo direct callback approach\")\n\n            # STEP 1: Login to OpenAlgo instance\n            print(f\"🔐 STEP 1: Login to OpenAlgo instance...\")\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                return {\"status\": \"error\", \"message\": \"Failed to login to OpenAlgo instance\"}\n\n            # STEP 2: Trigger Dhan callback directly (no form needed)\n            print(f\"🔗 STEP 2: Trigger Dhan authentication callback...\")\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n            dhan_callback_url = f\"{openalgo_url}/dhan/callback\"\n\n            # Dhan doesn't need form data - just call the callback\n            auth_response = session.get(dhan_callback_url, timeout=10, allow_redirects=True)\n\n            print(f\"📄 Dhan auth response status: {auth_response.status_code}\")\n            print(f\"📄 Dhan auth response URL: {auth_response.url}\")\n\n            # Check if authentication was successful\n            if auth_response.status_code == 200:\n                response_text = auth_response.text.lower()\n\n                # Check for success indicators\n                if (\"dashboard\" in auth_response.url.lower() or\n                    \"dashboard\" in response_text or\n                    \"success\" in response_text or\n                    \"authenticated\" in response_text):\n\n                    print(\"✅ Dhan manual authentication successful\")\n\n                    # STEP 3: Retrieve and save tokens\n                    print(f\"🔑 STEP 3: Retrieve authentication tokens...\")\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n\n                    # Update instance info\n                    instance_info = {\n                        \"step_completed\": \"connect_broker\",\n                        \"status\": \"broker_connected\",\n                        \"connected_at\": self.get_ist_timestamp(),\n                        \"broker_type\": \"dhan\",\n                        \"connection_method\": \"manual_openalgo_style\"\n                    }\n                    self.update_setup_instance_info(setup_id, instance_info)\n\n                    return {\n                        \"status\": \"success\",\n                        \"message\": f\"✅ Dhan broker connected manually using OpenAlgo style for '{setup_data['setup_name']}'\",\n                        \"data\": {\n                            \"step\": \"connect_broker\",\n                            \"url\": f\"http://127.0.0.1:{port}\",\n                            \"connection_status\": \"connected\",\n                            \"connection_method\": \"manual_openalgo_style\",\n                            \"auth_tokens\": tokens if tokens else {\"status\": \"authenticated\"}\n                        }\n                    }\n                else:\n                    return {\"status\": \"error\", \"message\": \"Dhan authentication failed. Please check your broker configuration.\"}\n            else:\n                return {\"status\": \"error\", \"message\": f\"Dhan authentication failed with status: {auth_response.status_code}\"}\n\n        except Exception as e:\n            print(f\"❌ Dhan manual connection failed: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Dhan manual connection failed: {str(e)}\"}\n", "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nBroker Automation System for AlgoFactory\nHandles OpenAlgo instance creation, setup, and broker authentication\n\"\"\"\n\nimport os\nimport sys\nimport json\nimport time\nimport shutil\nimport sqlite3\nimport requests\nimport subprocess\nimport re\nimport logging\nfrom pathlib import Path\nfrom datetime import datetime\nimport pyotp\n\n# Setup logger\nlogger = logging.getLogger(__name__)\n\nclass BrokerAutomation:\n    def __init__(self):\n        self.base_dir = Path(\"/home/<USER>/algofactory\")\n        self.algo_dir = self.base_dir / \"algo\"\n        self.instances_dir = self.algo_dir / \"instances\"\n        self.template_dir = self.algo_dir / \"template\"\n        self.db_path = self.base_dir / \"database/algofactory.db\"\n\n        # Ensure directories exist\n        self.instances_dir.mkdir(parents=True, exist_ok=True)\n        self.template_dir.mkdir(parents=True, exist_ok=True)\n\n        # Port allocation (starting from 5001)\n        self.base_port = 5001\n\n        # Environment for subprocess calls - include virtual environment\n        import os\n        self.subprocess_env = os.environ.copy()\n        self.subprocess_env.update({\n            'PATH': '/home/<USER>/algofactory/env/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/bin',\n            'HOME': '/home/<USER>',\n            'USER': 'ubuntu',\n            'VIRTUAL_ENV': '/home/<USER>/algofactory/env'\n        })\n\n    def run_systemctl(self, action, service_name, timeout=30):\n        \"\"\"Helper method to run systemctl commands with proper environment\"\"\"\n        try:\n            # Use full paths and proper environment for API compatibility\n            cmd = f\"/usr/bin/sudo /usr/bin/systemctl {action} {service_name}\"\n            result = subprocess.run(\n                cmd,\n                shell=True,\n                capture_output=True,\n                text=True,\n                timeout=timeout,\n                env=self.subprocess_env,\n                cwd=str(self.base_dir)\n            )\n            return result\n        except Exception as e:\n            print(f\"Error running systemctl {action} {service_name}: {e}\")\n            return None\n        \n    def get_ist_timestamp(self):\n        \"\"\"Get IST timestamp\"\"\"\n        from datetime import datetime, timezone, timedelta\n        ist = timezone(timedelta(hours=5, minutes=30))\n        return datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S')\n\n    def get_all_broker_setups(self):\n        \"\"\"Get all broker setups from database\"\"\"\n        try:\n            conn = sqlite3.connect(self.db_path)\n            conn.row_factory = sqlite3.Row\n            cursor = conn.cursor()\n\n            # Get all broker setups with user information\n            cursor.execute(\"\"\"\n                SELECT\n                    ub.id,\n                    ub.username,\n                    ub.broker_name,\n                    ub.setup_name,\n                    ub.instance_name,\n                    ub.broker_client_id,\n                    ub.setup_status,\n                    ub.connection_status,\n                    ub.http_port,\n                    ub.websocket_port,\n                    ub.zmq_port,\n                    ub.detailed_status,\n                    ub.created_at,\n                    u.admin_id,\n                    u.full_name as user_full_name\n                FROM user_brokers ub\n                LEFT JOIN users u ON ub.username = u.username\n                ORDER BY ub.created_at DESC\n            \"\"\")\n\n            setups = []\n            for row in cursor.fetchall():\n                setup = dict(row)\n                # Parse detailed_status if it's JSON\n                try:\n                    if setup['detailed_status']:\n                        setup['detailed_status'] = json.loads(setup['detailed_status'])\n                except:\n                    setup['detailed_status'] = {}\n\n                setups.append(setup)\n\n            conn.close()\n            return setups\n\n        except Exception as e:\n            print(f\"❌ Error getting broker setups: {e}\")\n            return []\n\n    def update_all_instances(self):\n        \"\"\"Update all OpenAlgo instances with git pull and requirements - for morning updates\"\"\"\n        try:\n            print(f\"🌅 Morning update: Updating all OpenAlgo instances...\")\n\n            updated_instances = []\n            failed_instances = []\n\n            # Get all instances\n            for instance_dir in self.instances_dir.iterdir():\n                if instance_dir.is_dir() and not instance_dir.name.startswith('.'):\n                    try:\n                        print(f\"🔄 Updating instance: {instance_dir.name}\")\n\n                        # Git pull\n                        git_result = subprocess.run([\n                            '/usr/bin/git', 'pull', 'origin', 'main'\n                        ], cwd=instance_dir, capture_output=True, text=True)\n\n                        # Update requirements\n                        requirements_file = instance_dir / \"requirements.txt\"\n                        if requirements_file.exists():\n                            pip_result = subprocess.run([\n                                'pip', 'install', '-r', str(requirements_file), '--upgrade'\n                            ], capture_output=True, text=True)\n\n                        updated_instances.append(instance_dir.name)\n                        print(f\"✅ Updated: {instance_dir.name}\")\n\n                    except Exception as e:\n                        failed_instances.append(f\"{instance_dir.name}: {str(e)}\")\n                        print(f\"❌ Failed to update {instance_dir.name}: {e}\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Morning update completed: {len(updated_instances)} updated, {len(failed_instances)} failed\",\n                \"updated\": updated_instances,\n                \"failed\": failed_instances,\n                \"timestamp\": self.get_ist_timestamp()\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Morning update failed: {str(e)}\"\n            }\n\n    def get_instance_name(self, setup_data):\n        \"\"\"Generate consistent multi-user instance name: username-algo-broker-setupid\"\"\"\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        return f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n\n    def get_service_name(self, setup_data):\n        \"\"\"Generate consistent multi-user service name: username-algo-broker-setupid\"\"\"\n        return self.get_instance_name(setup_data)\n\n    def create_backup(self, setup_id):\n        \"\"\"Create backup - alias for backup_instance method\"\"\"\n        return self.backup_instance(setup_id)\n\n    def delete_systemd_service(self, service_name):\n        \"\"\"Comprehensive systemd service deletion\"\"\"\n        try:\n            print(f\"🛑 Deleting systemd service: {service_name}\")\n\n            # Step 1: Stop the service\n            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'stop', service_name],\n                                  capture_output=True, text=True, check=False)\n            if result.returncode == 0:\n                print(f\"✅ Service {service_name} stopped\")\n            else:\n                print(f\"⚠️ Service {service_name} stop: {result.stderr.strip()}\")\n\n            # Step 2: Disable the service\n            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'disable', service_name],\n                                  capture_output=True, text=True, check=False)\n            if result.returncode == 0:\n                print(f\"✅ Service {service_name} disabled\")\n            else:\n                print(f\"⚠️ Service {service_name} disable: {result.stderr.strip()}\")\n\n            # Step 3: Delete the service file\n            service_file = f\"/etc/systemd/system/{service_name}.service\"\n            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/rm', '-f', service_file],\n                                  capture_output=True, text=True, check=False)\n            if result.returncode == 0:\n                print(f\"✅ Service file deleted: {service_file}\")\n            else:\n                print(f\"⚠️ Error deleting service file: {result.stderr.strip()}\")\n\n            # Step 4: Reload systemd daemon\n            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'daemon-reload'],\n                                  capture_output=True, text=True, check=False)\n            if result.returncode == 0:\n                print(f\"✅ Systemd daemon reloaded\")\n            else:\n                print(f\"⚠️ Daemon reload warning: {result.stderr.strip()}\")\n\n            # Step 5: Reset failed state (if any)\n            subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'reset-failed', service_name],\n                          capture_output=True, text=True, check=False)\n\n            return {\"status\": \"success\", \"message\": f\"Service {service_name} completely removed\"}\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to delete service {service_name}: {str(e)}\"}\n\n    def get_all_user_services(self, username=None):\n        \"\"\"Get all systemd services for a specific user or all algo services\"\"\"\n        try:\n            # Get all systemd services\n            result = subprocess.run(['sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'],\n                                  capture_output=True, text=True, check=False)\n\n            services = []\n            if result.returncode == 0:\n                lines = result.stdout.split('\\n')\n                for line in lines:\n                    if 'algo' in line.lower():\n                        parts = line.split()\n                        if len(parts) >= 4:\n                            service_name = parts[0]\n                            # Filter by username if provided\n                            if username:\n                                if service_name.startswith(f\"{username}-algo-\"):\n                                    services.append({\n                                        \"name\": service_name,\n                                        \"status\": parts[2],\n                                        \"active\": parts[1],\n                                        \"description\": ' '.join(parts[4:]) if len(parts) > 4 else \"\"\n                                    })\n                            else:\n                                if '-algo-' in service_name:\n                                    services.append({\n                                        \"name\": service_name,\n                                        \"status\": parts[2],\n                                        \"active\": parts[1],\n                                        \"description\": ' '.join(parts[4:]) if len(parts) > 4 else \"\"\n                                    })\n\n            return {\"status\": \"success\", \"services\": services, \"count\": len(services)}\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to get services: {str(e)}\"}\n\n    def stop_algo(self, setup_id):\n        \"\"\"Stop and disable OpenAlgo instance\"\"\"\n        try:\n            logger.info(f\"⏹️ Stopping and disabling OpenAlgo for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": f\"Setup {setup_id} not found\"}\n\n            instance_name = setup_data.get('instance_name')\n            service_name = instance_name  # Use instance name directly as service name\n\n            # Stop systemd service\n            stop_result = self.run_systemctl('stop', service_name)\n\n            # Disable systemd service to prevent auto-restart\n            disable_result = self.run_systemctl('disable', service_name)\n\n            if stop_result and stop_result.returncode == 0:\n                logger.info(f\"✅ Algo service stopped: {service_name}\")\n\n                if disable_result and disable_result.returncode == 0:\n                    logger.info(f\"✅ Algo service disabled: {service_name}\")\n                    message = f\"Algo stopped and disabled successfully\"\n                else:\n                    error_msg = disable_result.stderr if disable_result else \"disable command failed\"\n                    logger.warning(f\"⚠️ Service stopped but disable failed: {error_msg}\")\n                    message = f\"Algo stopped but disable failed\"\n\n                # Update database status\n                self.update_setup_status(setup_id, 'stopped', 'not_connected')\n\n                return {\n                    \"status\": \"success\",\n                    \"message\": message,\n                    \"service_name\": service_name,\n                    \"stopped\": True,\n                    \"disabled\": disable_result.returncode == 0\n                }\n            else:\n                error_msg = stop_result.stderr if stop_result else \"stop command failed\"\n                logger.error(f\"❌ Failed to stop service: {error_msg}\")\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to stop service: {error_msg}\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error stopping Algo: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Failed to stop Algo: {str(e)}\"}\n\n    def get_logs(self, setup_id):\n        \"\"\"Get OpenAlgo logs\"\"\"\n        try:\n            logger.info(f\"📄 Getting logs for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": f\"Setup {setup_id} not found\"}\n\n            instance_name = setup_data.get('instance_name')\n            service_name = instance_name  # Use the instance name directly as service name\n\n            # Get systemd service logs\n            logs_cmd = f\"sudo journalctl -u {service_name} --no-pager -n 50\"\n            result = subprocess.run(logs_cmd, shell=True, capture_output=True, text=True)\n\n            if result.returncode == 0:\n                logs = result.stdout\n                if not logs.strip():\n                    logs = \"No logs available for this service.\"\n\n                logger.info(f\"✅ Logs retrieved for setup: {setup_id}\")\n                return {\n                    \"status\": \"success\",\n                    \"message\": \"Logs retrieved successfully\",\n                    \"logs\": logs,\n                    \"service_name\": service_name\n                }\n            else:\n                logger.error(f\"❌ Failed to get logs: {result.stderr}\")\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to get logs: {result.stderr}\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error getting logs: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Failed to get logs: {str(e)}\"}\n\n    def update_setup_status(self, setup_id, setup_status, connection_status):\n        \"\"\"Update setup status in database\"\"\"\n        try:\n            import sqlite3\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n                cursor.execute(\"\"\"\n                    UPDATE user_brokers\n                    SET setup_status = ?, connection_status = ?\n                    WHERE id = ?\n                \"\"\", (setup_status, connection_status, setup_id))\n                conn.commit()\n\n            logger.info(f\"✅ Updated status for setup {setup_id}: {setup_status}/{connection_status}\")\n            return True\n\n        except Exception as e:\n            logger.error(f\"❌ Error updating setup status: {e}\")\n            return False\n\n    def update_detailed_status(self, setup_id, step, status):\n        \"\"\"Update detailed status in database\"\"\"\n        try:\n            import sqlite3\n            import json\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n\n                # Get current detailed status\n                cursor.execute(\"SELECT detailed_status FROM user_brokers WHERE id = ?\", (setup_id,))\n                row = cursor.fetchone()\n\n                if row and row[0]:\n                    try:\n                        detailed_status = json.loads(row[0])\n                    except:\n                        detailed_status = {}\n                else:\n                    detailed_status = {}\n\n                # Update the specific step\n                detailed_status[step] = status\n\n                # Save back to database\n                cursor.execute(\"\"\"\n                    UPDATE user_brokers\n                    SET detailed_status = ?\n                    WHERE id = ?\n                \"\"\", (json.dumps(detailed_status), setup_id))\n                conn.commit()\n\n            logger.info(f\"✅ Updated detailed status for setup {setup_id}: {step} = {status}\")\n            return True\n\n        except Exception as e:\n            logger.error(f\"❌ Error updating detailed status: {e}\")\n            return False\n\n    def fast_auto_connect(self, setup_id):\n        \"\"\"ULTRA FAST auto connection - get auth token only\"\"\"\n        try:\n            logger.info(f\"🚀 ULTRA FAST auto connection for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"success\": False, \"message\": f\"Setup {setup_id} not found\"}\n\n            # Get OpenAlgo instance details\n            http_port = setup_data.get('http_port')\n            base_url = f\"http://127.0.0.1:{http_port}\"\n\n            # FASTEST CONNECTION - Direct API calls only\n            try:\n                import requests\n\n                # Step 1: Quick register (don't wait for response)\n                try:\n                    register_data = {\n                        \"username\": setup_data.get('username'),\n                        \"password\": setup_data.get('username')\n                    }\n                    requests.post(f\"{base_url}/register\", json=register_data, timeout=5)\n                except:\n                    pass  # User might already exist\n\n                # DEPRECATED: This method returns fake responses\n                logger.warning(f\"⚠️ fast_auto_connect is deprecated and returns fake data\")\n                return {\n                    \"success\": False,\n                    \"message\": \"fast_auto_connect is deprecated. Use step_3_register + step_4_connect_broker for real connections.\"\n                }\n\n            except Exception as e:\n                logger.error(f\"Auth error: {e}\")\n                return {\n                    \"success\": False,\n                    \"message\": f\"Connection failed: {str(e)}\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error in ultra fast connection: {e}\")\n            return {\"success\": False, \"message\": f\"Ultra fast connection failed: {str(e)}\"}\n\n    def fast_manual_connect(self, setup_id, trading_pin, totp_code=None):\n        \"\"\"Fast manual connection - simplified approach\"\"\"\n        try:\n            logger.info(f\"🔗 Fast manual connection for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"success\": False, \"message\": f\"Setup {setup_id} not found\"}\n\n            # Get OpenAlgo instance details\n            http_port = setup_data.get('http_port')\n\n            # Quick service check - don't block on health check\n            try:\n                import subprocess\n                # Just check if port is open, don't wait for HTTP response\n                check_cmd = f\"ss -tuln | grep :{http_port}\"\n                result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=3)\n\n                if result.returncode != 0:\n                    return {\"success\": False, \"message\": f\"Algo service not running on port {http_port}\"}\n\n            except Exception as e:\n                # If check fails, continue anyway - service might be starting\n                logger.warning(f\"Service check failed, continuing anyway: {e}\")\n\n            # Temporarily update trading pin in database for this connection\n            original_pin = setup_data.get('trading_pin')\n            try:\n                # Update with user-provided PIN\n                import sqlite3\n                db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n                with sqlite3.connect(db_path) as conn:\n                    cursor = conn.cursor()\n                    cursor.execute(\"\"\"\n                        UPDATE user_brokers\n                        SET trading_pin = ?\n                        WHERE id = ?\n                    \"\"\", (trading_pin, setup_id))\n                    conn.commit()\n\n                    # Ultra-fast manual connection\n                logger.info(f\"🔗 Manual connection with PIN: {trading_pin}\")\n\n                # ULTRA FAST manual connection - Direct API call\n                import requests\n                base_url = f\"http://127.0.0.1:{http_port}\"\n\n                # Step 1: Quick register (don't wait for response)\n                try:\n                    register_data = {\n                        \"username\": setup_data.get('username'),\n                        \"password\": setup_data.get('username')\n                    }\n                    requests.post(f\"{base_url}/register\", json=register_data, timeout=5)\n                except:\n                    pass  # User might already exist\n\n                # DEPRECATED: This method returns fake responses\n                logger.warning(f\"⚠️ fast_manual_connect is deprecated and returns fake data\")\n                return {\n                    \"success\": False,\n                    \"message\": \"fast_manual_connect is deprecated. Use step_3_register + step_4_connect_broker for real connections.\"\n                }\n\n            finally:\n                # Restore original PIN\n                try:\n                    with sqlite3.connect(db_path) as conn:\n                        cursor = conn.cursor()\n                        cursor.execute(\"\"\"\n                            UPDATE user_brokers\n                            SET trading_pin = ?\n                            WHERE id = ?\n                        \"\"\", (original_pin, setup_id))\n                        conn.commit()\n                except Exception as e:\n                    logger.error(f\"Failed to restore original PIN: {e}\")\n\n        except Exception as e:\n            logger.error(f\"❌ Error in fast manual connection: {e}\")\n            return {\"success\": False, \"message\": f\"Fast manual connection failed: {str(e)}\"}\n\n    def manual_broker_connect(self, setup_id, trading_pin, totp_code=None):\n        \"\"\"Manual broker connection with user-provided credentials\"\"\"\n        try:\n            logger.info(f\"🔗 Manual broker connection for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"success\": False, \"message\": f\"Setup {setup_id} not found\"}\n\n            # Update status to connecting\n            self.update_setup_status(setup_id, 'running', 'connecting')\n\n            # For now, simulate manual connection process\n            # In the future, this will redirect to broker login page\n\n            # Step 3: Register user first\n            register_result = self.step_3_register(setup_id)\n\n            if register_result.get('status') != 'success':\n                self.update_setup_status(setup_id, 'running', 'registration_failed')\n                return {\n                    \"success\": False,\n                    \"message\": f\"Registration failed: {register_result.get('message')}\"\n                }\n\n            # Step 4: Connect broker with manual credentials\n            connect_result = self.step_4_connect_broker(setup_id)\n\n            if connect_result.get('status') == 'success':\n                logger.info(f\"✅ Manual broker connection successful for setup: {setup_id}\")\n\n                # Update status to connected\n                self.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Manual broker connection successful\",\n                    \"data\": connect_result\n                }\n            else:\n                logger.error(f\"❌ Manual broker connection failed for setup: {setup_id}\")\n\n                # Update status to connection failed\n                self.update_setup_status(setup_id, 'running', 'connection_failed')\n\n                return {\n                    \"success\": False,\n                    \"message\": connect_result.get('message', 'Manual connection failed')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error in manual broker connection: {e}\")\n\n            # Update status to error\n            self.update_setup_status(setup_id, 'running', 'error')\n\n            return {\n                \"success\": False,\n                \"message\": f\"Manual connection failed: {str(e)}\"\n            }\n\n    def delete_setup(self, setup_id):\n        \"\"\"Delete broker setup (alias for complete_delete_setup)\"\"\"\n        return self.complete_delete_setup(setup_id)\n\n    def complete_delete_setup(self, setup_id):\n        \"\"\"Complete deletion of broker setup including:\n        - Stop systemd service\n        - Delete service file\n        - Delete instance folder\n        - Create backup\n        - Clean database entry\n        \"\"\"\n        try:\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            instance_name = self.get_instance_name(setup_data)\n            service_name = self.get_service_name(setup_data)\n            instance_path = self.instances_dir / instance_name\n\n            print(f\"🗑️ Starting complete deletion of setup: {setup_data['setup_name']}\")\n            print(f\"   Instance: {instance_name}\")\n            print(f\"   Service: {service_name}\")\n\n            # Step 1: Create backup before deletion\n            backup_result = self.create_backup(setup_id)\n            if backup_result.get('status') == 'success':\n                print(f\"✅ Backup created: {backup_result['data']['backup_file']}\")\n            else:\n                print(f\"⚠️ Backup failed: {backup_result.get('message', 'Unknown error')}\")\n\n            # Step 2: Delete systemd service completely\n            print(f\"🛑 Deleting systemd service: {service_name}\")\n            service_delete_result = self.delete_systemd_service(service_name)\n            if service_delete_result.get('status') == 'success':\n                print(f\"✅ {service_delete_result['message']}\")\n            else:\n                print(f\"⚠️ Service deletion warning: {service_delete_result.get('message', 'Unknown error')}\")\n\n            # Step 4: Kill any remaining processes\n            try:\n                # Kill any python processes running from this instance\n                subprocess.run(['/usr/bin/sudo', '/usr/bin/pkill', '-f', f'python.*{instance_name}'],\n                             check=False, capture_output=True)\n\n                # Kill any processes using the instance port\n                port = 5000 + setup_id\n                port_pids = subprocess.run(['/usr/bin/sudo', '/usr/bin/lsof', '-ti', f':{port}'],\n                                         capture_output=True, text=True, check=False)\n                if port_pids.stdout.strip():\n                    for pid in port_pids.stdout.strip().split('\\n'):\n                        if pid.strip():\n                            subprocess.run(['/usr/bin/sudo', '/usr/bin/kill', '-9', pid.strip()],\n                                         check=False, capture_output=True)\n                print(f\"✅ All processes for {instance_name} terminated\")\n            except Exception as e:\n                print(f\"⚠️ Error killing processes: {e}\")\n\n            # Step 5: Delete instance folder\n            if instance_path.exists():\n                try:\n                    shutil.rmtree(instance_path)\n                    print(f\"✅ Instance folder deleted: {instance_path}\")\n                except Exception as e:\n                    print(f\"⚠️ Error deleting instance folder: {e}\")\n            else:\n                print(f\"ℹ️ Instance folder not found: {instance_path}\")\n\n            # Step 6: Completely remove database entry\n            try:\n                import sqlite3\n                conn = sqlite3.connect(self.db_path)\n                cursor = conn.cursor()\n\n                # Actually delete the entry for clean system\n                cursor.execute(\"DELETE FROM user_brokers WHERE id = ?\", (setup_id,))\n\n                conn.commit()\n                conn.close()\n                print(f\"✅ Database entry completely removed\")\n            except Exception as e:\n                print(f\"⚠️ Error deleting from database: {e}\")\n\n            # Step 7: Clean up any remaining systemd files\n            try:\n                service_file = f\"/etc/systemd/system/{service_name}.service\"\n                if os.path.exists(service_file):\n                    subprocess.run(['/usr/bin/sudo', '/usr/bin/rm', '-f', service_file],\n                                 check=False, capture_output=True)\n                    print(f\"✅ Systemd service file removed: {service_file}\")\n\n                # Reload systemd daemon\n                subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'daemon-reload'],\n                             check=False, capture_output=True)\n                print(f\"✅ Systemd daemon reloaded\")\n            except Exception as e:\n                print(f\"⚠️ Error cleaning systemd files: {e}\")\n\n            # Step 8: Final cleanup - remove any temp files\n            try:\n                temp_patterns = [\n                    f\"/tmp/*{instance_name}*\",\n                    f\"/tmp/*{setup_id}*\"\n                ]\n                for pattern in temp_patterns:\n                    subprocess.run(['/usr/bin/sudo', '/usr/bin/rm', '-rf'] + [pattern],\n                                 shell=True, check=False, capture_output=True)\n                print(f\"✅ Temporary files cleaned\")\n            except Exception as e:\n                print(f\"⚠️ Error cleaning temp files: {e}\")\n\n            print(f\"🎉 Complete deletion successful for setup: {setup_data['setup_name']}\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Setup '{setup_data['setup_name']}' completely deleted\",\n                \"data\": {\n                    \"setup_id\": setup_id,\n                    \"instance_name\": instance_name,\n                    \"service_name\": service_name,\n                    \"backup_created\": backup_result.get('status') == 'success'\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to delete setup: {str(e)}\"}\n\n    def get_next_available_port(self):\n        \"\"\"Get next available port for OpenAlgo instance\"\"\"\n        used_ports = []\n        \n        # Check existing instances\n        if self.instances_dir.exists():\n            for instance_dir in self.instances_dir.iterdir():\n                if instance_dir.is_dir():\n                    env_file = instance_dir / \".env\"\n                    if env_file.exists():\n                        with open(env_file, 'r') as f:\n                            content = f.read()\n                            for line in content.split('\\n'):\n                                if line.startswith('PORT='):\n                                    port = int(line.split('=')[1])\n                                    used_ports.append(port)\n        \n        # Find next available port\n        port = self.base_port\n        while port in used_ports:\n            port += 1\n        \n        return port\n\n    def get_next_instance_number(self, username, broker_name):\n        \"\"\"Get the next available instance number for user-broker combination\"\"\"\n\n        try:\n            import sqlite3\n\n            # Connect to our database\n            conn = sqlite3.connect(self.db_path)\n            cursor = conn.cursor()\n\n            # Get highest instance number for this user-broker\n            cursor.execute(\"\"\"\n                SELECT MAX(instance_number) FROM user_brokers\n                WHERE username = ? AND broker_name = ?\n            \"\"\", (username, broker_name))\n\n            result = cursor.fetchone()\n            max_instance = result[0] if result[0] is not None else 0\n\n            conn.close()\n\n            return max_instance + 1\n\n        except Exception as e:\n            print(f\"⚠️ Failed to get next instance number: {e}\")\n            return 1  # Default to 1 if error\n\n    def create_instance(self, setup_id):\n        \"\"\"Complete instance creation - folder, clone, configure\"\"\"\n\n        try:\n            # Get setup details with user information\n            setup = self.get_setup_data_with_user(setup_id)\n            if not setup:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            # Get next instance number if not set\n            if not setup.get('instance_number'):\n                instance_number = self.get_next_instance_number(setup.get('username', '1000'), setup['broker_name'])\n            else:\n                instance_number = setup['instance_number']\n\n            # Create instance name: username-broker-instance format\n            instance_name = f\"{setup.get('username', '1000')}-{setup['broker_name']}-{instance_number}\"\n            instance_path = self.instances_dir / instance_name\n\n            print(f\"🔧 Creating instance: {instance_name}\")\n\n            # Step 1: Create instance folder\n            if instance_path.exists():\n                print(f\"📁 Instance folder already exists: {instance_path}\")\n            else:\n                instance_path.mkdir(parents=True, exist_ok=True)\n                print(f\"✅ Created instance folder: {instance_path}\")\n\n            # Step 2: Clone OpenAlgo if not already present\n            if not (instance_path / \"app.py\").exists():\n                print(f\"📥 Cloning OpenAlgo...\")\n                clone_success = self.clone_openalgo_source(instance_path)\n                if not clone_success:\n                    return {\"status\": \"error\", \"message\": \"Failed to clone OpenAlgo\"}\n            else:\n                print(f\"✅ OpenAlgo already present in instance\")\n\n            # Step 3: Configure .env file\n            print(f\"⚙️ Configuring environment...\")\n            config_success = self.configure_env_file(instance_path, setup)\n            if not config_success:\n                return {\"status\": \"error\", \"message\": \"Failed to configure environment\"}\n\n            print(f\"✅ Instance setup completed: {instance_name}\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": \"Instance created successfully\",\n                \"instance_name\": instance_name,\n                \"instance_path\": str(instance_path)\n            }\n\n        except Exception as e:\n            print(f\"❌ Instance creation failed: {e}\")\n            return {\"status\": \"error\", \"message\": str(e)}\n\n    def create_instance_folder(self, setup_id, broker_name, setup_name):\n        \"\"\"Create OpenAlgo instance folder for broker setup\"\"\"\n        \n        # Get user data for multi-user naming\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Create unique instance name with multi-user format: username-algo-broker-setupid\n        instance_name = self.get_instance_name(setup_data)\n        instance_path = self.instances_dir / instance_name\n        \n        # Remove existing instance if it exists\n        if instance_path.exists():\n            shutil.rmtree(instance_path)\n\n        # Don't create the directory - let git clone create it\n        print(f\"✅ Prepared instance path: {instance_path}\")\n        return instance_path\n    \n    def clone_openalgo_source(self, instance_path):\n        \"\"\"Clone OpenAlgo source code directly into instance folder\"\"\"\n\n        try:\n            print(f\"🔍 DEBUG: Starting clone process...\")\n            print(f\"🔍 DEBUG: Target path: {instance_path}\")\n            print(f\"🔍 DEBUG: Path exists: {instance_path.exists()}\")\n            print(f\"🔍 DEBUG: Parent directory: {instance_path.parent}\")\n            print(f\"🔍 DEBUG: Parent exists: {instance_path.parent.exists()}\")\n\n            # Ensure parent directory exists\n            instance_path.parent.mkdir(parents=True, exist_ok=True)\n\n            # Remove existing directory if it exists\n            if instance_path.exists():\n                print(f\"🗑️ Removing existing directory: {instance_path}\")\n                shutil.rmtree(instance_path)\n\n            # Clone OpenAlgo repository directly into instance folder\n            print(f\"📥 Cloning OpenAlgo from GitHub to: {instance_path}\")\n            print(f\"🔍 DEBUG: Git command: /usr/bin/git clone https://github.com/marketcalls/openalgo.git {instance_path}\")\n\n            result = subprocess.run([\n                '/usr/bin/git', 'clone',\n                'https://github.com/marketcalls/openalgo.git',\n                str(instance_path)\n            ], capture_output=True, text=True, timeout=120)\n\n            print(f\"🔍 DEBUG: Git return code: {result.returncode}\")\n            print(f\"🔍 DEBUG: Git STDOUT: {result.stdout}\")\n            print(f\"🔍 DEBUG: Git STDERR: {result.stderr}\")\n\n            if result.returncode != 0:\n                print(f\"❌ Git clone failed with return code {result.returncode}\")\n                raise Exception(f\"Git clone failed: {result.stderr}\")\n\n            print(f\"✅ OpenAlgo cloned successfully to: {instance_path}\")\n\n            # Verify the clone was successful by checking for key files\n            app_py_path = instance_path / \"app.py\"\n            print(f\"🔍 DEBUG: Checking for app.py at: {app_py_path}\")\n            print(f\"🔍 DEBUG: app.py exists: {app_py_path.exists()}\")\n\n            if not app_py_path.exists():\n                # List what files are actually there\n                if instance_path.exists():\n                    files = list(instance_path.iterdir())\n                    print(f\"🔍 DEBUG: Files in directory: {[f.name for f in files[:10]]}\")\n                raise Exception(\"Clone verification failed: app.py not found\")\n\n            return True\n\n        except Exception as e:\n            print(f\"❌ Failed to clone OpenAlgo: {e}\")\n            import traceback\n            traceback.print_exc()\n            return False\n\n    def configure_env_file(self, instance_path, setup_data):\n        \"\"\"Configure .env file with broker credentials, user data, and XTS support\"\"\"\n\n        env_file = instance_path / \".env\"\n        sample_env_file = instance_path / \".sample.env\"\n\n        # Copy .sample.env to .env first\n        if sample_env_file.exists():\n            shutil.copy2(sample_env_file, env_file)\n            print(f\"📋 Copied .sample.env to .env\")\n        else:\n            print(f\"⚠️ .sample.env not found, creating basic .env\")\n            with open(env_file, 'w') as f:\n                f.write(\"ENV_CONFIG_VERSION = '1.0.1'\\n\")\n\n        # Read the .env content\n        with open(env_file, 'r') as f:\n            env_content = f.read()\n\n        # Calculate ports based on setup_id\n        setup_id = setup_data['id']\n        port = 5000 + setup_id              # HTTP port: 5000 + setup_id\n        websocket_port = 8000 + setup_id    # WebSocket port: 8000 + setup_id\n        zmq_port = 6000 + setup_id          # ZMQ port: 6000 + setup_id\n\n        # Determine broker type (XTS or standard)\n        broker_type = self.get_broker_type(setup_data['broker_name'])\n\n        # Base configurations - use calculated ports for all configurations\n        modifications = {\n            'BROKER_API_KEY': setup_data['broker_api_key'],\n            'BROKER_API_SECRET': setup_data['broker_api_secret'],\n            'REDIRECT_URL': f'http://127.0.0.1:{port}/{setup_data[\"broker_name\"]}/callback',\n            'HOST_SERVER': f'http://127.0.0.1:{port}',\n            'FLASK_PORT': str(port),  # HTTP port\n            'WEBSOCKET_PORT': str(websocket_port),  # WebSocket port\n            'WEBSOCKET_URL': f'ws://localhost:{websocket_port}',  # WebSocket URL\n            'ZMQ_PORT': str(zmq_port),  # ZMQ port\n            'CORS_ALLOWED_ORIGINS': f'http://127.0.0.1:{port}',  # CORS for current port\n            'CSRF_ENABLED': 'FALSE',  # Disable CSRF for automation\n            # Increase rate limits by 10X for automation (prevents login failures)\n            'LOGIN_RATE_LIMIT_MIN': '50 per minute',  # 5 -> 50 (10X)\n            'LOGIN_RATE_LIMIT_HOUR': '250 per hour',  # 25 -> 250 (10X)\n            'API_RATE_LIMIT': '100 per second',  # 10 -> 100 (10X)\n        }\n\n        # Add XTS broker market API keys if needed\n        if broker_type == 'xts':\n            print(f\"🔧 Configuring XTS broker with market API keys\")\n            modifications.update({\n                'BROKER_API_KEY_MARKET': setup_data.get('broker_api_key_market', ''),\n                'BROKER_API_SECRET_MARKET': setup_data.get('broker_api_secret_market', '')\n            })\n\n        # Note: AlgoFactory data is now stored in our database, not in .env files\n\n        # Update .env content\n        lines = env_content.split('\\n')\n        updated_lines = []\n        modified_keys = set()\n\n        for line in lines:\n            if '=' in line and not line.strip().startswith('#'):\n                key = line.split('=')[0].strip()\n                if key in modifications:\n                    updated_lines.append(f\"{key} = '{modifications[key]}'\")\n                    modified_keys.add(key)\n                else:\n                    updated_lines.append(line)\n            else:\n                updated_lines.append(line)\n\n        # Clean .env file - no AlgoFactory data added\n\n        # Write updated .env file\n        with open(env_file, 'w') as f:\n            f.write('\\n'.join(updated_lines))\n\n        print(f\"✅ Configured .env file for {broker_type.upper()} broker: {env_file}\")\n        print(f\"🚀 Rate limits increased 10X for automation: LOGIN=50/min, API=100/sec\")\n        return port\n\n    def fix_socketio_async_mode(self, instance_path):\n        \"\"\"Skip SocketIO setup - let OpenAlgo run normally without auto-binding\"\"\"\n        extensions_file = instance_path / \"extensions.py\"\n\n        print(f\"⚠️ Skipping SocketIO auto-setup - OpenAlgo will run normally without auto-binding\")\n        print(f\"📝 Extensions file: {extensions_file}\")\n        print(f\"🔧 Manual start/stop control enabled - no automatic SocketIO binding\")\n\n        # Do not modify extensions.py - let OpenAlgo run with its default configuration\n        # This prevents automatic SocketIO startup and binding issues\n\n    def get_broker_type(self, broker_name):\n        \"\"\"Determine if broker is XTS type or standard\"\"\"\n        xts_brokers = ['fivepaisaxts', 'compositedge', 'iifl']  # Add more XTS brokers as needed\n        return 'xts' if broker_name in xts_brokers else 'standard'\n\n    def get_port_from_env_content(self, env_content):\n        \"\"\"Get port from .env file content, return OpenAlgo default if not found\"\"\"\n        for line in env_content.split('\\n'):\n            if 'FLASK_PORT' in line and '=' in line:\n                try:\n                    port_str = line.split('=')[1].strip().strip(\"'\\\"\")\n                    return int(port_str)\n                except:\n                    pass\n        # Return OpenAlgo default port\n        return 5000\n\n    def install_dependencies_shared_venv(self, instance_path):\n        \"\"\"Install OpenAlgo dependencies using shared virtual environment\"\"\"\n\n        try:\n            print(f\"📦 Installing dependencies using shared virtual environment...\")\n\n            # Use the shared virtual environment\n            venv_python = self.base_dir / \"env/bin/python\"\n            venv_pip = self.base_dir / \"env/bin/pip\"\n\n            if not venv_python.exists():\n                print(f\"❌ Shared virtual environment not found: {venv_python}\")\n                return False\n\n            # Check if requirements.txt exists\n            requirements_file = instance_path / \"requirements.txt\"\n            if requirements_file.exists():\n                # Install requirements using shared venv\n                result = subprocess.run([\n                    str(venv_pip), 'install', '-r', str(requirements_file)\n                ], capture_output=True, text=True, cwd=str(instance_path))\n\n                if result.returncode != 0:\n                    print(f\"⚠️ Warning: pip install failed: {result.stderr}\")\n                    return False\n                else:\n                    print(f\"✅ Dependencies installed successfully in shared venv\")\n                    return True\n            else:\n                print(f\"⚠️ No requirements.txt found, skipping dependency installation\")\n                return True\n\n        except Exception as e:\n            print(f\"⚠️ Warning: Failed to install dependencies: {e}\")\n            return False\n\n    def install_dependencies_instance_venv(self, instance_path):\n        \"\"\"Install OpenAlgo dependencies using instance-specific virtual environment\"\"\"\n\n        try:\n            print(f\"📦 Creating virtual environment for {instance_path.name}...\")\n\n            # Create instance-specific virtual environment\n            venv_path = instance_path / \"venv\"\n\n            # Remove existing venv if it exists\n            if venv_path.exists():\n                shutil.rmtree(venv_path)\n\n            # Create new virtual environment\n            result = subprocess.run([\n                \"python3\", \"-m\", \"venv\", str(venv_path)\n            ], capture_output=True, text=True)\n\n            if result.returncode != 0:\n                print(f\"❌ Failed to create virtual environment: {result.stderr}\")\n                return False\n\n            # Install requirements in the new venv\n            venv_python = venv_path / \"bin\" / \"python\"\n            venv_pip = venv_path / \"bin\" / \"pip\"\n            requirements_file = instance_path / \"requirements.txt\"\n\n            if not requirements_file.exists():\n                print(f\"⚠️ No requirements.txt found in {instance_path}\")\n                return True\n\n            # Upgrade pip first\n            subprocess.run([str(venv_pip), \"install\", \"--upgrade\", \"pip\"], capture_output=True)\n\n            # Install requirements\n            result = subprocess.run([\n                str(venv_pip), \"install\", \"-r\", str(requirements_file)\n            ], capture_output=True, text=True, cwd=str(instance_path))\n\n            if result.returncode == 0:\n                print(f\"✅ Dependencies installed successfully in instance venv\")\n                return True\n            else:\n                print(f\"❌ Failed to install dependencies: {result.stderr}\")\n                return False\n\n        except Exception as e:\n            print(f\"❌ Dependency installation failed: {e}\")\n            return False\n\n    def start_openalgo_instance(self, instance_path, port, setup_data):\n        \"\"\"Start OpenAlgo instance with Gunicorn\"\"\"\n\n        try:\n            print(f\"🚀 Starting OpenAlgo instance on port {port}...\")\n\n            # Create Gunicorn configuration\n            gunicorn_conf = instance_path / \"gunicorn.conf.py\"\n            gunicorn_config = f\"\"\"# Gunicorn configuration for {setup_data['setup_name']}\nbind = \"0.0.0.0:{port}\"\nworkers = 1\nworker_class = \"uvicorn.workers.UvicornWorker\"\nworker_connections = 1000\nmax_requests = 1000\nmax_requests_jitter = 100\ntimeout = 30\nkeepalive = 2\npreload_app = True\ndaemon = True\npidfile = \"{instance_path}/gunicorn.pid\"\naccesslog = \"{instance_path}/access.log\"\nerrorlog = \"{instance_path}/error.log\"\nloglevel = \"info\"\n\"\"\"\n\n            with open(gunicorn_conf, 'w') as f:\n                f.write(gunicorn_config)\n\n            # Start Gunicorn process\n            cmd = [\n                'gunicorn',\n                '-c', str(gunicorn_conf),\n                'app:app'  # Assuming main file is app.py\n            ]\n\n            result = subprocess.run(\n                cmd,\n                cwd=str(instance_path),\n                capture_output=True,\n                text=True\n            )\n\n            if result.returncode != 0:\n                print(f\"⚠️ Gunicorn start warning: {result.stderr}\")\n\n            # Wait a moment for startup\n            time.sleep(2)\n\n            # Check if process is running\n            if self.check_instance_running(port):\n                print(f\"✅ Algo instance started successfully on port {port}\")\n                return True\n            else:\n                print(f\"⚠️ Algo instance may not be running properly\")\n                return False\n\n        except Exception as e:\n            print(f\"❌ Failed to start OpenAlgo instance: {e}\")\n            return False\n\n    def check_instance_running(self, port):\n        \"\"\"Check if OpenAlgo instance is running on specified port\"\"\"\n\n        try:\n            response = requests.get(f\"http://127.0.0.1:{port}\", timeout=5)\n            return response.status_code == 200\n        except:\n            return False\n\n    def get_actual_running_port(self, setup_id):\n        \"\"\"Get the actual port where the service is running from database\"\"\"\n        try:\n            import sqlite3\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n                cursor.execute(\"\"\"\n                    SELECT http_port, setup_status\n                    FROM user_brokers\n                    WHERE id = ?\n                \"\"\", (setup_id,))\n\n                result = cursor.fetchone()\n                if result:\n                    http_port, setup_status = result\n                    if http_port:\n                        print(f\"🔍 Found HTTP port {http_port} from database for setup {setup_id}\")\n\n                        # Verify the port is actually running\n                        if self.check_instance_running(http_port):\n                            print(f\"✅ Confirmed port {http_port} is running\")\n                            return http_port\n                        else:\n                            print(f\"⚠️ Port {http_port} from database is not responding\")\n                    else:\n                        print(f\"⚠️ No HTTP port stored in database for setup {setup_id}\")\n                else:\n                    print(f\"⚠️ Setup {setup_id} not found in database\")\n\n            # Fallback: try calculated port\n            calculated_port = 5000 + setup_id\n            print(f\"🔄 Trying fallback calculated port: {calculated_port}\")\n            if self.check_instance_running(calculated_port):\n                print(f\"✅ Fallback port {calculated_port} is working\")\n                return calculated_port\n\n            print(f\"❌ No working port found for setup {setup_id}\")\n            return None\n\n        except Exception as e:\n            print(f\"❌ Error getting port from database: {e}\")\n            return None\n\n    def auto_register_user(self, port, setup_data):\n        \"\"\"Auto-register user in Algo\"\"\"\n\n        try:\n            print(f\"👤 Auto-registering user in Algo...\")\n\n            algo_url = f\"http://127.0.0.1:{port}\"\n            session = requests.Session()\n\n            # Wait for Algo to fully start (reduced for faster connection)\n            print(\"⏳ Waiting for Algo to initialize...\")\n            time.sleep(3)\n\n            # Check if Algo is responding\n            for attempt in range(5):\n                try:\n                    response = session.get(algo_url, timeout=5)\n                    if response.status_code == 200:\n                        break\n                    print(f\"⏳ Attempt {attempt + 1}: Algo not ready yet...\")\n                    time.sleep(2)\n                except:\n                    print(f\"⏳ Attempt {attempt + 1}: Algo not responding yet...\")\n                    time.sleep(2)\n            else:\n                print(\"❌ Algo instance not responding after multiple attempts\")\n                return False\n\n            # Get setup page (initial setup for OpenAlgo)\n            response = session.get(f\"{algo_url}/setup\", timeout=10)\n            if response.status_code != 200:\n                print(f\"❌ Cannot access setup page: {response.status_code}\")\n                print(f\"Response text: {response.text[:200]}...\")\n                return False\n\n            # Setup admin user with standard password pattern (CSRF disabled)\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            user_password = f\"algofactory{username}\"  # Standard password pattern\n            setup_data_form = {\n                \"username\": username,\n                \"email\": setup_data['email'],\n                \"password\": user_password\n            }\n            print(f\"🔐 Setting up user with username: {username} and password: algofactory{username}\")\n\n            setup_response = session.post(\n                f\"{algo_url}/setup\",\n                data=setup_data_form,\n                timeout=10\n            )\n\n            if setup_response.status_code == 200 or \"login\" in setup_response.url:\n                print(\"✅ Admin user setup completed successfully in Algo\")\n                return True\n            else:\n                print(f\"❌ Setup failed: {setup_response.status_code}\")\n                print(f\"Response text: {setup_response.text[:200]}...\")\n                return False\n\n        except Exception as e:\n            print(f\"❌ Auto-registration failed: {e}\")\n            return False\n\n    def auto_login(self, port, setup_data):\n        \"\"\"Auto-login to OpenAlgo and return session\"\"\"\n\n        try:\n            print(f\"🔐 Auto-logging into OpenAlgo...\")\n\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n            session = requests.Session()\n\n            # Get login page\n            login_page = session.get(f\"{openalgo_url}/auth/login\", timeout=10)\n            if login_page.status_code != 200:\n                print(f\"❌ Cannot access login page: {login_page.status_code}\")\n                return None\n\n            # Submit login (CSRF disabled) - Use standard password pattern\n            # Use the actual username from users table, not the one from user_brokers\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))  # This is from users table via JOIN\n            user_password = f\"algofactory{username}\"  # Standard password pattern\n            login_data = {\n                \"username\": username,\n                \"password\": user_password\n            }\n            print(f\"🔐 Logging in with username: {username} and password: algofactory{username}\")\n\n            # Handle rate limiting with retry mechanism\n            for attempt in range(3):\n                if attempt > 0:\n                    print(f\"⏳ Retrying login (attempt {attempt + 1}/3) after rate limit...\")\n                    time.sleep(5)  # Wait 5 seconds between attempts\n\n                login_response = session.post(\n                    f\"{openalgo_url}/auth/login\",\n                    data=login_data,\n                    allow_redirects=True,\n                    timeout=10\n                )\n\n                if login_response.status_code == 200:\n                    # Check if we're redirected away from login page (successful login)\n                    if \"login\" not in login_response.url or \"dashboard\" in login_response.url or \"broker\" in login_response.url:\n                        print(\"✅ OpenAlgo login successful\")\n                        return session\n                    else:\n                        print(f\"⚠️ Login response 200 but still on login page: {login_response.url}\")\n                        continue\n                elif login_response.status_code == 429:\n                    print(f\"⚠️ Rate limited (429), waiting before retry...\")\n                    continue\n                else:\n                    print(f\"❌ Login failed: {login_response.status_code}\")\n                    return None\n\n            print(\"❌ Login failed after all retry attempts\")\n            return None\n\n        except Exception as e:\n            print(f\"❌ Auto-login failed: {e}\")\n            return None\n\n    def get_setup_connection_status(self, setup_id):\n        \"\"\"Get connection status from our database\"\"\"\n        try:\n            import sqlite3\n            with sqlite3.connect(self.db_path) as conn:\n                cursor = conn.cursor()\n                cursor.execute(\"SELECT connection_status FROM user_brokers WHERE id = ?\", (setup_id,))\n                row = cursor.fetchone()\n                return row[0] if row else None\n        except Exception as e:\n            print(f\"⚠️ Failed to get setup status: {e}\")\n            return None\n\n    def check_existing_connection(self, port, setup_data, session):\n        \"\"\"Ultra-fast check if broker is already connected\"\"\"\n        try:\n            print(f\"🔍 Ultra-fast connection check...\")\n\n            # FASTEST: Check OpenAlgo database directly for auth tokens\n            instance_name = self.get_instance_name(setup_data)\n            instance_path = self.instances_dir / instance_name\n            algo_db_path = instance_path / \"db\" / \"openalgo.db\"\n\n            if algo_db_path.exists():\n                import sqlite3\n                with sqlite3.connect(algo_db_path) as conn:\n                    conn.row_factory = sqlite3.Row\n                    cursor = conn.cursor()\n\n                    # Check if auth tokens exist and are not revoked\n                    cursor.execute(\"SELECT auth, is_revoked FROM auth WHERE auth != '' AND auth IS NOT NULL AND (is_revoked = 0 OR is_revoked IS NULL) LIMIT 1\")\n                    auth_row = cursor.fetchone()\n\n                    if auth_row and auth_row['auth'] and len(auth_row['auth']) > 10:\n                        print(\"✅ Found valid auth tokens in OpenAlgo database\")\n                        return True\n                    else:\n                        print(\"❌ No valid auth tokens found (empty, revoked, or missing)\")\n                        return False\n\n            print(\"❌ No valid connection found - proceeding with authentication\")\n            return False\n\n        except Exception as e:\n            print(f\"⚠️ Fast connection check failed: {e} - proceeding with authentication\")\n            return False\n\n    def auto_broker_auth(self, port, setup_data, session):\n        \"\"\"Auto-authenticate broker in OpenAlgo\"\"\"\n\n        try:\n            print(f\"🏦 Auto-authenticating {setup_data['broker_name']} broker...\")\n\n            # FAST CHECK: See if already connected (2-3 seconds)\n            # BUT ONLY if our database also shows connected\n            our_db_status = self.get_setup_connection_status(setup_data.get('id'))\n            if our_db_status == 'connected' and self.check_existing_connection(port, setup_data, session):\n                print(\"⚡ Broker already connected - skipping authentication!\")\n                return True\n            elif our_db_status == 'disconnected':\n                print(\"🔄 Database shows disconnected - forcing full authentication\")\n            else:\n                print(\"🔄 No existing connection or status mismatch - proceeding with authentication\")\n\n            print(f\"🔄 No existing connection - proceeding with authentication...\")\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n\n            # Navigate to broker selection page\n            broker_page = session.get(f\"{openalgo_url}/auth/broker\", timeout=10)\n            if broker_page.status_code != 200:\n                print(f\"❌ Cannot access broker page: {broker_page.status_code}\")\n                return False\n\n            print(f\"✅ Accessed broker selection page\")\n\n            # Find and click the Angel broker connect button\n            # Look for the connect URL in the page\n            if \"angel\" in broker_page.text.lower():\n                print(f\"✅ Found Angel broker option\")\n\n                # Try to find the connect link for Angel\n                import re\n                connect_match = re.search(r'href=\"([^\"]*angel[^\"]*)\"', broker_page.text)\n                if connect_match:\n                    connect_path = connect_match.group(1)\n                    if not connect_path.startswith('http'):\n                        connect_url = f\"{openalgo_url}{connect_path}\"\n                    else:\n                        connect_url = connect_path\n                else:\n                    # Fallback to standard Angel callback URL\n                    connect_url = f\"{openalgo_url}/angel/callback\"\n\n                # Navigate directly to Angel callback URL to get the form\n                angel_auth_url = f\"{openalgo_url}/angel/callback\"\n                print(f\"🔗 Getting Angel authentication form from: {angel_auth_url}\")\n\n                # GET request to get the authentication form (faster timeout)\n                auth_form_page = session.get(angel_auth_url, timeout=5)\n\n                if auth_form_page.status_code != 200:\n                    print(f\"❌ Cannot access Angel auth form: {auth_form_page.status_code}\")\n                    return False\n\n                print(f\"✅ Retrieved Angel authentication form\")\n\n                # The form submits back to the same URL via POST\n                submit_url = angel_auth_url\n            else:\n                print(f\"❌ Angel broker not found on broker page\")\n                return False\n\n            # Generate TOTP\n            totp_code = self.generate_totp(setup_data['totp_secret'])\n            if not totp_code:\n                print(\"❌ Failed to generate TOTP\")\n                return False\n\n            print(f\"🔐 Generated TOTP: {totp_code}\")\n            print(f\"🔑 Using Client ID: {setup_data['broker_client_id']}\")\n\n            # Submit broker authentication (CSRF disabled)\n            # Use the exact field names from OpenAlgo Angel form\n            auth_data = {\n                \"clientid\": setup_data['broker_client_id'],  # OpenAlgo uses 'clientid' not 'client_id'\n                \"pin\": setup_data['trading_pin'],\n                \"totp\": totp_code\n            }\n\n            print(f\"🚀 Submitting broker authentication to: {submit_url}\")\n            print(f\"📝 Auth data: {auth_data}\")\n\n            auth_response = session.post(\n                submit_url,\n                data=auth_data,\n                allow_redirects=True,\n                timeout=15  # Reduced timeout for faster response\n            )\n\n            print(f\"📡 Auth response status: {auth_response.status_code}\")\n            print(f\"📡 Auth response URL: {auth_response.url}\")\n            print(f\"📄 Response preview: {auth_response.text[:500]}...\")\n\n            if auth_response.status_code == 200:\n                # Check if authentication was successful\n                if \"success\" in auth_response.text.lower() or \"authenticated\" in auth_response.text.lower():\n                    print(\"✅ Broker authentication successful\")\n\n                    # Wait for OpenAlgo to process and store tokens (reduced)\n                    import time\n                    time.sleep(2)\n\n                    # Retrieve and save tokens from Algo database using updated method\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n                    if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):\n                        print(\"✅ OpenAlgo API key retrieved and saved successfully\")\n                        return True\n                    else:\n                        print(\"⚠️ Authentication appeared successful but no tokens found in database\")\n                        print(\"🔍 This indicates the broker authentication didn't complete properly in Algo\")\n                        return False\n                else:\n                    print(\"❌ Broker authentication failed - no success indication\")\n                    return False\n            else:\n                print(f\"❌ Broker authentication failed: {auth_response.status_code}\")\n                return False\n\n        except Exception as e:\n            print(f\"❌ Auto-broker authentication failed: {e}\")\n\n            # If it's a timeout, check if tokens already exist\n            if \"timeout\" in str(e).lower() or \"timed out\" in str(e).lower():\n                print(\"⚠️ Broker authentication timed out, but checking if tokens already exist...\")\n\n                # Even if the API call timed out, check if tokens already exist\n                try:\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n                    if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):\n                        print(\"✅ Found existing tokens! Broker was already authenticated\")\n                        return True\n                    else:\n                        print(\"❌ No existing tokens found\")\n                        return False\n                except Exception as token_error:\n                    print(f\"❌ Token retrieval also failed: {token_error}\")\n                    return False\n\n            return False\n\n    def generate_totp(self, totp_secret):\n        \"\"\"Generate TOTP code\"\"\"\n\n        try:\n            totp = pyotp.TOTP(totp_secret)\n            return totp.now()\n        except Exception as e:\n            print(f\"❌ TOTP generation failed: {e}\")\n            return None\n\n    def retrieve_and_save_tokens(self, port, setup_data):\n        \"\"\"Retrieve authentication tokens from Algo database and save decrypted keys to our database\"\"\"\n\n        try:\n            print(f\"🔍 Retrieving tokens from Algo database...\")\n\n            # Algo database path (in db subfolder)\n            instance_name = self.get_instance_name(setup_data)\n            instance_path = self.instances_dir / instance_name\n            algo_db_path = instance_path / \"db\" / \"openalgo.db\"  # Still named openalgo.db in the repo\n\n            if not algo_db_path.exists():\n                print(f\"❌ Algo database not found: {algo_db_path}\")\n                return None\n\n            # Connect to Algo database\n            conn = sqlite3.connect(algo_db_path)\n            conn.row_factory = sqlite3.Row\n            cursor = conn.cursor()\n\n            # Check auth table for broker authentication tokens\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            cursor.execute(\"SELECT * FROM auth WHERE user_id = ? OR name = ? LIMIT 1\", (username, username))\n            auth_row = cursor.fetchone()\n\n            # Check symtoken table for master contracts (optimized for speed)\n            print(f\"📊 Checking symbol download status...\")\n            symbol_download_complete = False\n            max_wait_time = 30  # Reduced wait time for faster response\n            wait_start = time.time()\n\n            # First quick check - if symbols already exist, don't wait\n            cursor.execute(\"SELECT COUNT(*) as count FROM symtoken\")\n            symtoken_count = cursor.fetchone()['count']\n\n            if symtoken_count > 100000:  # Angel typically has 120k+ symbols\n                symbol_download_complete = True\n                print(f\"✅ Symbol download already complete: {symtoken_count} symbols\")\n            else:\n                print(f\"⏳ Symbol download in progress: {symtoken_count} symbols\")\n                # Only wait if symbols are actively downloading\n                while not symbol_download_complete and (time.time() - wait_start) < max_wait_time:\n                    time.sleep(3)  # Reduced check interval\n                    cursor.execute(\"SELECT COUNT(*) as count FROM symtoken\")\n                    symtoken_count = cursor.fetchone()['count']\n\n                    if symtoken_count > 100000:\n                        symbol_download_complete = True\n                        print(f\"✅ Symbol download complete: {symtoken_count} symbols\")\n                    else:\n                        print(f\"⏳ Symbol download progress: {symtoken_count} symbols\")\n\n            if not symbol_download_complete:\n                print(f\"⚠️ Symbol download timeout after {max_wait_time}s: {symtoken_count} symbols (continuing anyway)\")\n\n            # Check api_keys table for API keys\n            cursor.execute(\"SELECT * FROM api_keys WHERE user_id = ? LIMIT 1\", (username,))\n            api_key_row = cursor.fetchone()\n\n            conn.close()\n\n            # Decrypt and save API keys to our database\n            decrypted_tokens = self.decrypt_and_save_api_keys(auth_row, api_key_row, setup_data, symtoken_count)\n\n            if decrypted_tokens:\n                print(f\"✅ OpenAlgo setup complete: Broker_Auth={decrypted_tokens['has_auth_token']}, API_Key={decrypted_tokens['has_openalgo_api_key']}, Symbols={symtoken_count}\")\n                return decrypted_tokens\n            else:\n                print(\"❌ No authentication tokens found in Algo database\")\n                return None\n\n        except Exception as e:\n            print(f\"❌ Token retrieval failed: {e}\")\n            return None\n\n    # REMOVED: step_4_connect_broker_manual_init() - Duplicate method, use run_complete_manual_setup() instead\n\n    # REMOVED: step_4_connect_broker_manual_verify() - Duplicate method, use connect_with_manual_otp() instead\n\n    # REMOVED: step_4_connect_broker_with_credentials() - Duplicate method #1\n\n    # REMOVED: step_4_connect_broker_with_terminal_totp() - Legacy method, not needed\n\n    def decrypt_and_save_api_keys(self, auth_row, api_key_row, setup_data, symbol_count):\n        \"\"\"Decrypt OpenAlgo API keys from Algo database and save to our database\"\"\"\n\n        try:\n            from cryptography.fernet import Fernet\n            import os\n\n            # Get encryption keys from Algo .env file\n            instance_name = self.get_instance_name(setup_data)\n            instance_path = self.instances_dir / instance_name\n            env_file = instance_path / \".env\"\n\n            app_key = None\n            api_key_pepper = None\n\n            if env_file.exists():\n                with open(env_file, 'r') as f:\n                    for line in f:\n                        line = line.strip()\n                        if line.startswith('APP_KEY ='):\n                            app_key = line.split('=', 1)[1].strip().strip(\"'\\\"\")\n                        elif line.startswith('API_KEY_PEPPER ='):\n                            api_key_pepper = line.split('=', 1)[1].strip().strip(\"'\\\"\")\n\n            if not app_key or not api_key_pepper:\n                print(f\"❌ Encryption keys not found in .env file: APP_KEY={bool(app_key)}, API_KEY_PEPPER={bool(api_key_pepper)}\")\n                return None\n\n            print(f\"✅ Found encryption keys in .env file\")\n\n            # Initialize Fernet cipher using OpenAlgo's method\n            from cryptography.fernet import Fernet\n            from cryptography.hazmat.primitives import hashes\n            from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC\n            import base64\n\n            # Use OpenAlgo's encryption method\n            kdf = PBKDF2HMAC(\n                algorithm=hashes.SHA256(),\n                length=32,\n                salt=b'openalgo_static_salt',  # OpenAlgo's static salt\n                iterations=100000,\n            )\n            key = base64.urlsafe_b64encode(kdf.derive(api_key_pepper.encode()))\n            cipher = Fernet(key)\n\n            # Decrypt OpenAlgo API key (this is what we need for trading)\n            decrypted_openalgo_api_key = None\n\n            if api_key_row:\n                try:\n                    if 'api_key_encrypted' in api_key_row.keys() and api_key_row['api_key_encrypted']:\n                        decrypted_openalgo_api_key = cipher.decrypt(api_key_row['api_key_encrypted'].encode()).decode()\n                        print(f\"✅ OpenAlgo API key decrypted successfully\")\n                except Exception as e:\n                    print(f\"⚠️ OpenAlgo API key decryption failed: {e}\")\n\n            # Check broker authentication status (auth_row indicates broker is connected)\n            broker_auth_status = \"connected\" if auth_row else \"failed\"\n\n            # Get port information from .env\n            websocket_port, zmq_port = self.get_port_info_from_env(instance_path)\n            http_port = 5000 + setup_data['id']  # Calculate HTTP port\n\n            # Save to our database\n            self.save_decrypted_tokens_to_database(\n                setup_data['id'],\n                broker_auth_status,  # Auth status (broker connected)\n                decrypted_openalgo_api_key,  # OpenAlgo API key for trading\n                app_key,  # APP_KEY for future use\n                api_key_pepper,  # API_KEY_PEPPER for future use\n                symbol_count,\n                websocket_port,  # WebSocket port\n                zmq_port,  # ZMQ port\n                http_port  # HTTP port\n            )\n\n            # Test OpenAlgo API if we have the key\n            api_test_result = None\n            if decrypted_openalgo_api_key:\n                api_test_result = self.test_openalgo_api(setup_data, decrypted_openalgo_api_key)\n\n            # Return status\n            return {\n                \"has_auth_token\": bool(auth_row),  # Broker authentication\n                \"has_openalgo_api_key\": bool(decrypted_openalgo_api_key),  # OpenAlgo API key\n                \"symbol_count\": symbol_count,\n                \"auth_token_status\": broker_auth_status,\n                \"symbol_token_status\": \"connected\" if symbol_count > 100000 else \"downloading\" if symbol_count > 0 else \"failed\",\n                \"api_test_result\": api_test_result\n            }\n\n        except Exception as e:\n            print(f\"❌ Token decryption failed: {e}\")\n            return None\n\n    def get_port_info_from_env(self, instance_path):\n        \"\"\"Get WebSocket and ZMQ port information from .env file\"\"\"\n\n        try:\n            env_file = instance_path / \".env\"\n            websocket_port = None\n            zmq_port = None\n\n            if env_file.exists():\n                with open(env_file, 'r') as f:\n                    for line in f:\n                        line = line.strip()\n                        if line.startswith('WEBSOCKET_PORT='):\n                            websocket_port = int(line.split('=', 1)[1].strip().strip(\"'\\\"\"))\n                        elif line.startswith('ZMQ_PORT='):\n                            zmq_port = int(line.split('=', 1)[1].strip().strip(\"'\\\"\"))\n\n            print(f\"📡 Port info: WebSocket={websocket_port}, ZMQ={zmq_port}\")\n            return websocket_port, zmq_port\n\n        except Exception as e:\n            print(f\"⚠️ Failed to get port info: {e}\")\n            return None, None\n\n    def save_decrypted_tokens_to_database(self, setup_id, auth_status, openalgo_api_key, app_key, api_key_pepper, symbol_count, websocket_port, zmq_port, http_port=None):\n        \"\"\"Save decrypted OpenAlgo API key, encryption keys, and port information to our database\"\"\"\n\n        try:\n            import sqlite3\n            from datetime import datetime\n\n            # Connect to our database\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n            conn = sqlite3.connect(db_path)\n            cursor = conn.cursor()\n\n            # Determine status indicators\n            symbol_status = \"connected\" if symbol_count > 100000 else \"downloading\" if symbol_count > 0 else \"failed\"\n\n            # Update broker setup with OpenAlgo API key, encryption keys, and port information\n            # Calculate HTTP port if not provided\n            if http_port is None:\n                http_port = 5000 + setup_id\n\n            cursor.execute(\"\"\"\n                UPDATE user_brokers\n                SET algo_api_key = ?,\n                    algo_app_key = ?,\n                    algo_api_key_pepper = ?,\n                    http_port = ?,\n                    websocket_port = ?,\n                    zmq_port = ?,\n                    auth_token_status = ?,\n                    symbol_token_status = ?,\n                    symbol_count = ?,\n                    last_connected_at = ?,\n                    status = 'connected',\n                    updated_at = ?\n                WHERE id = ?\n            \"\"\", (\n                openalgo_api_key,  # OpenAlgo API key for trading\n                app_key,           # APP_KEY for future decryption\n                api_key_pepper,    # API_KEY_PEPPER for future decryption\n                http_port,         # HTTP port for web interface\n                websocket_port,    # WebSocket port for real-time data\n                zmq_port,          # ZMQ port for messaging\n                auth_status,       # Broker authentication status\n                symbol_status,     # Symbol download status\n                symbol_count,\n                datetime.now().isoformat(),\n                datetime.now().isoformat(),\n                setup_id\n            ))\n\n            conn.commit()\n            conn.close()\n\n            print(f\"💾 OpenAlgo data saved: Auth={auth_status}, Symbols={symbol_status}, API_Key={'✅' if openalgo_api_key else '❌'}, WebSocket={websocket_port}, ZMQ={zmq_port}\")\n\n        except Exception as e:\n            print(f\"❌ Failed to save tokens to database: {e}\")\n\n    def save_port_info_to_database(self, setup_id, http_port, websocket_port, zmq_port):\n        \"\"\"Save port information to database during Step 1\"\"\"\n        try:\n            import sqlite3\n            from datetime import datetime\n\n            conn = sqlite3.connect(self.db_path)\n            cursor = conn.cursor()\n\n            cursor.execute(\"\"\"\n                UPDATE user_brokers\n                SET http_port = ?,\n                    websocket_port = ?,\n                    zmq_port = ?,\n                    updated_at = ?\n                WHERE id = ?\n            \"\"\", (\n                http_port,\n                websocket_port,\n                zmq_port,\n                datetime.now().isoformat(),\n                setup_id\n            ))\n\n            conn.commit()\n            conn.close()\n\n            print(f\"💾 Port info saved: HTTP={http_port}, WebSocket={websocket_port}, ZMQ={zmq_port}\")\n\n        except Exception as e:\n            print(f\"❌ Failed to save port info to database: {e}\")\n\n    def test_openalgo_api(self, setup_data, api_key):\n        \"\"\"Test OpenAlgo API calls to verify everything is working\"\"\"\n\n        try:\n            import requests\n\n            # Get the port for this instance\n            port = self.get_actual_running_port(setup_data['id'])\n            if not port:\n                print(\"❌ Cannot determine OpenAlgo port for API testing\")\n                return {\"status\": \"failed\", \"error\": \"Port not found\"}\n\n            base_url = f\"http://127.0.0.1:{port}/api/v1\"\n\n            print(f\"🧪 Testing OpenAlgo API at {base_url}\")\n\n            # Test 1: Funds API\n            try:\n                funds_response = requests.post(\n                    f\"{base_url}/funds\",\n                    json={\"apikey\": api_key},\n                    timeout=10\n                )\n                funds_working = funds_response.status_code == 200\n                print(f\"💰 Funds API: {'✅' if funds_working else '❌'} ({funds_response.status_code})\")\n            except Exception as e:\n                funds_working = False\n                print(f\"💰 Funds API: ❌ ({e})\")\n\n            # Test 2: Holdings API\n            try:\n                holdings_response = requests.post(\n                    f\"{base_url}/holdings\",\n                    json={\"apikey\": api_key},\n                    timeout=10\n                )\n                holdings_working = holdings_response.status_code == 200\n                print(f\"📊 Holdings API: {'✅' if holdings_working else '❌'} ({holdings_response.status_code})\")\n            except Exception as e:\n                holdings_working = False\n                print(f\"📊 Holdings API: ❌ ({e})\")\n\n            # Test 3: Positions API\n            try:\n                positions_response = requests.post(\n                    f\"{base_url}/positionbook\",\n                    json={\"apikey\": api_key},\n                    timeout=10\n                )\n                positions_working = positions_response.status_code == 200\n                print(f\"📈 Positions API: {'✅' if positions_working else '❌'} ({positions_response.status_code})\")\n            except Exception as e:\n                positions_working = False\n                print(f\"📈 Positions API: ❌ ({e})\")\n\n            # Overall status\n            all_working = funds_working and holdings_working and positions_working\n\n            result = {\n                \"status\": \"success\" if all_working else \"partial\",\n                \"base_url\": base_url,\n                \"api_key_working\": True,\n                \"tests\": {\n                    \"funds\": funds_working,\n                    \"holdings\": holdings_working,\n                    \"positions\": positions_working\n                },\n                \"ready_for_trading\": all_working\n            }\n\n            if all_working:\n                print(\"🎉 All OpenAlgo APIs working perfectly! Ready for trading!\")\n            else:\n                print(\"⚠️ Some OpenAlgo APIs not working properly\")\n\n            return result\n\n        except Exception as e:\n            print(f\"❌ OpenAlgo API testing failed: {e}\")\n            return {\"status\": \"failed\", \"error\": str(e)}\n    \n    def step_1_set_env(self, setup_id):\n        \"\"\"STEP 1: SET_ENV - Create Algo instance and configure .env file\"\"\"\n\n        # Get setup data from database including user info\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        try:\n            print(f\"🔧 STEP 1: SET_ENV for {setup_data['setup_name']}\")\n\n            # Step 1: Create instance folder\n            instance_path = self.create_instance_folder(\n                setup_data['id'],\n                setup_data['broker_name'],\n                setup_data['setup_name']\n            )\n\n            # Step 2: Clone Algo source code\n            if not self.clone_openalgo_source(instance_path):\n                raise Exception(\"Failed to clone Algo source code\")\n\n            # Step 3: Configure .env file with broker and user data\n            port = self.configure_env_file(instance_path, setup_data)\n\n            # Step 3.4: Save port information to database\n            websocket_port = 8000 + setup_data['id']\n            zmq_port = 6000 + setup_data['id']\n            self.save_port_info_to_database(setup_data['id'], port, websocket_port, zmq_port)\n\n            # Step 3.5: Fix SocketIO async_mode to avoid trio/gevent conflicts\n            self.fix_socketio_async_mode(instance_path)\n\n            # Step 4: Skip dependency installation - using global environment\n            print(f\"✅ Using global Python environment - no dependency installation needed\")\n\n            # Update database with instance info\n            instance_info = {\n                \"instance_path\": str(instance_path),\n                \"port\": port,\n                \"status\": \"env_configured\",\n                \"algo_url\": f\"http://127.0.0.1:{port}\",\n                \"step_completed\": \"set_env\",\n                \"created_at\": self.get_ist_timestamp()\n            }\n\n            self.update_setup_instance_info(setup_id, instance_info)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ STEP 1 (SET_ENV) completed for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"set_env\",\n                    \"instance_path\": str(instance_path),\n                    \"port\": port,\n                    \"algo_url\": f\"http://127.0.0.1:{port}\",\n                    \"setup_name\": setup_data['setup_name'],\n                    \"broker_name\": setup_data['broker_name'],\n                    \"broker_type\": setup_data.get('broker_type', 'standard'),\n                    \"next_step\": \"step_2_start_algo\"\n                }\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"STEP 1 (SET_ENV) failed: {str(e)}\"\n            }\n\n    def fast_start_service(self, setup_id):\n        \"\"\"FAST START: Just start existing systemd service (no git pull, no pip install)\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use full service name: username-algo-broker-instance\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        service_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n\n        try:\n            print(f\"⚡ FAST START: Starting existing service {service_name}\")\n\n            # Just start the service - no git pull, no pip install\n            result = self.run_systemctl('start', service_name)\n\n            if result.returncode != 0:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to start service: {result.stderr}\"\n                }\n\n            # Quick 1-second check\n            import time\n            time.sleep(1)\n\n            port = 5000 + setup_id\n            print(f\"✅ Service started successfully\")\n            print(f\"🌐 Algo service available at: http://127.0.0.1:{port}\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Service {service_name} started successfully (fast start)\",\n                \"service_name\": service_name,\n                \"port\": port,\n                \"instance_path\": str(self.instances_dir / service_name),\n                \"fast_start\": True,\n                \"next_step\": \"step_3_register\"\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"FAST START failed: {str(e)}\"\n            }\n\n    def step_2_start_algo(self, setup_id):\n        \"\"\"STEP 2: START_ALGO - Enhanced start with git pull, requirements, and full service name\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use full service name: username-algo-broker-instance\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        if not instance_path.exists():\n            return {\"status\": \"error\", \"message\": \"Instance not found. Run STEP 1 (SET_ENV) first.\"}\n\n        try:\n            print(f\"🚀 STEP 2: START_ALGO - Enhanced start for {setup_data['setup_name']}\")\n            print(f\"📁 Instance: {instance_name}\")\n\n            # Step 1: Git pull for latest updates\n            print(f\"🔄 Updating Algo source code...\")\n            git_result = subprocess.run([\n                '/usr/bin/git', 'pull', 'origin', 'main'\n            ], cwd=instance_path, capture_output=True, text=True)\n\n            if git_result.returncode == 0:\n                print(f\"✅ Git pull successful: {git_result.stdout.strip()}\")\n            else:\n                print(f\"⚠️ Git pull warning: {git_result.stderr.strip()}\")\n\n            # Step 2: Install/update requirements\n            print(f\"📦 Installing/updating Python dependencies...\")\n            requirements_file = instance_path / \"requirements.txt\"\n\n            if requirements_file.exists():\n                pip_result = subprocess.run([\n                    '/home/<USER>/algofactory_production/env/bin/pip', 'install', '-r', str(requirements_file), '--upgrade'\n                ], capture_output=True, text=True)\n\n                if pip_result.returncode == 0:\n                    print(f\"✅ Requirements installed successfully\")\n                else:\n                    print(f\"⚠️ Requirements installation warning: {pip_result.stderr.strip()}\")\n            else:\n                print(f\"⚠️ requirements.txt not found, skipping dependency installation\")\n\n            # Step 3: Create and start systemd service with full name\n            service_result = self.create_systemd_service(setup_id, instance_path, setup_data)\n            if not service_result:\n                raise Exception(\"Failed to create systemd service\")\n\n            # Step 4: Start the service with full name\n            service_name = instance_name  # Full name: 1003-algo-angel-12\n            result = self.run_systemctl('start', service_name)\n\n            if result.returncode != 0:\n                raise Exception(f\"Failed to start service: {result.stderr}\")\n\n            # DO NOT enable auto-start - manual control only\n            # subprocess.run(['sudo', 'systemctl', 'enable', service_name], capture_output=True)\n            print(f\"⚠️ Auto-start disabled - service will NOT start automatically on boot\")\n\n            # Wait for service to start\n            time.sleep(5)\n\n            # Check if service is running (HTTP approach)\n            port = 5000 + setup_id\n            print(f\"✅ Algo started successfully\")\n            print(f\"🌐 Algo service available at: http://127.0.0.1:{port}\")\n\n            # Wait a moment for service to start\n            time.sleep(3)\n            if self.check_instance_running(port):\n                print(f\"✅ Algo service is ready and accessible\")\n            else:\n                print(f\"⚠️ Algo service starting (will be available shortly)\")\n\n            # Update status\n            instance_info = {\n                \"step_completed\": \"start_algo\",\n                \"status\": \"algo_running\",\n                \"service_name\": service_name,\n                \"started_at\": self.get_ist_timestamp()\n            }\n            self.update_setup_instance_info(setup_id, instance_info)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Algo started successfully for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"start_algo\",\n                    \"instance_path\": str(instance_path),\n                    \"http_url\": f\"http://127.0.0.1:{5000 + setup_id}\",\n                    \"port\": 5000 + setup_id,\n                    \"service_name\": service_name,\n                    \"connection_type\": \"http\",\n                    \"status\": \"running\",\n                    \"next_step\": \"step_3_register\"\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"STEP 2 (START_ALGO) failed: {str(e)}\"}\n\n    def step_3_register(self, setup_id):\n        \"\"\"STEP 3: REGISTER - Automatically register user in Algo\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        # Get the actual running port\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port. Run STEP 2 (START_ALGO) first.\"}\n\n        try:\n            print(f\"👤 STEP 3: REGISTER - Auto-registering user for {setup_data['setup_name']} on port {port}\")\n\n            # Check if Algo is running\n            if not self.check_instance_running(port):\n                raise Exception(f\"Algo instance not running on port {port}. Run STEP 2 (START_ALGO) first.\")\n\n            # Auto-register user (using HTTP)\n            registration_result = self.auto_register_user(port, setup_data)\n            if not registration_result:\n                raise Exception(\"Failed to register user automatically\")\n\n            # Update status\n            instance_info = {\n                \"step_completed\": \"register\",\n                \"status\": \"user_registered\",\n                \"registered_at\": self.get_ist_timestamp()\n            }\n            self.update_setup_instance_info(setup_id, instance_info)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ STEP 3 (REGISTER) completed! User registered automatically for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"register\",\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"username\": setup_data.get('user_username', setup_data.get('username', '1000')),\n                    \"email\": setup_data['email'],\n                    \"registration_status\": \"completed\",\n                    \"next_step\": \"step_4_connect_broker\"\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"STEP 3 (REGISTER) failed: {str(e)}\"}\n\n    def fast_register_user(self, setup_id):\n        \"\"\"FAST REGISTER: Check if user already registered, skip if so\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Get the actual running port\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port\"}\n\n        try:\n            print(f\"⚡ FAST REGISTER: Checking if user already registered for {setup_data['setup_name']} on port {port}\")\n\n            # Quick check if user is already registered (no delays)\n            algo_url = f\"http://127.0.0.1:{port}\"\n\n            # Try to access setup page (if user exists, it redirects to login)\n            import requests\n            session = requests.Session()\n\n            try:\n                response = session.get(f\"{algo_url}/setup\", timeout=2)\n                if response.status_code == 302 or \"login\" in response.url.lower():\n                    # User already registered - redirect to login\n                    print(f\"✅ User already registered, skipping registration\")\n                    return {\n                        \"status\": \"success\",\n                        \"message\": f\"✅ User already registered for '{setup_data['setup_name']}'\",\n                        \"data\": {\n                            \"step\": \"register\",\n                            \"url\": f\"http://127.0.0.1:{port}\",\n                            \"username\": setup_data.get('user_username', setup_data.get('username', '1000')),\n                            \"registration_status\": \"already_registered\",\n                            \"fast_register\": True,\n                            \"next_step\": \"step_4_connect_broker\"\n                        }\n                    }\n                else:\n                    # User not registered - do full registration\n                    print(f\"⚠️ User not registered, running full registration\")\n                    return self.auto_register_user(port, setup_data)\n\n            except requests.exceptions.RequestException:\n                # Service might still be starting - do full registration\n                print(f\"⚠️ Service not ready, running full registration\")\n                return self.auto_register_user(port, setup_data)\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"FAST REGISTER failed: {str(e)}\"}\n\n    def ultra_fast_connect_broker(self, setup_id):\n        \"\"\"ULTRA FAST CONNECT: Optimized for speed with minimal delays\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Get the actual running port\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port\"}\n\n        try:\n            print(f\"⚡ ULTRA FAST CONNECT: {setup_data['setup_name']} on port {port}\")\n\n            # BROKER-SPECIFIC CONNECTION: Handle different broker types\n            broker_name = setup_data.get('broker_name', '').lower()\n\n            if broker_name == 'dhan':\n                print(f\"🏦 Detected Dhan broker - using direct connection (no OTP needed)\")\n                return self.connect_dhan_direct(setup_id, port, setup_data)\n\n            elif broker_name == 'flattrade':\n                print(f\"🏦 Detected Flattrade broker - using OAuth flow\")\n                return self.connect_flattrade_oauth(setup_id, port, setup_data)\n\n            # FAST CHECK: Skip if already connected AND actually working (for other brokers)\n            our_db_status = self.get_setup_connection_status(setup_data.get('id'))\n            print(f\"🔍 Database status for broker {setup_data.get('id')}: {our_db_status}\")\n\n            if our_db_status == 'connected':\n                try:\n                    # Quick check if still actually connected (not just database status)\n                    import requests\n                    session = requests.Session()\n\n                    # Try to access a protected page that requires authentication\n                    response = session.get(f\"http://127.0.0.1:{port}/dashboard\", timeout=2)\n\n                    if response.status_code == 200 and \"dashboard\" in response.text.lower():\n                        print(f\"⚡ Already connected and authenticated - skipping authentication!\")\n                        return {\n                            \"status\": \"success\",\n                            \"message\": f\"✅ Broker already connected for '{setup_data['setup_name']}'\",\n                            \"data\": {\n                                \"step\": \"connect_broker\",\n                                \"url\": f\"http://127.0.0.1:{port}\",\n                                \"connection_status\": \"connected\",\n                                \"fast_connect\": True,\n                                \"skipped_auth\": True\n                            }\n                        }\n                    else:\n                        print(f\"⚠️ Database shows connected but broker not authenticated - running full auth\")\n                except Exception as e:\n                    print(f\"⚠️ Fast check failed: {e} - running full auth\")\n                    pass  # Continue with full auth if quick check fails\n\n            # FAST AUTH: Reduced timeouts and no delays\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                return {\"status\": \"error\", \"message\": \"Failed to login to Algo\"}\n\n            # FAST BROKER AUTH: Optimized authentication\n            auth_result = self.ultra_fast_broker_auth(port, setup_data, session)\n            if not auth_result:\n                return {\"status\": \"error\", \"message\": \"Failed to authenticate broker\"}\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Broker connected ultra-fast for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"connect_broker\",\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"connection_status\": \"connected\",\n                    \"ultra_fast_connect\": True,\n                    \"auth_tokens\": auth_result if isinstance(auth_result, dict) else {\"status\": \"authenticated\"}\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"ULTRA FAST CONNECT failed: {str(e)}\"}\n\n    def connect_dhan_direct(self, setup_id, port, setup_data):\n        \"\"\"Dhan broker connection - same flow as other brokers but no OTP for broker auth\"\"\"\n        try:\n            print(f\"🏦 DHAN CONNECTION: Standard flow (register → login → broker auth without OTP)\")\n\n            # Check if Dhan instance is running\n            if not self.check_instance_running(port):\n                return {\"status\": \"error\", \"message\": f\"Dhan instance not running on port {port}\"}\n\n            # STEP 1: Register user (same as other brokers)\n            print(f\"👤 STEP 1: Register user...\")\n            register_result = self.step_3_register(setup_id)\n            if register_result.get('status') != 'success':\n                return {\"status\": \"error\", \"message\": f\"Registration failed: {register_result.get('message')}\"}\n\n            # STEP 2: Login to OpenAlgo (same as other brokers)\n            print(f\"🔐 STEP 2: Login to OpenAlgo...\")\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                return {\"status\": \"error\", \"message\": \"Failed to login to OpenAlgo\"}\n\n            # STEP 3: Dhan broker auth (use broker login flow)\n            print(f\"🏦 STEP 3: Dhan broker auth (using brlogin flow)...\")\n\n            # For Dhan, we need to trigger the broker login process\n            # This will use the BROKER_API_KEY and BROKER_API_SECRET from .env\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n\n            try:\n                # For Dhan, we just need to trigger the broker callback (no form data needed)\n                dhan_callback_url = f\"{openalgo_url}/dhan/callback\"\n                print(f\"🔗 Triggering Dhan authentication via: {dhan_callback_url}\")\n\n                # Dhan doesn't need any form data - just call the callback\n                auth_response = session.get(dhan_callback_url, timeout=10, allow_redirects=True)\n\n                print(f\"📄 Dhan auth response status: {auth_response.status_code}\")\n                print(f\"📄 Dhan auth response URL: {auth_response.url}\")\n\n                # Check if we're redirected to broker.html (success) or still on auth page (failure)\n                if auth_response.status_code == 200:\n                    response_text = auth_response.text.lower()\n\n                    # Success indicators\n                    if (\"broker.html\" in str(auth_response.url) or\n                        \"successfully\" in response_text or\n                        \"connected\" in response_text or\n                        \"dashboard\" in response_text):\n\n                        print(f\"✅ Dhan broker authentication successful!\")\n\n                        # CRITICAL: Generate OpenAlgo API key if it doesn't exist (Dhan specific)\n                        print(f\"🔑 Generating OpenAlgo API key for Dhan broker...\")\n                        api_key_generated = self.generate_openalgo_api_key(port, setup_data)\n\n                        if api_key_generated:\n                            print(f\"✅ Dhan OpenAlgo API key generated successfully\")\n                        else:\n                            print(f\"⚠️ Failed to generate Dhan OpenAlgo API key, trying to retrieve existing...\")\n\n                        # CRITICAL: Retrieve and save OpenAlgo API key (same as Angel One process)\n                        print(f\"🔑 Retrieving and saving OpenAlgo API key for Dhan broker...\")\n                        tokens = self.retrieve_and_save_tokens(port, setup_data)\n\n                        if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):\n                            print(f\"✅ Dhan OpenAlgo API key retrieved and saved successfully\")\n                        else:\n                            print(f\"⚠️ Dhan OpenAlgo API key not found, but broker is connected\")\n\n                        # Update our database status\n                        self.update_setup_status(setup_id, 'running', 'connected')\n\n                        return {\n                            \"status\": \"success\",\n                            \"message\": f\"✅ Dhan broker connected successfully for '{setup_data['setup_name']}'\",\n                            \"data\": {\n                                \"step\": \"connect_broker\",\n                                \"url\": f\"http://127.0.0.1:{port}\",\n                                \"connection_status\": \"connected\",\n                                \"broker_type\": \"dhan\",\n                                \"auth_method\": \"simple_callback\",\n                                \"final_url\": str(auth_response.url),\n                                \"tokens_retrieved\": tokens is not None\n                            }\n                        }\n                    else:\n                        print(f\"⚠️ Dhan authentication failed\")\n                        print(f\"Response content preview: {response_text[:300]}...\")\n                        return {\"status\": \"error\", \"message\": \"Dhan authentication failed - check BROKER_API_KEY and BROKER_API_SECRET in .env file\"}\n                else:\n                    print(f\"⚠️ Dhan callback returned HTTP {auth_response.status_code}\")\n                    return {\"status\": \"error\", \"message\": f\"Dhan callback failed with HTTP {auth_response.status_code}\"}\n\n            except Exception as e:\n                print(f\"❌ Dhan broker auth failed: {e}\")\n                return {\"status\": \"error\", \"message\": f\"Dhan broker authentication failed: {str(e)}\"}\n\n        except Exception as e:\n            print(f\"❌ Dhan connection failed: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Dhan connection failed: {str(e)}\"}\n\n    def connect_flattrade_oauth(self, setup_id, port, setup_data):\n        \"\"\"Flattrade broker connection - OAuth flow with manual redirect\"\"\"\n        try:\n            print(f\"🏦 FLATTRADE CONNECTION: OAuth flow (register → login → OAuth redirect)\")\n\n            # Check if Flattrade instance is running\n            if not self.check_instance_running(port):\n                return {\"status\": \"error\", \"message\": f\"Flattrade instance not running on port {port}\"}\n\n            # STEP 1: Register user (same as other brokers)\n            print(f\"👤 STEP 1: Register user...\")\n            register_result = self.step_3_register(setup_id)\n            if register_result.get('status') != 'success':\n                return {\"status\": \"error\", \"message\": f\"Registration failed: {register_result.get('message')}\"}\n\n            # STEP 2: Login to OpenAlgo\n            print(f\"🔐 STEP 2: Login to OpenAlgo...\")\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                return {\"status\": \"error\", \"message\": \"Failed to login to OpenAlgo\"}\n\n            # STEP 3: Smart OAuth flow - try automation, fallback to guided manual\n            print(f\"🔗 STEP 3: Smart Flattrade OAuth flow...\")\n\n            try:\n                # Get the API key for OAuth URL\n                full_api_key = setup_data.get('broker_api_key', '')\n                api_key = full_api_key.split(':::')[1] if ':::' in full_api_key else full_api_key\n\n                print(f\"🔧 OAuth URL generation:\")\n                print(f\"   Full API Key: {full_api_key}\")\n                print(f\"   Extracted API Key: {api_key}\")\n\n                if not api_key or api_key == 'undefined':\n                    raise Exception(f\"Invalid API key: {api_key}\")\n\n                oauth_url = f\"https://auth.flattrade.in/?app_key={api_key}\"\n                print(f\"🌐 OAuth URL: {oauth_url}\")\n\n                # Try lightweight automation first\n                print(f\"🤖 Attempting lightweight OAuth automation...\")\n\n                # Check if we can detect an existing authorization code in the session\n                # This happens when user completes OAuth in another tab\n                auth_check_url = f\"{openalgo_url}/api/v1/auth\"\n                auth_check = session.get(auth_check_url, timeout=5)\n\n                if auth_check.status_code == 200:\n                    try:\n                        auth_data = auth_check.json()\n                        if auth_data.get('status') == 'success':\n                            print(f\"✅ Already authenticated! Skipping OAuth.\")\n                            self.update_setup_status(setup_id, 'running', 'connected')\n\n                            return {\n                                \"status\": \"success\",\n                                \"message\": f\"✅ Flattrade already connected for '{setup_data['setup_name']}'\",\n                                \"data\": {\n                                    \"step\": \"oauth_completed\",\n                                    \"url\": f\"http://127.0.0.1:{port}\",\n                                    \"connection_status\": \"connected\",\n                                    \"auth_method\": \"existing_session\"\n                                }\n                            }\n                    except:\n                        pass  # Not authenticated yet\n\n                # Try simple HTTP form submission (works for some OAuth implementations)\n                print(f\"🔐 Trying HTTP form submission...\")\n                oauth_form_data = {\n                    'userid': setup_data.get('broker_client_id'),\n                    'password': setup_data.get('trading_pin'),\n                    'dob': setup_data.get('totp_secret'),\n                    'app_key': api_key\n                }\n\n                oauth_submit_response = session.post(\n                    oauth_url,\n                    data=oauth_form_data,\n                    allow_redirects=True,\n                    timeout=10\n                )\n\n                # Check if we got an authorization code\n                final_url = str(oauth_submit_response.url)\n                if \"code=\" in final_url and \"127.0.0.1\" in final_url:\n                    auth_code = final_url.split(\"code=\")[1].split(\"&\")[0]\n                    print(f\"✅ HTTP automation successful! Got code: {auth_code[:10]}...\")\n\n                    # Complete OAuth callback\n                    callback_url = f\"{openalgo_url}/flattrade/callback?code={auth_code}\"\n                    callback_response = session.get(callback_url, timeout=10)\n\n                    if callback_response.status_code == 200:\n                        print(f\"✅ Flattrade OAuth completed via HTTP automation!\")\n                        self.update_setup_status(setup_id, 'running', 'connected')\n\n                        return {\n                            \"status\": \"success\",\n                            \"message\": f\"✅ Flattrade auto-connected for '{setup_data['setup_name']}'\",\n                            \"data\": {\n                                \"step\": \"oauth_completed\",\n                                \"url\": f\"http://127.0.0.1:{port}\",\n                                \"connection_status\": \"connected\",\n                                \"auth_method\": \"http_automation\"\n                            }\n                        }\n\n                # If HTTP automation didn't work, fall back to guided manual\n                print(f\"⚠️ HTTP automation didn't work, falling back to guided manual OAuth...\")\n                raise Exception(\"HTTP automation failed - using guided manual OAuth\")\n\n            except Exception as auto_error:\n                print(f\"⚠️ Auto OAuth failed: {auto_error}\")\n                print(f\"📋 Falling back to manual OAuth instructions...\")\n\n                # Fallback to manual instructions\n                full_api_key = setup_data.get('broker_api_key', '')\n                api_key = full_api_key.split(':::')[1] if ':::' in full_api_key else full_api_key\n\n                print(f\"🔧 Fallback OAuth URL generation:\")\n                print(f\"   Full API Key: {full_api_key}\")\n                print(f\"   Extracted API Key: {api_key}\")\n\n                if not api_key or api_key == 'undefined':\n                    api_key = 'INVALID_API_KEY'\n\n                oauth_url = f\"https://auth.flattrade.in/?app_key={api_key}\"\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"✅ Flattrade OAuth flow initiated for '{setup_data['setup_name']}' (manual)\",\n                    \"data\": {\n                        \"step\": \"oauth_manual\",\n                        \"url\": f\"http://127.0.0.1:{port}\",\n                        \"oauth_url\": oauth_url,\n                        \"manual_steps\": True,\n                        \"instructions\": {\n                            \"client_code\": setup_data.get('broker_client_id'),\n                            \"password\": setup_data.get('trading_pin'),\n                            \"dob\": setup_data.get('totp_secret')\n                        }\n                    }\n                }\n\n        except Exception as e:\n            print(f\"❌ Flattrade connection failed: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Flattrade connection failed: {str(e)}\"}\n\n    def ultra_fast_login(self, port, setup_data):\n        \"\"\"ULTRA FAST LOGIN: Minimal timeouts, no delays\"\"\"\n        try:\n            print(f\"⚡ Ultra fast login to OpenAlgo...\")\n\n            import requests\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n            session = requests.Session()\n\n            # Fast login (2 second timeout)\n            login_page = session.get(f\"{openalgo_url}/auth/login\", timeout=2)\n            if login_page.status_code != 200:\n                return None\n\n            # Submit login immediately\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            user_password = f\"algofactory{username}\"\n            login_data = {\n                \"username\": username,\n                \"password\": user_password\n            }\n\n            login_response = session.post(\n                f\"{openalgo_url}/auth/login\",\n                data=login_data,\n                allow_redirects=True,\n                timeout=2  # Fast timeout\n            )\n\n            if login_response.status_code == 200:\n                print(f\"⚡ Ultra fast login successful\")\n                return session\n            else:\n                return None\n\n        except Exception as e:\n            print(f\"❌ Ultra fast login failed: {e}\")\n            return None\n\n    def ultra_fast_broker_auth(self, port, setup_data, session):\n        \"\"\"ULTRA FAST BROKER AUTH: No delays, minimal timeouts\"\"\"\n        try:\n            print(f\"⚡ Ultra fast broker authentication...\")\n\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n\n            # Direct to Angel callback (1 second timeout)\n            angel_auth_url = f\"{openalgo_url}/angel/callback\"\n            auth_form_page = session.get(angel_auth_url, timeout=1)\n\n            if auth_form_page.status_code != 200:\n                return False\n\n            # Generate TOTP and submit immediately\n            totp_code = self.generate_totp(setup_data['totp_secret'])\n            if not totp_code:\n                return False\n\n            auth_data = {\n                \"clientid\": setup_data['broker_client_id'],\n                \"pin\": setup_data['trading_pin'],\n                \"totp\": totp_code\n            }\n\n            # Fast auth submission (3 second timeout)\n            auth_response = session.post(\n                angel_auth_url,\n                data=auth_data,\n                allow_redirects=True,\n                timeout=3  # Reduced from 15 seconds\n            )\n\n            if auth_response.status_code == 200:\n                if \"success\" in auth_response.text.lower() or \"authenticated\" in auth_response.text.lower():\n                    print(\"⚡ Ultra fast broker auth successful\")\n\n                    # NO SLEEP - immediate token retrieval\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n                    if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):\n                        return True\n                    else:\n                        # Even if no tokens, consider it successful if auth response was good\n                        return True\n\n            return False\n\n        except Exception as e:\n            print(f\"❌ Ultra fast broker auth failed: {e}\")\n            return False\n\n    def generate_openalgo_api_key(self, port, setup_data):\n        \"\"\"Generate OpenAlgo API key for Dhan broker\"\"\"\n        try:\n            print(f\"🔑 Generating OpenAlgo API key for user {setup_data['username']}...\")\n\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n\n            # Create a session for API key generation\n            session = requests.Session()\n\n            # Login to OpenAlgo first\n            login_data = {\n                'username': setup_data['username'],\n                'password': setup_data['username']  # Default password is same as username\n            }\n\n            login_response = session.post(f\"{openalgo_url}/auth/login\", data=login_data, timeout=10)\n\n            if login_response.status_code != 200:\n                print(f\"❌ Failed to login to OpenAlgo for API key generation: {login_response.status_code}\")\n                return False\n\n            # Generate API key via POST request\n            api_key_data = {\n                'user_id': setup_data['username']\n            }\n\n            api_key_response = session.post(\n                f\"{openalgo_url}/apikey\",\n                json=api_key_data,\n                timeout=10\n            )\n\n            if api_key_response.status_code == 200:\n                response_data = api_key_response.json()\n                if response_data.get('api_key'):\n                    print(f\"✅ OpenAlgo API key generated successfully: {response_data['api_key'][:8]}...\")\n                    return True\n                else:\n                    print(f\"⚠️ API key generation response missing key: {response_data}\")\n                    return False\n            else:\n                print(f\"❌ Failed to generate OpenAlgo API key: {api_key_response.status_code}\")\n                return False\n\n        except Exception as e:\n            print(f\"❌ Error generating OpenAlgo API key: {e}\")\n            return False\n\n    def connect_with_manual_otp(self, setup_id, manual_otp):\n        \"\"\"Connect broker using manual OTP with saved credentials\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Get the actual running port\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port\"}\n\n        try:\n            print(f\"📱 FAST MANUAL OTP CONNECT: Using OTP {manual_otp} for {setup_data['setup_name']} on port {port}\")\n\n            # Ultra fast login to OpenAlgo (optimized)\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                return {\"status\": \"error\", \"message\": \"Failed to login to Algo\"}\n\n            # Use manual OTP with saved credentials (optimized)\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n\n            # Get Angel auth page and extract CSRF token (faster timeout)\n            angel_auth_url = f\"{openalgo_url}/angel/callback\"\n            auth_form_page = session.get(angel_auth_url, timeout=2)  # Reduced timeout\n\n            if auth_form_page.status_code != 200:\n                return {\"status\": \"error\", \"message\": \"Failed to access Angel auth page\"}\n\n            # Extract CSRF token from the form\n            csrf_token = None\n            if 'csrf_token' in auth_form_page.text:\n                import re\n                csrf_match = re.search(r'name=\"csrf_token\" value=\"([^\"]+)\"', auth_form_page.text)\n                if csrf_match:\n                    csrf_token = csrf_match.group(1)\n\n            # Prepare authentication data exactly as Angel One expects\n            auth_data = {\n                \"clientid\": setup_data['broker_client_id'],  # Saved credential\n                \"pin\": setup_data['trading_pin'],            # Saved credential\n                \"totp\": manual_otp                           # Manual OTP input\n            }\n\n            # Add CSRF token if found\n            if csrf_token:\n                auth_data[\"csrf_token\"] = csrf_token\n\n            print(f\"🔐 FAST Angel One auth with saved credentials + manual OTP: {manual_otp}\")\n\n            # Submit authentication to Angel callback endpoint (optimized timeout)\n            auth_response = session.post(\n                angel_auth_url,\n                data=auth_data,\n                allow_redirects=True,\n                timeout=5  # Faster timeout for broker auth\n            )\n\n            # Check Angel One authentication response\n            if auth_response.status_code == 200:\n                # Angel One redirects to dashboard on successful auth\n                if \"dashboard\" in auth_response.url.lower() or \"dashboard\" in auth_response.text.lower():\n                    print(\"✅ FAST Manual OTP authentication successful - redirected to dashboard\")\n\n                    # Ultra fast token retrieval (optimized, no delays)\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n\n                    return {\n                        \"status\": \"success\",\n                        \"message\": f\"✅ Angel One connected with manual OTP for '{setup_data['setup_name']}'\",\n                        \"data\": {\n                            \"step\": \"connect_broker\",\n                            \"url\": f\"http://127.0.0.1:{port}\",\n                            \"connection_status\": \"connected\",\n                            \"manual_otp_used\": True,\n                            \"auth_tokens\": tokens if tokens else {\"status\": \"authenticated\"}\n                        }\n                    }\n                elif \"error\" in auth_response.text.lower() or \"invalid\" in auth_response.text.lower():\n                    return {\"status\": \"error\", \"message\": \"Authentication failed - invalid OTP, PIN, or Client ID\"}\n                else:\n                    # Check if we're still on the auth page (auth failed)\n                    if \"angel/callback\" in auth_response.url or \"totp\" in auth_response.text.lower():\n                        return {\"status\": \"error\", \"message\": \"Authentication failed - please check OTP, PIN, and Client ID\"}\n                    else:\n                        # Assume success if not on auth page\n                        print(\"✅ Manual OTP authentication appears successful\")\n                        tokens = self.retrieve_and_save_tokens(port, setup_data)\n                        return {\n                            \"status\": \"success\",\n                            \"message\": f\"✅ Angel One connected with manual OTP for '{setup_data['setup_name']}'\",\n                            \"data\": {\n                                \"step\": \"connect_broker\",\n                                \"url\": f\"http://127.0.0.1:{port}\",\n                                \"connection_status\": \"connected\",\n                                \"manual_otp_used\": True,\n                                \"auth_tokens\": tokens if tokens else {\"status\": \"authenticated\"}\n                            }\n                        }\n            else:\n                return {\"status\": \"error\", \"message\": f\"Authentication request failed: HTTP {auth_response.status_code}\"}\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Manual OTP connection failed: {str(e)}\"}\n\n    def run_complete_manual_setup(self, setup_id, manual_otp):\n        \"\"\"\n        Run complete automation flow for new instances with manual OTP\n        Handles: register -> login -> symbols -> broker auth -> api key\n        \"\"\"\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port\"}\n\n        try:\n            print(f\"🔄 COMPLETE MANUAL SETUP: Running full automation for {setup_data['setup_name']} on port {port}\")\n\n            # Check current status\n            detailed_status = setup_data.get('detailed_status', {})\n            if isinstance(detailed_status, str):\n                import json\n                detailed_status = json.loads(detailed_status)\n\n            print(f\"📋 Current status: {detailed_status}\")\n\n            # Check if this is a new instance that needs complete setup\n            pending_steps = [k for k, v in detailed_status.items() if v == 'pending']\n            if len(pending_steps) <= 1:  # Only broker auth pending\n                print(\"📱 Instance already setup, using direct manual OTP\")\n                return self.connect_with_manual_otp(setup_id, manual_otp)\n\n            print(f\"🔄 New instance detected, running complete setup. Pending steps: {pending_steps}\")\n\n            # Step 1: Check if OpenAlgo is running\n            if not self.check_instance_running(port):\n                return {\"status\": \"error\", \"message\": \"OpenAlgo instance is not running\"}\n\n            # Step 2: Register user if needed (FAST)\n            if detailed_status.get('step_3_register') == 'pending':\n                print(\"📝 Step 3: FAST registering user in OpenAlgo...\")\n                register_result = self.step_3_register(setup_id)\n                if register_result.get('status') != 'success':\n                    return {\"status\": \"error\", \"message\": \"Failed to register user in OpenAlgo\"}\n\n                detailed_status['step_3_register'] = 'completed'\n                self.update_detailed_status(setup_id, 'step_3_register', 'completed')\n\n            # Step 3: Download symbols if needed (FAST - mark as completed, handled automatically)\n            if detailed_status.get('step_5_symbols') == 'pending':\n                print(\"📊 Step 5: FAST symbols download (automatic)...\")\n                detailed_status['step_5_symbols'] = 'completed'\n                self.update_detailed_status(setup_id, 'step_5_symbols', 'completed')\n\n            # Step 4: FAST Broker authentication with manual OTP\n            if detailed_status.get('step_4_connect') == 'pending':\n                print(f\"🔐 Step 4: FAST broker authentication with manual OTP: {manual_otp}\")\n\n                # Use the existing manual OTP function for broker auth (optimized)\n                auth_result = self.connect_with_manual_otp(setup_id, manual_otp)\n                if auth_result.get('status') != 'success':\n                    return auth_result  # Return the error from broker auth\n\n                detailed_status['step_4_connect'] = 'completed'\n                self.update_detailed_status(setup_id, 'step_4_connect', 'completed')\n\n            # Step 5: FAST Broker authentication (step_6_auth) - mark as completed\n            if detailed_status.get('step_6_auth') == 'pending':\n                print(\"🔐 Step 6: FAST broker auth (already completed in step 4)\")\n                detailed_status['step_6_auth'] = 'completed'\n                self.update_detailed_status(setup_id, 'step_6_auth', 'completed')\n\n            # Step 6: FAST API key generation (automatic)\n            if detailed_status.get('step_7_api_key') == 'pending':\n                print(\"🔑 Step 7: FAST API key generation (automatic)...\")\n                detailed_status['step_7_api_key'] = 'completed'\n                self.update_detailed_status(setup_id, 'step_7_api_key', 'completed')\n\n            print(\"✅ COMPLETE MANUAL SETUP SUCCESS - All steps completed\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Complete setup completed for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"complete_setup\",\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"connection_status\": \"connected\",\n                    \"manual_otp_used\": True,\n                    \"setup_method\": \"complete_manual_flow\",\n                    \"completed_steps\": detailed_status\n                }\n            }\n\n        except Exception as e:\n            print(f\"❌ Complete manual setup error: {str(e)}\")\n            return {\"status\": \"error\", \"message\": f\"Complete setup failed: {str(e)}\"}\n\n    def step_4_connect_broker(self, setup_id, manual_credentials=None):\n        \"\"\"STEP 4: CONNECT_BROKER - Automatically connect and authenticate broker\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # If manual credentials provided, use them\n        if manual_credentials:\n            print(f\"🔐 Using manual credentials for connection...\")\n            setup_data.update(manual_credentials)\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        # Get the actual running port\n        port = self.get_actual_running_port(setup_id)\n        if not port:\n            raise Exception(\"Cannot detect running Algo instance port. Complete previous steps first.\")\n\n        try:\n            print(f\"🏦 STEP 4: CONNECT_BROKER - Auto-connecting broker for {setup_data['setup_name']} on port {port}\")\n\n            # Check if Algo is running\n            if not self.check_instance_running(port):\n                raise Exception(f\"Algo instance not running on port {port}. Complete previous steps first.\")\n\n            # BROKER-SPECIFIC CONNECTION: Handle different broker types\n            broker_name = setup_data.get('broker_name', '').lower()\n\n            if broker_name == 'dhan':\n                print(f\"🏦 Detected Dhan broker - using setup → login → broker auth flow (no OTP needed)\")\n                dhan_result = self.connect_dhan_direct(setup_id, port, setup_data)\n                if dhan_result.get('status') == 'success':\n                    # Update status for consistency with other brokers\n                    instance_info = {\n                        \"step_completed\": \"connect_broker\",\n                        \"status\": \"broker_connected\",\n                        \"connected_at\": self.get_ist_timestamp(),\n                        \"broker_type\": \"dhan\"\n                    }\n                    self.update_setup_instance_info(setup_id, instance_info)\n                    return dhan_result\n                else:\n                    raise Exception(f\"Dhan connection failed: {dhan_result.get('message')}\")\n\n            elif broker_name == 'flattrade':\n                print(f\"🏦 Detected Flattrade broker - using OAuth flow\")\n                flattrade_result = self.connect_flattrade_oauth(setup_id, port, setup_data)\n                if flattrade_result.get('status') == 'success':\n                    data = flattrade_result.get('data', {})\n\n                    if data.get('auth_method') == 'automated_oauth':\n                        # Fully automated OAuth completed\n                        instance_info = {\n                            \"step_completed\": \"connect_broker\",\n                            \"status\": \"broker_connected\",\n                            \"connected_at\": self.get_ist_timestamp(),\n                            \"broker_type\": \"flattrade\"\n                        }\n                        self.update_setup_instance_info(setup_id, instance_info)\n                        # Update database status to connected\n                        self.update_setup_status(setup_id, 'running', 'connected')\n                    else:\n                        # Manual OAuth required - do NOT mark as connected\n                        instance_info = {\n                            \"step_completed\": \"oauth_initiated\",\n                            \"status\": \"oauth_pending\",\n                            \"connected_at\": self.get_ist_timestamp(),\n                            \"broker_type\": \"flattrade\"\n                        }\n                        self.update_setup_instance_info(setup_id, instance_info)\n                        # Keep database status as not_connected until OAuth is completed\n                        self.update_setup_status(setup_id, 'running', 'not_connected')\n\n                    return flattrade_result\n                else:\n                    raise Exception(f\"Flattrade OAuth initiation failed: {flattrade_result.get('message')}\")\n\n            # Auto-login to Algo (for other brokers)\n            session = self.auto_login(port, setup_data)\n            if not session:\n                raise Exception(\"Failed to login to Algo automatically\")\n\n            # Auto-authenticate broker (for other brokers)\n            print(f\"🔑 Starting broker authentication...\")\n            print(f\"🔍 Client ID: {setup_data.get('broker_client_id')}\")\n            print(f\"🔍 TOTP secret length: {len(setup_data.get('totp_secret', ''))}\")\n\n            auth_result = self.auto_broker_auth(port, setup_data, session)\n            if not auth_result:\n                print(f\"❌ Broker authentication failed for setup {setup_data.get('id')}\")\n                print(f\"💡 Common issues:\")\n                print(f\"   - TOTP secret is incorrect\")\n                print(f\"   - Trading PIN is wrong\")\n                print(f\"   - Angel One account has 2FA issues\")\n                raise Exception(\"Failed to authenticate broker automatically. Check TOTP secret and trading PIN.\")\n\n            # Update status\n            instance_info = {\n                \"step_completed\": \"connect_broker\",\n                \"status\": \"broker_connected\",\n                \"connected_at\": self.get_ist_timestamp(),\n                \"auth_tokens\": auth_result if isinstance(auth_result, dict) else None\n            }\n            self.update_setup_instance_info(setup_id, instance_info)\n\n            broker_type = self.get_broker_type(setup_data['broker_name'])\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ STEP 4 (CONNECT_BROKER) completed! Broker connected automatically for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"step\": \"connect_broker\",\n                    \"broker_type\": broker_type,\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"broker_name\": setup_data['broker_name'],\n                    \"client_id\": setup_data['broker_client_id'],\n                    \"connection_status\": \"connected\",\n                    \"auth_tokens\": auth_result if isinstance(auth_result, dict) else {\"status\": \"authenticated\"},\n                    \"connected_at\": self.get_ist_timestamp(),\n                    \"all_steps_completed\": True\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"STEP 4 (CONNECT_BROKER) failed: {str(e)}\"}\n\n    # REMOVED: step_4_connect_broker_with_credentials() - Duplicate method #2\n\n    # REMOVED: step_4_connect_broker_manual_init() - Legacy duplicate method\n\n    # REMOVED: step_4_connect_broker_manual_verify() - Legacy duplicate method\n\n    def stop_openalgo_manual(self, setup_id):\n        \"\"\"Stop OpenAlgo instance manually\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        try:\n            # Kill any python processes running from this instance\n            result = subprocess.run([\n                'pkill', '-f', str(instance_path)\n            ], capture_output=True)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"OpenAlgo instance stopped (if it was running)\",\n                \"data\": {\n                    \"instance_path\": str(instance_path)\n                }\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Failed to stop instance: {str(e)}\"\n            }\n    \n    def get_setup_data(self, setup_id):\n        \"\"\"Get broker setup data from database\"\"\"\n        try:\n            conn = sqlite3.connect(self.db_path)\n            conn.row_factory = sqlite3.Row\n            cursor = conn.cursor()\n\n            cursor.execute(\"\"\"\n                SELECT ub.*, ub.user_id\n                FROM user_brokers ub\n                WHERE ub.id = ?\n            \"\"\", (setup_id,))\n\n            row = cursor.fetchone()\n            conn.close()\n\n            if row:\n                return dict(row)\n            return None\n\n        except Exception as e:\n            print(f\"Database error: {e}\")\n            return None\n\n    def get_setup_data_with_user(self, setup_id):\n        \"\"\"Get broker setup data with user information from database\"\"\"\n        try:\n            conn = sqlite3.connect(self.db_path)\n            conn.row_factory = sqlite3.Row\n            cursor = conn.cursor()\n\n            cursor.execute(\"\"\"\n                SELECT ub.*, u.username as user_username, u.email, u.full_name\n                FROM user_brokers ub\n                JOIN users u ON ub.username = u.username\n                WHERE ub.id = ?\n            \"\"\", (setup_id,))\n\n            row = cursor.fetchone()\n            conn.close()\n\n            if row:\n                return dict(row)\n            return None\n\n        except Exception as e:\n            print(f\"Database error: {e}\")\n            return None\n\n    def get_port_from_env(self, instance_path):\n        \"\"\"Get port number from .env file\"\"\"\n        env_file = instance_path / \".env\"\n        port = 5001  # default\n\n        if env_file.exists():\n            with open(env_file, 'r') as f:\n                for line in f:\n                    if 'FLASK_PORT' in line and '=' in line:\n                        port = int(line.split('=')[1].strip().strip(\"'\\\"\"))\n                        break\n        return port\n\n    # Gunicorn Management Methods\n    def start_gunicorn_service(self, setup_id):\n        \"\"\"Start OpenAlgo with Gunicorn as systemd service\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        if not instance_path.exists():\n            return {\"status\": \"error\", \"message\": \"Instance not found. Run STEP 1 (SET_ENV) first.\"}\n\n        try:\n            print(f\"🚀 Starting Gunicorn service for {setup_data['setup_name']}\")\n\n            # Create systemd service file\n            service_result = self.create_systemd_service(setup_id, instance_path, setup_data)\n            if not service_result:\n                return {\"status\": \"error\", \"message\": \"Failed to create systemd service\"}\n\n            # Start the service - use full instance name as service name\n            service_name = instance_name\n            result = self.run_systemctl('start', service_name)\n\n            if result.returncode != 0:\n                return {\"status\": \"error\", \"message\": f\"Failed to start service: {result.stderr}\"}\n\n            # DO NOT enable auto-start - manual control only\n            # subprocess.run(['sudo', 'systemctl', 'enable', service_name], capture_output=True)\n            print(f\"⚠️ Auto-start disabled - service will NOT start automatically on boot\")\n\n            # Wait and check status\n            time.sleep(3)\n            status_result = self.get_service_status(service_name)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Gunicorn service started for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"service_name\": service_name,\n                    \"instance_path\": str(instance_path),\n                    \"port\": self.get_port_from_env(instance_path),\n                    \"service_status\": status_result,\n                    \"url\": f\"http://127.0.0.1:{self.get_port_from_env(instance_path)}\",\n                    \"auto_restart\": True\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to start Gunicorn service: {str(e)}\"}\n\n    def stop_gunicorn_service(self, setup_id):\n        \"\"\"Stop OpenAlgo Gunicorn service\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        service_name = instance_name  # Use full instance name as service name\n\n        try:\n            print(f\"🛑 Stopping Gunicorn service for {setup_data['setup_name']}\")\n\n            result = self.run_systemctl('stop', service_name)\n\n            if result.returncode != 0:\n                return {\"status\": \"error\", \"message\": f\"Failed to stop service: {result.stderr}\"}\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Gunicorn service stopped for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"service_name\": service_name,\n                    \"status\": \"stopped\"\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to stop Gunicorn service: {str(e)}\"}\n\n    def restart_gunicorn_service(self, setup_id):\n        \"\"\"Restart OpenAlgo Gunicorn service\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        service_name = instance_name  # Use full instance name as service name\n\n        try:\n            print(f\"🔄 Restarting Gunicorn service for {setup_data['setup_name']}\")\n\n            result = subprocess.run([\n                '/usr/bin/sudo', '/usr/bin/systemctl', 'restart', service_name\n            ], capture_output=True, text=True)\n\n            if result.returncode != 0:\n                return {\"status\": \"error\", \"message\": f\"Failed to restart service: {result.stderr}\"}\n\n            # Wait and check status\n            time.sleep(3)\n            status_result = self.get_service_status(service_name)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Gunicorn service restarted for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"service_name\": service_name,\n                    \"service_status\": status_result,\n                    \"url\": f\"http://127.0.0.1:{self.get_port_from_env(self.instances_dir / instance_name)}\"\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to restart Gunicorn service: {str(e)}\"}\n\n    def health_check(self, setup_id):\n        \"\"\"Health check for Algo instance\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n\n        # Get port from .env file\n        port = self.get_port_from_env(instance_path) if instance_path.exists() else 5000\n\n        try:\n            # Check if Algo is responding\n            http_healthy = False\n            http_status = \"unreachable\"\n            try:\n                response = requests.get(f\"http://127.0.0.1:{port}\", timeout=5)\n                http_healthy = response.status_code == 200\n                http_status = response.status_code\n            except Exception as e:\n                http_status = f\"error: {str(e)}\"\n\n            # Check systemd service status\n            service_name = instance_name  # Use full instance name as service name\n            service_status = self.get_service_status(service_name)\n\n            # Check database connectivity\n            db_status = self.check_database_health(instance_path)\n\n            # Overall health\n            overall_healthy = http_healthy and service_status.get(\"active\", False) and db_status.get(\"healthy\", False)\n\n            return {\n                \"status\": \"success\",\n                \"data\": {\n                    \"setup_name\": setup_data['setup_name'],\n                    \"broker_name\": setup_data['broker_name'],\n                    \"instance_healthy\": overall_healthy,\n                    \"http_healthy\": http_healthy,\n                    \"http_status\": http_status,\n                    \"service_status\": service_status,\n                    \"database_status\": db_status,\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"port\": port,\n                    \"instance_path\": str(instance_path),\n                    \"service_name\": service_name,\n                    \"last_checked\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Health check failed: {str(e)}\",\n                \"data\": {\n                    \"setup_name\": setup_data['setup_name'],\n                    \"instance_healthy\": False,\n                    \"error\": str(e),\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"port\": port,\n                    \"last_checked\": self.get_ist_timestamp()\n                }\n            }\n\n    def get_instance_status(self, setup_id):\n        \"\"\"Get detailed status of OpenAlgo instance\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        instance_path = self.instances_dir / instance_name\n        port = self.get_port_from_env(instance_path)\n\n        # Get health check\n        health_result = self.health_check(setup_id)\n        health_data = health_result.get(\"data\", {})\n\n        # Get additional info\n        service_name = instance_name  # Use full instance name as service name\n\n        return {\n            \"status\": \"success\",\n            \"data\": {\n                \"setup_info\": {\n                    \"setup_id\": setup_id,\n                    \"setup_name\": setup_data['setup_name'],\n                    \"broker_name\": setup_data['broker_name'],\n                    \"broker_type\": self.get_broker_type(setup_data['broker_name']),\n                    \"client_id\": setup_data['broker_client_id']\n                },\n                \"instance_info\": {\n                    \"instance_path\": str(instance_path),\n                    \"port\": port,\n                    \"url\": f\"http://127.0.0.1:{port}\",\n                    \"service_name\": service_name\n                },\n                \"health_status\": health_data,\n                \"timestamps\": {\n                    \"created_at\": setup_data.get('created_at'),\n                    \"updated_at\": setup_data.get('updated_at'),\n                    \"last_connected\": setup_data.get('last_connected'),\n                    \"checked_at\": self.get_ist_timestamp()\n                }\n            }\n        }\n\n    def get_instance_logs(self, setup_id, lines=50):\n        \"\"\"Get OpenAlgo instance logs\"\"\"\n\n        setup_data = self.get_setup_data_with_user(setup_id)\n        if not setup_data:\n            return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n        # Use consistent naming: username-algo-broker-id\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        service_name = instance_name  # Use full instance name as service name\n\n        try:\n            # Get systemd service logs\n            result = subprocess.run([\n                'sudo', 'journalctl', '-u', service_name, '-n', str(lines), '--no-pager'\n            ], capture_output=True, text=True)\n\n            if result.returncode != 0:\n                return {\"status\": \"error\", \"message\": f\"Failed to get logs: {result.stderr}\"}\n\n            return {\n                \"status\": \"success\",\n                \"data\": {\n                    \"setup_name\": setup_data['setup_name'],\n                    \"service_name\": service_name,\n                    \"lines_requested\": lines,\n                    \"logs\": result.stdout,\n                    \"retrieved_at\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to get logs: {str(e)}\"}\n\n    # Helper Methods\n    def create_systemd_service(self, setup_id, instance_path, setup_data):\n        \"\"\"Create systemd service file for Algo instance with full service name\"\"\"\n\n        # Use full service name: username-algo-broker-instance\n        username = setup_data.get('user_username', setup_data.get('username', '1000'))\n        service_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n        port = 5000 + setup_id  # Calculate port for this instance\n\n        print(f\"✅ Using HTTP configuration on port {port}\")\n\n        # Create systemd service using HTTP binding\n        service_content = f\"\"\"[Unit]\nDescription=Algo Instance - {setup_data['setup_name']} (HTTP Port {port})\nAfter=network.target\n\n[Service]\nType=simple\nUser=ubuntu\nGroup=ubuntu\nWorkingDirectory={instance_path}\nExecStart=/bin/bash -c 'source {self.base_dir}/env/bin/activate && cd {instance_path} && gunicorn \\\\\n    --worker-class sync \\\\\n    -w 1 \\\\\n    --bind 127.0.0.1:{port} \\\\\n    --log-level info \\\\\n    --access-logfile - \\\\\n    --error-logfile - \\\\\n    app:app'\nRestart=always\nRestartSec=5\nTimeoutSec=60\nStandardOutput=journal\nStandardError=journal\n\n[Install]\nWantedBy=multi-user.target\n\"\"\"\n\n        try:\n            # Write service file\n            service_file = f\"/etc/systemd/system/{service_name}.service\"\n            with open(f\"/tmp/{service_name}.service\", 'w') as f:\n                f.write(service_content)\n\n            # Move to systemd directory\n            result = subprocess.run([\n                '/usr/bin/sudo', '/usr/bin/mv', f\"/tmp/{service_name}.service\", service_file\n            ], capture_output=True, text=True, env=self.subprocess_env)\n\n            if result.returncode != 0:\n                print(f\"Failed to create service file: {result.stderr}\")\n                return False\n\n            # Reload systemd\n            subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'daemon-reload'],\n                         capture_output=True, env=self.subprocess_env)\n\n            print(f\"✅ Created systemd service: {service_name}\")\n            return True\n\n        except Exception as e:\n            print(f\"Failed to create systemd service: {e}\")\n            return False\n\n    def check_socket_running(self, socket_file):\n        \"\"\"Check if Unix socket file exists and is accessible\"\"\"\n\n        try:\n            import socket\n            import os\n\n            if not os.path.exists(socket_file):\n                return False\n\n            # Try to connect to the socket\n            sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)\n            try:\n                sock.connect(socket_file)\n                sock.close()\n                return True\n            except:\n                return False\n\n        except Exception as e:\n            print(f\"⚠️ Socket check failed: {e}\")\n            return False\n\n    def get_service_status(self, service_name):\n        \"\"\"Get systemd service status\"\"\"\n\n        try:\n            result = subprocess.run([\n                '/usr/bin/sudo', '/usr/bin/systemctl', 'is-active', service_name\n            ], capture_output=True, text=True)\n\n            status = result.stdout.strip()\n\n            # Get more detailed status\n            status_result = subprocess.run([\n                '/usr/bin/sudo', '/usr/bin/systemctl', 'status', service_name, '--no-pager', '-l'\n            ], capture_output=True, text=True)\n\n            return {\n                \"active\": status == \"active\",\n                \"status\": status,\n                \"details\": status_result.stdout if status_result.returncode == 0 else \"Service not found\"\n            }\n\n        except Exception as e:\n            return {\n                \"active\": False,\n                \"status\": \"error\",\n                \"details\": str(e)\n            }\n\n    def check_database_health(self, instance_path):\n        \"\"\"Check Algo database health\"\"\"\n\n        try:\n            if not instance_path.exists():\n                return {\"healthy\": False, \"error\": \"Instance path not found\"}\n\n            # Check for database file\n            db_path = instance_path / \"db\" / \"openalgo.db\"\n\n            if not db_path.exists():\n                # Database might not be created yet\n                return {\"healthy\": False, \"error\": \"Database file not found (not created yet)\"}\n\n            # Try to connect to database\n            conn = sqlite3.connect(db_path, timeout=5)\n            cursor = conn.cursor()\n\n            # Simple query to check if database is accessible\n            cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table' LIMIT 1\")\n            result = cursor.fetchone()\n\n            conn.close()\n\n            return {\n                \"healthy\": True,\n                \"database_path\": str(db_path),\n                \"accessible\": True,\n                \"tables_found\": result is not None\n            }\n\n        except Exception as e:\n            return {\n                \"healthy\": False,\n                \"error\": str(e),\n                \"database_path\": str(instance_path / \"db\" / \"openalgo.db\") if instance_path.exists() else \"unknown\"\n            }\n\n    # ============================================================================\n    # SYSTEM SERVICE MANAGEMENT METHODS\n    # ============================================================================\n\n    def get_system_services(self):\n        \"\"\"Get all algo-related systemd services\"\"\"\n        try:\n            # Get all algo services\n            result = subprocess.run([\n                'sudo', 'systemctl', 'list-units', '--type=service', '--state=active,inactive,failed',\n                '--no-pager', '--plain', 'algo-*'\n            ], capture_output=True, text=True)\n\n            services = []\n            if result.returncode == 0:\n                lines = result.stdout.strip().split('\\n')\n                for line in lines:\n                    if 'algo-' in line and '.service' in line:\n                        parts = line.split()\n                        if len(parts) >= 4:\n                            service_name = parts[0].replace('.service', '')\n                            status = parts[2]  # active, inactive, failed\n                            description = ' '.join(parts[4:]) if len(parts) > 4 else ''\n\n                            # Extract setup info from service name\n                            setup_info = self.extract_setup_info_from_service(service_name)\n\n                            services.append({\n                                \"service_name\": service_name,\n                                \"status\": status,\n                                \"description\": description,\n                                \"setup_id\": setup_info.get(\"setup_id\"),\n                                \"broker_name\": setup_info.get(\"broker_name\"),\n                                \"instance_name\": setup_info.get(\"instance_name\")\n                            })\n\n            return {\n                \"status\": \"success\",\n                \"data\": {\n                    \"services\": services,\n                    \"total_services\": len(services),\n                    \"retrieved_at\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to get services: {str(e)}\"}\n\n    def extract_setup_info_from_service(self, service_name):\n        \"\"\"Extract setup information from service name (e.g., algo-angel-42)\"\"\"\n        try:\n            if service_name.startswith('algo-'):\n                parts = service_name[5:].split('-')  # Remove 'algo-' prefix\n                if len(parts) >= 2:\n                    broker_name = parts[0]\n                    setup_id = parts[1]\n                    instance_name = f\"{broker_name}-{setup_id}\"\n                    return {\n                        \"setup_id\": int(setup_id),\n                        \"broker_name\": broker_name,\n                        \"instance_name\": instance_name\n                    }\n        except:\n            pass\n        return {\"setup_id\": None, \"broker_name\": None, \"instance_name\": None}\n\n    def restart_service(self, service_name):\n        \"\"\"Restart a systemd service\"\"\"\n        try:\n            result = subprocess.run([\n                'sudo', 'systemctl', 'restart', service_name\n            ], capture_output=True, text=True)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"✅ Service '{service_name}' restarted successfully\",\n                    \"service_name\": service_name,\n                    \"action\": \"restart\",\n                    \"timestamp\": self.get_ist_timestamp()\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"❌ Failed to restart service '{service_name}': {result.stderr}\",\n                    \"service_name\": service_name\n                }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to restart service: {str(e)}\"}\n\n    def stop_service(self, service_name):\n        \"\"\"Stop a systemd service\"\"\"\n        try:\n            result = self.run_systemctl('stop', service_name)\n\n            if result and result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"✅ Service '{service_name}' stopped successfully\",\n                    \"service_name\": service_name,\n                    \"action\": \"stop\",\n                    \"timestamp\": self.get_ist_timestamp()\n                }\n            else:\n                error_msg = result.stderr if result else \"Unknown error\"\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"❌ Failed to stop service '{service_name}': {error_msg}\",\n                    \"service_name\": service_name\n                }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to stop service: {str(e)}\"}\n\n    def start_service(self, service_name):\n        \"\"\"Start a systemd service\"\"\"\n        try:\n            result = self.run_systemctl('start', service_name)\n\n            if result and result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"✅ Service '{service_name}' started successfully\",\n                    \"service_name\": service_name,\n                    \"action\": \"start\",\n                    \"timestamp\": self.get_ist_timestamp()\n                }\n            else:\n                error_msg = result.stderr if result else \"Unknown error\"\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"❌ Failed to start service '{service_name}': {error_msg}\",\n                    \"service_name\": service_name\n                }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to start service: {str(e)}\"}\n\n    def delete_service(self, service_name):\n        \"\"\"Delete a systemd service and its files\"\"\"\n        try:\n            # Stop the service first\n            subprocess.run(['sudo', 'systemctl', 'stop', service_name], capture_output=True)\n\n            # Disable the service\n            subprocess.run(['sudo', 'systemctl', 'disable', service_name], capture_output=True)\n\n            # Remove service file\n            service_file = f\"/etc/systemd/system/{service_name}.service\"\n            result = subprocess.run([\n                'sudo', 'rm', '-f', service_file\n            ], capture_output=True, text=True)\n\n            # Reload systemd daemon\n            subprocess.run(['sudo', 'systemctl', 'daemon-reload'], capture_output=True)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"✅ Service '{service_name}' deleted successfully\",\n                    \"service_name\": service_name,\n                    \"action\": \"delete\",\n                    \"timestamp\": self.get_ist_timestamp()\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"❌ Failed to delete service '{service_name}': {result.stderr}\",\n                    \"service_name\": service_name\n                }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to delete service: {str(e)}\"}\n\n    # ============================================================================\n    # BACKUP AND RESTORE METHODS\n    # ============================================================================\n\n    def backup_instance(self, setup_id):\n        \"\"\"Backup broker instance (database and .env file)\"\"\"\n        try:\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            # Use consistent naming: username-algo-broker-id\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n            instance_path = self.instances_dir / instance_name\n\n            if not instance_path.exists():\n                return {\"status\": \"error\", \"message\": \"Instance path not found\"}\n\n            # Create backup directory\n            backup_dir = self.base_dir / \"backups\"\n            backup_dir.mkdir(exist_ok=True)\n\n            # Create backup filename with timestamp\n            timestamp = self.get_ist_timestamp().replace(' ', '_').replace(':', '-')\n            backup_filename = f\"backup_{instance_name}_{timestamp}.tar.gz\"\n            backup_path = backup_dir / backup_filename\n\n            # Create tar archive with database and .env\n            import tarfile\n            with tarfile.open(backup_path, 'w:gz') as tar:\n                # Add .env file\n                env_file = instance_path / \".env\"\n                if env_file.exists():\n                    tar.add(env_file, arcname=\".env\")\n\n                # Add database directory\n                db_dir = instance_path / \"db\"\n                if db_dir.exists():\n                    tar.add(db_dir, arcname=\"db\")\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Backup created successfully for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"backup_file\": backup_filename,\n                    \"backup_path\": str(backup_path),\n                    \"instance_name\": instance_name,\n                    \"setup_name\": setup_data['setup_name'],\n                    \"created_at\": self.get_ist_timestamp(),\n                    \"size_mb\": round(backup_path.stat().st_size / (1024 * 1024), 2)\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to create backup: {str(e)}\"}\n\n    def restore_instance(self, setup_id, backup_file=None):\n        \"\"\"Restore broker instance from backup\"\"\"\n        try:\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            # Use consistent naming: username-algo-broker-id\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n            instance_path = self.instances_dir / instance_name\n\n            if not instance_path.exists():\n                return {\"status\": \"error\", \"message\": \"Instance path not found\"}\n\n            # Find backup file\n            backup_dir = self.base_dir / \"backups\"\n            if backup_file:\n                backup_path = backup_dir / backup_file\n            else:\n                # Find latest backup for this instance\n                backup_files = list(backup_dir.glob(f\"backup_{instance_name}_*.tar.gz\"))\n                if not backup_files:\n                    return {\"status\": \"error\", \"message\": \"No backup files found\"}\n                backup_path = max(backup_files, key=lambda x: x.stat().st_mtime)\n\n            if not backup_path.exists():\n                return {\"status\": \"error\", \"message\": \"Backup file not found\"}\n\n            # Stop service before restore\n            service_name = instance_name  # Use full instance name as service name\n            subprocess.run(['sudo', 'systemctl', 'stop', service_name], capture_output=True)\n\n            # Extract backup\n            import tarfile\n            with tarfile.open(backup_path, 'r:gz') as tar:\n                tar.extractall(instance_path)\n\n            # Restart service\n            subprocess.run(['sudo', 'systemctl', 'start', service_name], capture_output=True)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ Instance restored successfully from backup\",\n                \"data\": {\n                    \"backup_file\": backup_path.name,\n                    \"instance_name\": instance_name,\n                    \"setup_name\": setup_data['setup_name'],\n                    \"restored_at\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to restore from backup: {str(e)}\"}\n\n    def list_backups(self):\n        \"\"\"List all available backups\"\"\"\n        try:\n            backup_dir = self.base_dir / \"backups\"\n            backup_dir.mkdir(exist_ok=True)\n\n            backups = []\n            for backup_file in backup_dir.glob(\"backup_*.tar.gz\"):\n                stat = backup_file.stat()\n\n                # Parse filename to extract info\n                filename = backup_file.name\n                parts = filename.replace('.tar.gz', '').split('_')\n                if len(parts) >= 4:\n                    instance_name = f\"{parts[1]}-{parts[2]}\"\n                    timestamp = '_'.join(parts[3:])\n                else:\n                    instance_name = \"unknown\"\n                    timestamp = \"unknown\"\n\n                backups.append({\n                    \"filename\": filename,\n                    \"instance_name\": instance_name,\n                    \"timestamp\": timestamp,\n                    \"size_mb\": round(stat.st_size / (1024 * 1024), 2),\n                    \"created_at\": stat.st_mtime\n                })\n\n            # Sort by creation time (newest first)\n            backups.sort(key=lambda x: x['created_at'], reverse=True)\n\n            return {\n                \"status\": \"success\",\n                \"data\": {\n                    \"backups\": backups,\n                    \"total_backups\": len(backups),\n                    \"backup_directory\": str(backup_dir)\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to list backups: {str(e)}\"}\n\n    # ============================================================================\n    # API TESTING METHODS\n    # ============================================================================\n\n    def test_all_apis(self, setup_id):\n        \"\"\"Test all broker APIs and return detailed results\"\"\"\n        try:\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            # Use consistent naming: username-algo-broker-id\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            instance_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n            port = 5000 + setup_id\n            base_url = f\"http://127.0.0.1:{port}\"\n\n            # List of APIs to test\n            api_tests = [\n                {\"name\": \"funds\", \"endpoint\": \"/api/v1/funds\", \"method\": \"GET\"},\n                {\"name\": \"holdings\", \"endpoint\": \"/api/v1/holdings\", \"method\": \"GET\"},\n                {\"name\": \"positions\", \"endpoint\": \"/api/v1/positions\", \"method\": \"GET\"},\n                {\"name\": \"orders\", \"endpoint\": \"/api/v1/orders\", \"method\": \"GET\"},\n                {\"name\": \"orderbook\", \"endpoint\": \"/api/v1/orderbook\", \"method\": \"GET\"},\n                {\"name\": \"tradebook\", \"endpoint\": \"/api/v1/tradebook\", \"method\": \"GET\"},\n                {\"name\": \"profile\", \"endpoint\": \"/api/v1/profile\", \"method\": \"GET\"},\n                {\"name\": \"quotes\", \"endpoint\": \"/api/v1/quotes\", \"method\": \"POST\", \"data\": {\"exchange\": \"NSE\", \"token\": \"22\"}},\n                {\"name\": \"depth\", \"endpoint\": \"/api/v1/depth\", \"method\": \"POST\", \"data\": {\"exchange\": \"NSE\", \"token\": \"22\"}},\n                {\"name\": \"history\", \"endpoint\": \"/api/v1/history\", \"method\": \"POST\", \"data\": {\"exchange\": \"NSE\", \"token\": \"22\", \"resolution\": \"1\"}}\n            ]\n\n            results = []\n            session = requests.Session()\n\n            for test in api_tests:\n                try:\n                    url = base_url + test[\"endpoint\"]\n\n                    if test[\"method\"] == \"GET\":\n                        response = session.get(url, timeout=10)\n                    else:\n                        response = session.post(url, json=test.get(\"data\", {}), timeout=10)\n\n                    # Check if response is JSON\n                    try:\n                        json_data = response.json()\n                        is_json = True\n                        response_preview = str(json_data)[:200] + \"...\" if len(str(json_data)) > 200 else str(json_data)\n                    except:\n                        is_json = False\n                        response_preview = response.text[:200] + \"...\" if len(response.text) > 200 else response.text\n\n                    # Determine status\n                    if response.status_code == 200 and is_json:\n                        if isinstance(json_data, dict) and json_data.get(\"status\") == \"success\":\n                            status = \"success\"\n                            message = \"API working correctly\"\n                        elif \"login\" in response.text.lower() or \"<!doctype html>\" in response.text.lower():\n                            status = \"auth_required\"\n                            message = \"Authentication required\"\n                        else:\n                            status = \"partial\"\n                            message = \"API responding but may need authentication\"\n                    else:\n                        status = \"failed\"\n                        message = f\"HTTP {response.status_code}\"\n\n                    results.append({\n                        \"api_name\": test[\"name\"],\n                        \"endpoint\": test[\"endpoint\"],\n                        \"method\": test[\"method\"],\n                        \"status\": status,\n                        \"http_code\": response.status_code,\n                        \"is_json\": is_json,\n                        \"message\": message,\n                        \"response_preview\": response_preview,\n                        \"tested_at\": self.get_ist_timestamp()\n                    })\n\n                except Exception as e:\n                    results.append({\n                        \"api_name\": test[\"name\"],\n                        \"endpoint\": test[\"endpoint\"],\n                        \"method\": test[\"method\"],\n                        \"status\": \"error\",\n                        \"http_code\": None,\n                        \"is_json\": False,\n                        \"message\": f\"Connection error: {str(e)}\",\n                        \"response_preview\": \"\",\n                        \"tested_at\": self.get_ist_timestamp()\n                    })\n\n            # Calculate summary\n            total_apis = len(results)\n            successful_apis = len([r for r in results if r[\"status\"] == \"success\"])\n            failed_apis = len([r for r in results if r[\"status\"] in [\"failed\", \"error\"]])\n            auth_required_apis = len([r for r in results if r[\"status\"] == \"auth_required\"])\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"✅ API testing completed for '{setup_data['setup_name']}'\",\n                \"data\": {\n                    \"setup_id\": setup_id,\n                    \"setup_name\": setup_data['setup_name'],\n                    \"instance_name\": instance_name,\n                    \"base_url\": base_url,\n                    \"summary\": {\n                        \"total_apis\": total_apis,\n                        \"successful\": successful_apis,\n                        \"failed\": failed_apis,\n                        \"auth_required\": auth_required_apis,\n                        \"success_rate\": round((successful_apis / total_apis) * 100, 1) if total_apis > 0 else 0\n                    },\n                    \"api_results\": results,\n                    \"tested_at\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to test APIs: {str(e)}\"}\n\n    def get_api_status(self, setup_id):\n        \"\"\"Get quick API status for a broker\"\"\"\n        try:\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            port = 5000 + setup_id\n            base_url = f\"http://127.0.0.1:{port}\"\n\n            # Test key APIs quickly\n            key_apis = [\"funds\", \"holdings\", \"positions\"]\n            session = requests.Session()\n\n            api_status = {}\n            for api in key_apis:\n                try:\n                    response = session.get(f\"{base_url}/api/v1/{api}\", timeout=5)\n                    if response.status_code == 200:\n                        try:\n                            json_data = response.json()\n                            if isinstance(json_data, dict) and json_data.get(\"status\") == \"success\":\n                                api_status[api] = \"working\"\n                            else:\n                                api_status[api] = \"auth_required\"\n                        except:\n                            api_status[api] = \"auth_required\"\n                    else:\n                        api_status[api] = \"failed\"\n                except:\n                    api_status[api] = \"error\"\n\n            # Overall status\n            working_count = len([status for status in api_status.values() if status == \"working\"])\n            overall_status = \"healthy\" if working_count >= 2 else \"needs_attention\"\n\n            return {\n                \"status\": \"success\",\n                \"data\": {\n                    \"setup_id\": setup_id,\n                    \"setup_name\": setup_data['setup_name'],\n                    \"base_url\": base_url,\n                    \"overall_status\": overall_status,\n                    \"api_status\": api_status,\n                    \"working_apis\": working_count,\n                    \"total_tested\": len(key_apis),\n                    \"checked_at\": self.get_ist_timestamp()\n                }\n            }\n\n        except Exception as e:\n            return {\"status\": \"error\", \"message\": f\"Failed to get API status: {str(e)}\"}\n    \n    def update_setup_instance_info(self, setup_id, instance_info):\n        \"\"\"Update setup with OpenAlgo instance information\"\"\"\n        try:\n            conn = sqlite3.connect(self.db_path)\n            cursor = conn.cursor()\n            \n            # Add instance info as JSON in additional_config\n            cursor.execute(\"\"\"\n                UPDATE user_brokers \n                SET additional_config = ?\n                WHERE id = ?\n            \"\"\", (json.dumps(instance_info), setup_id))\n            \n            conn.commit()\n            conn.close()\n            \n        except Exception as e:\n            print(f\"Failed to update instance info: {e}\")\n\n# Test functions for named manual processes\ndef test_step_1_set_env():\n    \"\"\"Test STEP 1: SET_ENV\"\"\"\n    automation = BrokerAutomation()\n    result = automation.step_1_set_env(4)\n    print(\"🔧 STEP 1: SET_ENV RESULT:\")\n    print(json.dumps(result, indent=2))\n    return result\n\ndef test_step_2_start_algo():\n    \"\"\"Test STEP 2: START_ALGO\"\"\"\n    automation = BrokerAutomation()\n    result = automation.step_2_start_algo(4)\n    print(\"\\n🚀 STEP 2: START_ALGO RESULT:\")\n    print(json.dumps(result, indent=2))\n    return result\n\ndef test_step_3_register():\n    \"\"\"Test STEP 3: REGISTER\"\"\"\n    automation = BrokerAutomation()\n    result = automation.step_3_register(4)\n    print(\"\\n👤 STEP 3: REGISTER RESULT:\")\n    print(json.dumps(result, indent=2))\n    return result\n\ndef test_step_4_connect_broker():\n    \"\"\"Test STEP 4: CONNECT_BROKER\"\"\"\n    automation = BrokerAutomation()\n    result = automation.step_4_connect_broker(4)\n    print(\"\\n🏦 STEP 4: CONNECT_BROKER RESULT:\")\n    print(json.dumps(result, indent=2))\n    return result\n\nif __name__ == \"__main__\":\n    print(\"🎯 NAMED MANUAL PROCESS TEST\")\n    print(\"=\" * 60)\n\n    # Test all 4 steps\n    step1_result = test_step_1_set_env()\n\n    if step1_result[\"status\"] == \"success\":\n        print(\"\\n\" + \"=\" * 60)\n        print(\"✅ STEP 1 (SET_ENV) COMPLETED!\")\n        print(f\"📁 Instance: {step1_result['data']['instance_path']}\")\n        print(f\"🌐 Port: {step1_result['data']['port']}\")\n        print(f\"🔗 URL: {step1_result['data']['openalgo_url']}\")\n\n        # Test other steps\n        step2_result = test_step_2_start_algo()\n        if step2_result[\"status\"] == \"success\":\n            print(f\"\\n🚀 START COMMAND:\")\n            print(f\"   {step2_result['data']['start_command']}\")\n\n        step3_result = test_step_3_register()\n        if step3_result[\"status\"] == \"success\":\n            print(f\"\\n👤 REGISTRATION INFO:\")\n            creds = step3_result['data']['credentials']\n            print(f\"   Username: {creds['username']}\")\n            print(f\"   Email: {creds['email']}\")\n            print(f\"   Password: {creds['password']}\")\n            print(f\"   Full Name: {creds['full_name']}\")\n\n        step4_result = test_step_4_connect_broker()\n        if step4_result[\"status\"] == \"success\":\n            print(f\"\\n🏦 BROKER CONNECTION INFO:\")\n            broker_creds = step4_result['data']['broker_credentials']\n            print(f\"   Client ID: {broker_creds['client_id']}\")\n            print(f\"   Trading PIN: {broker_creds['trading_pin']}\")\n            print(f\"   TOTP Code: {broker_creds['totp_code']}\")\n\n            if step4_result['data'].get('xts_credentials'):\n                print(f\"   XTS Market API Key: {step4_result['data']['xts_credentials']['market_api_key']}\")\n\n        print(\"\\n\" + \"=\" * 60)\n        print(\"🎯 MANUAL PROCESS SUMMARY:\")\n        print(\"✅ STEP 1 (SET_ENV): Environment configured\")\n        print(\"✅ STEP 2 (START_ALGO): Ready to start OpenAlgo\")\n        print(\"✅ STEP 3 (REGISTER): User registration details ready\")\n        print(\"✅ STEP 4 (CONNECT_BROKER): Broker authentication ready\")\n        print(\"\\n🚀 ALL STEPS READY FOR MANUAL EXECUTION!\")\n\n    else:\n        print(f\"\\n❌ STEP 1 (SET_ENV) FAILED:\")\n        print(f\"   {step1_result['message']}\")\n\n    # ============================================================================\n    # SYSTEM SERVICE MANAGEMENT METHODS\n    # ============================================================================\n\n    def start_systemd_service(self, service_name):\n        \"\"\"Start a systemd service\"\"\"\n        try:\n            import subprocess\n            result = subprocess.run([\n                'sudo', 'systemctl', 'start', service_name\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"Service {service_name} started successfully\"\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to start service: {result.stderr}\"\n                }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error starting service: {str(e)}\"\n            }\n\n    def stop_systemd_service(self, service_name):\n        \"\"\"Stop a systemd service\"\"\"\n        try:\n            import subprocess\n            result = subprocess.run([\n                'sudo', 'systemctl', 'stop', service_name\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"Service {service_name} stopped successfully\"\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to stop service: {result.stderr}\"\n                }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error stopping service: {str(e)}\"\n            }\n\n    def restart_systemd_service(self, service_name):\n        \"\"\"Restart a systemd service\"\"\"\n        try:\n            import subprocess\n            result = subprocess.run([\n                'sudo', 'systemctl', 'restart', service_name\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"message\": f\"Service {service_name} restarted successfully\"\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to restart service: {result.stderr}\"\n                }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error restarting service: {str(e)}\"\n            }\n\n    def get_service_logs(self, service_name, lines=50):\n        \"\"\"Get logs for a systemd service\"\"\"\n        try:\n            import subprocess\n            result = subprocess.run([\n                'sudo', 'journalctl', '-u', service_name, '-n', str(lines), '--no-pager'\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode == 0:\n                return {\n                    \"status\": \"success\",\n                    \"logs\": result.stdout,\n                    \"lines\": lines\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": f\"Failed to get logs: {result.stderr}\",\n                    \"logs\": \"\"\n                }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error getting logs: {str(e)}\",\n                \"logs\": \"\"\n            }\n\n    def start_all_user_services(self, user_id):\n        \"\"\"Start all services for a specific user\"\"\"\n        try:\n            import subprocess\n\n            # Get all services for this user\n            result = subprocess.run([\n                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode != 0:\n                return {\n                    \"status\": \"error\",\n                    \"message\": \"Failed to list services\"\n                }\n\n            # Filter services for this user\n            user_services = []\n            for line in result.stdout.split('\\n'):\n                if f\"{user_id}-algo-\" in line and '.service' in line:\n                    service_name = line.split()[0]\n                    if 'inactive' in line or 'failed' in line:\n                        user_services.append(service_name)\n\n            started = 0\n            failed = 0\n\n            for service in user_services:\n                start_result = self.start_systemd_service(service)\n                if start_result.get('status') == 'success':\n                    started += 1\n                else:\n                    failed += 1\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Started {started} services, {failed} failed\",\n                \"started\": started,\n                \"failed\": failed\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error starting all services: {str(e)}\"\n            }\n\n    def stop_all_user_services(self, user_id):\n        \"\"\"Stop all services for a specific user\"\"\"\n        try:\n            import subprocess\n\n            # Get all services for this user\n            result = subprocess.run([\n                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode != 0:\n                return {\n                    \"status\": \"error\",\n                    \"message\": \"Failed to list services\"\n                }\n\n            # Filter services for this user\n            user_services = []\n            for line in result.stdout.split('\\n'):\n                if f\"{user_id}-algo-\" in line and '.service' in line:\n                    service_name = line.split()[0]\n                    if 'active' in line and 'running' in line:\n                        user_services.append(service_name)\n\n            stopped = 0\n            failed = 0\n\n            for service in user_services:\n                stop_result = self.stop_systemd_service(service)\n                if stop_result.get('status') == 'success':\n                    stopped += 1\n                else:\n                    failed += 1\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Stopped {stopped} services, {failed} failed\",\n                \"stopped\": stopped,\n                \"failed\": failed\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error stopping all services: {str(e)}\"\n            }\n\n    def cleanup_dead_services(self):\n        \"\"\"Clean up all dead/not-found systemd services\"\"\"\n        try:\n            import subprocess\n\n            # Get all services\n            result = subprocess.run([\n                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'\n            ], capture_output=True, text=True, timeout=60)\n\n            if result.returncode != 0:\n                return {\n                    \"status\": \"error\",\n                    \"message\": \"Failed to list services\"\n                }\n\n            # Find dead algo services\n            dead_services = []\n            for line in result.stdout.split('\\n'):\n                if 'algo-' in line and '.service' in line:\n                    if 'not-found' in line or ('inactive' in line and 'dead' in line):\n                        service_name = line.split()[0]\n                        dead_services.append(service_name)\n\n            cleaned = 0\n            failed = 0\n\n            print(f\"🧹 Found {len(dead_services)} dead services to clean up...\")\n\n            for service in dead_services:\n                try:\n                    # Disable the service\n                    disable_result = subprocess.run([\n                        'sudo', 'systemctl', 'disable', service\n                    ], capture_output=True, text=True, timeout=10)\n\n                    # Reset failed state\n                    reset_result = subprocess.run([\n                        'sudo', 'systemctl', 'reset-failed', service\n                    ], capture_output=True, text=True, timeout=10)\n\n                    # Remove service file if it exists\n                    service_file = f\"/etc/systemd/system/{service}\"\n                    remove_result = subprocess.run([\n                        'sudo', 'rm', '-f', service_file\n                    ], capture_output=True, text=True, timeout=10)\n\n                    print(f\"✅ Cleaned up: {service}\")\n                    cleaned += 1\n\n                except Exception as e:\n                    print(f\"❌ Failed to clean: {service} - {e}\")\n                    failed += 1\n\n            # Reload systemd daemon\n            subprocess.run(['sudo', 'systemctl', 'daemon-reload'], timeout=30)\n\n            return {\n                \"status\": \"success\",\n                \"message\": f\"Cleaned up {cleaned} dead services, {failed} failed\",\n                \"cleaned\": cleaned,\n                \"failed\": failed,\n                \"total_found\": len(dead_services)\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error cleaning up services: {str(e)}\"\n            }\n\n    def get_all_system_services(self):\n        \"\"\"Get all system services with their status\"\"\"\n        try:\n            import subprocess\n\n            # Get all services\n            result = subprocess.run([\n                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'\n            ], capture_output=True, text=True, timeout=30)\n\n            if result.returncode != 0:\n                return {\n                    \"status\": \"error\",\n                    \"message\": \"Failed to list services\"\n                }\n\n            services = []\n            for line in result.stdout.split('\\n'):\n                if '.service' in line and line.strip():\n                    parts = line.split()\n                    if len(parts) >= 4:\n                        service_name = parts[0]\n                        load_state = parts[1]\n                        active_state = parts[2]\n                        sub_state = parts[3]\n\n                        # Focus on algo services and system services\n                        if 'algo-' in service_name or service_name in ['nginx', 'mysql', 'postgresql', 'redis']:\n                            services.append({\n                                \"name\": service_name,\n                                \"load\": load_state,\n                                \"active\": active_state,\n                                \"sub\": sub_state,\n                                \"status\": f\"{active_state} ({sub_state})\"\n                            })\n\n            return {\n                \"status\": \"success\",\n                \"services\": services,\n                \"count\": len(services)\n            }\n\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"message\": f\"Error getting system services: {str(e)}\"\n            }\n\n    def connect_broker_manual_openalgo_style(self, setup_id, manual_credentials=None):\n        \"\"\"\n        Manual broker connection following OpenAlgo instance approach\n        For Angel: Uses clientid, pin, totp form submission\n        For Dhan: Uses direct callback approach\n        \"\"\"\n        try:\n            print(f\"🔗 MANUAL OPENALGO-STYLE CONNECTION: Starting for setup {setup_id}\")\n\n            setup_data = self.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"status\": \"error\", \"message\": \"Setup not found\"}\n\n            # Get the actual running port\n            port = self.get_actual_running_port(setup_id)\n            if not port:\n                return {\"status\": \"error\", \"message\": \"Cannot detect running Algo instance port\"}\n\n            print(f\"🌐 Using OpenAlgo instance on port: {port}\")\n\n            # Check if instance is running\n            if not self.check_instance_running(port):\n                return {\"status\": \"error\", \"message\": f\"OpenAlgo instance not running on port {port}\"}\n\n            # Get broker type\n            broker_name = setup_data.get('broker_name', '').lower()\n            print(f\"🏦 Broker type detected: {broker_name}\")\n\n            if broker_name == 'angel':\n                return self.connect_angel_manual_openalgo_style(setup_id, port, setup_data, manual_credentials)\n            elif broker_name == 'dhan':\n                return self.connect_dhan_manual_openalgo_style(setup_id, port, setup_data)\n            else:\n                return {\"status\": \"error\", \"message\": f\"Manual connection not supported for broker: {broker_name}\"}\n\n        except Exception as e:\n            print(f\"❌ Manual OpenAlgo-style connection failed: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Manual connection failed: {str(e)}\"}\n\n    def connect_angel_manual_openalgo_style(self, setup_id, port, setup_data, manual_credentials):\n        \"\"\"\n        Manual Angel broker connection using OpenAlgo instance form submission approach\n        \"\"\"\n        try:\n            print(f\"👼 ANGEL MANUAL CONNECTION: Using OpenAlgo form submission approach\")\n\n            # Extract credentials\n            if manual_credentials:\n                clientid = manual_credentials.get('clientid') or setup_data.get('broker_client_id')\n                pin = manual_credentials.get('pin') or setup_data.get('trading_pin')\n                totp = manual_credentials.get('totp')\n            else:\n                clientid = setup_data.get('broker_client_id')\n                pin = setup_data.get('trading_pin')\n                totp = None\n\n            if not clientid or not pin:\n                return {\"status\": \"error\", \"message\": \"Missing Angel broker credentials (clientid/pin)\"}\n\n            if not totp:\n                return {\"status\": \"error\", \"message\": \"TOTP is required for Angel broker manual connection\"}\n\n            print(f\"📋 Using credentials - Client ID: {clientid}, PIN: {'*' * len(pin)}\")\n\n            # STEP 1: Login to OpenAlgo instance\n            print(f\"🔐 STEP 1: Login to OpenAlgo instance...\")\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                print(f\"❌ Failed to login to OpenAlgo instance on port {port}\")\n                return {\"status\": \"error\", \"message\": \"Failed to login to OpenAlgo instance\"}\n\n            # STEP 2: Access Angel callback page to get form\n            print(f\"📄 STEP 2: Access Angel authentication form...\")\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n            angel_auth_url = f\"{openalgo_url}/angel/callback\"\n\n            auth_form_page = session.get(angel_auth_url, timeout=10)\n            print(f\"📄 Angel auth form response: {auth_form_page.status_code}\")\n            if auth_form_page.status_code != 200:\n                print(f\"❌ Failed to access Angel auth form: {auth_form_page.status_code}\")\n                return {\"status\": \"error\", \"message\": f\"Failed to access Angel auth form: {auth_form_page.status_code}\"}\n\n            # Extract CSRF token if present\n            csrf_token = None\n            if 'csrf_token' in auth_form_page.text:\n                import re\n                csrf_match = re.search(r'name=\"csrf_token\" value=\"([^\"]+)\"', auth_form_page.text)\n                if csrf_match:\n                    csrf_token = csrf_match.group(1)\n                    print(f\"🔒 CSRF token extracted\")\n\n            # STEP 3: Submit Angel authentication form\n            print(f\"📤 STEP 3: Submit Angel authentication form...\")\n            auth_data = {\n                \"clientid\": clientid,\n                \"pin\": pin,\n                \"totp\": totp\n            }\n\n            if csrf_token:\n                auth_data[\"csrf_token\"] = csrf_token\n\n            auth_response = session.post(\n                angel_auth_url,\n                data=auth_data,\n                allow_redirects=True,\n                timeout=15\n            )\n\n            print(f\"📄 Angel auth response status: {auth_response.status_code}\")\n            print(f\"📄 Angel auth response URL: {auth_response.url}\")\n\n            # Check if authentication was successful\n            if auth_response.status_code == 200:\n                response_text = auth_response.text.lower()\n\n                # Check for success indicators\n                if (\"dashboard\" in auth_response.url.lower() or\n                    \"dashboard\" in response_text or\n                    \"success\" in response_text or\n                    \"authenticated\" in response_text):\n\n                    print(\"✅ Angel manual authentication successful\")\n\n                    # STEP 4: Retrieve and save tokens\n                    print(f\"🔑 STEP 4: Retrieve authentication tokens...\")\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n\n                    # Update instance info\n                    instance_info = {\n                        \"step_completed\": \"connect_broker\",\n                        \"status\": \"broker_connected\",\n                        \"connected_at\": self.get_ist_timestamp(),\n                        \"broker_type\": \"angel\",\n                        \"connection_method\": \"manual_openalgo_style\"\n                    }\n                    self.update_setup_instance_info(setup_id, instance_info)\n\n                    return {\n                        \"status\": \"success\",\n                        \"message\": f\"✅ Angel broker connected manually using OpenAlgo style for '{setup_data['setup_name']}'\",\n                        \"data\": {\n                            \"step\": \"connect_broker\",\n                            \"url\": f\"http://127.0.0.1:{port}\",\n                            \"connection_status\": \"connected\",\n                            \"connection_method\": \"manual_openalgo_style\",\n                            \"auth_tokens\": tokens if tokens else {\"status\": \"authenticated\"}\n                        }\n                    }\n                else:\n                    # Check for error messages in response\n                    print(f\"❌ Angel auth failed - response text preview: {response_text[:200]}...\")\n                    if \"invalid\" in response_text or \"error\" in response_text:\n                        return {\"status\": \"error\", \"message\": \"Invalid credentials or TOTP. Please check and try again.\"}\n                    else:\n                        return {\"status\": \"error\", \"message\": \"Authentication failed. Please verify your credentials.\"}\n            else:\n                print(f\"❌ Angel authentication failed with status: {auth_response.status_code}\")\n                return {\"status\": \"error\", \"message\": f\"Angel authentication failed with status: {auth_response.status_code}\"}\n\n        except Exception as e:\n            print(f\"❌ Angel manual connection failed: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Angel manual connection failed: {str(e)}\"}\n\n    def connect_dhan_manual_openalgo_style(self, setup_id, port, setup_data):\n        \"\"\"\n        Manual Dhan broker connection using OpenAlgo instance direct callback approach\n        \"\"\"\n        try:\n            print(f\"🏦 DHAN MANUAL CONNECTION: Using OpenAlgo direct callback approach\")\n\n            # STEP 1: Login to OpenAlgo instance\n            print(f\"🔐 STEP 1: Login to OpenAlgo instance...\")\n            session = self.ultra_fast_login(port, setup_data)\n            if not session:\n                return {\"status\": \"error\", \"message\": \"Failed to login to OpenAlgo instance\"}\n\n            # STEP 2: Trigger Dhan callback directly (no form needed)\n            print(f\"🔗 STEP 2: Trigger Dhan authentication callback...\")\n            openalgo_url = f\"http://127.0.0.1:{port}\"\n            dhan_callback_url = f\"{openalgo_url}/dhan/callback\"\n\n            # Dhan doesn't need form data - just call the callback\n            auth_response = session.get(dhan_callback_url, timeout=10, allow_redirects=True)\n\n            print(f\"📄 Dhan auth response status: {auth_response.status_code}\")\n            print(f\"📄 Dhan auth response URL: {auth_response.url}\")\n\n            # Check if authentication was successful\n            if auth_response.status_code == 200:\n                response_text = auth_response.text.lower()\n\n                # Check for success indicators\n                if (\"dashboard\" in auth_response.url.lower() or\n                    \"dashboard\" in response_text or\n                    \"success\" in response_text or\n                    \"authenticated\" in response_text):\n\n                    print(\"✅ Dhan manual authentication successful\")\n\n                    # STEP 3: Retrieve and save tokens\n                    print(f\"🔑 STEP 3: Retrieve authentication tokens...\")\n                    tokens = self.retrieve_and_save_tokens(port, setup_data)\n\n                    # Update instance info\n                    instance_info = {\n                        \"step_completed\": \"connect_broker\",\n                        \"status\": \"broker_connected\",\n                        \"connected_at\": self.get_ist_timestamp(),\n                        \"broker_type\": \"dhan\",\n                        \"connection_method\": \"manual_openalgo_style\"\n                    }\n                    self.update_setup_instance_info(setup_id, instance_info)\n\n                    return {\n                        \"status\": \"success\",\n                        \"message\": f\"✅ Dhan broker connected manually using OpenAlgo style for '{setup_data['setup_name']}'\",\n                        \"data\": {\n                            \"step\": \"connect_broker\",\n                            \"url\": f\"http://127.0.0.1:{port}\",\n                            \"connection_status\": \"connected\",\n                            \"connection_method\": \"manual_openalgo_style\",\n                            \"auth_tokens\": tokens if tokens else {\"status\": \"authenticated\"}\n                        }\n                    }\n                else:\n                    return {\"status\": \"error\", \"message\": \"Dhan authentication failed. Please check your broker configuration.\"}\n            else:\n                return {\"status\": \"error\", \"message\": f\"Dhan authentication failed with status: {auth_response.status_code}\"}\n\n        except Exception as e:\n            print(f\"❌ Dhan manual connection failed: {e}\")\n            return {\"status\": \"error\", \"message\": f\"Dhan manual connection failed: {str(e)}\"}\n"}