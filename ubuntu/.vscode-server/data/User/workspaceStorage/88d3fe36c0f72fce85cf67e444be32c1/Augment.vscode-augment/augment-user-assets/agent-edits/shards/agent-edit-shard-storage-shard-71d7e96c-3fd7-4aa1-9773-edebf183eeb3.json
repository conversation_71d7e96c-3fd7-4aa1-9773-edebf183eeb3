{"id": "shard-71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "checkpoints": {"71d7e96c-3fd7-4aa1-9773-edebf183eeb3:/home/<USER>/algofactory/scripts/automation/broker_automation.py": [{"sourceToolCallRequestId": "14cd437b-3997-4a4c-a8eb-7b3839684976", "timestamp": 0, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "74137af8-a458-4f46-8b02-d6af53f725da", "timestamp": 1753976161849, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "9ad0dbf6-9d52-47aa-b960-bc15fe4b7d3a", "timestamp": 1753976162448, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "f38830bf-8b28-4ba1-932d-029561ea1224", "timestamp": 1753976162448, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "1c9005f1-3fbf-494d-9dc0-69b3e0e80875", "timestamp": 1753976194863, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "9d76fb44-2457-465f-afaf-f02887d8bd3b", "timestamp": 1753976194999, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "3c699ba7-948f-41a6-9d4f-406d0c041b65", "timestamp": 1753976194999, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "0ca03a8a-ce34-4728-8c6a-f7199dbb25be", "timestamp": 1753976218444, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "f2d31ac8-bc28-4533-907c-5235a16be49a", "timestamp": 1753976218706, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "cf434902-cc1a-4c5d-9879-93343d0fe284", "timestamp": 1753976218706, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "lastIncludedInRequestId": "00419101-cf99-4df5-b9e6-6791df420c7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "8876252e-1f23-4270-82d9-0143d0cb10e0", "timestamp": 1753976820749, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "fcf3b501-5a2b-427b-8d1d-cc253fdb7695", "timestamp": 1753976821029, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "25a09e2c-4421-4616-b0e7-d7249ad70ba7", "timestamp": 1753976821029, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "699eafe5-e210-4ddb-bf5a-d3022c8ebc1e", "timestamp": 1753976835280, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "bf37d644-25a4-424f-9b63-7fac08bb6627", "timestamp": 1753976835414, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "d76b7f31-afd9-4952-a424-3b8e6165425f", "timestamp": 1753976835414, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "a064fb07-f0a3-4966-9e9e-ae0ac623b769", "timestamp": 1753976851108, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "49e26564-b398-4e7f-bc77-cbf0062c693f", "timestamp": 1753976851222, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}, {"sourceToolCallRequestId": "99662a46-7807-402f-a4c3-21302ffa6abe", "timestamp": 1753976851222, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/scripts/automation/broker_automation.py"}}}], "71d7e96c-3fd7-4aa1-9773-edebf183eeb3:/home/<USER>/algofactory/backend/api/services/broker_service.py": [{"sourceToolCallRequestId": "ba903830-e354-4116-a869-78b863cf9af0", "timestamp": 0, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/services/broker_service.py"}}}, {"sourceToolCallRequestId": "ff161d2a-5054-488a-8d2c-2cccb83f41cc", "timestamp": 1753976272619, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/services/broker_service.py"}}}, {"sourceToolCallRequestId": "633e8546-c78c-4e89-abaf-f38b0bd5c973", "timestamp": 1753976272784, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/services/broker_service.py"}}}, {"sourceToolCallRequestId": "a0dbea20-15d6-4e18-a59d-75fd3d2a3027", "timestamp": 1753976272784, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "lastIncludedInRequestId": "00419101-cf99-4df5-b9e6-6791df420c7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/services/broker_service.py"}}}, {"sourceToolCallRequestId": "021fed17-0fcf-4829-a75e-f956ed760b4f", "timestamp": 1753976876399, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/services/broker_service.py"}}}, {"sourceToolCallRequestId": "aa4f0a04-ae1f-4a19-b606-5e118bcad1b1", "timestamp": 1753976876487, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/services/broker_service.py"}}}, {"sourceToolCallRequestId": "30d7daf0-bdb8-473f-9455-19949230f13b", "timestamp": 1753976876487, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/services/broker_service.py"}}}], "71d7e96c-3fd7-4aa1-9773-edebf183eeb3:/home/<USER>/algofactory/backend/api/main_api.py": [{"sourceToolCallRequestId": "6712f59c-f97b-42e7-8a10-bb2347b66574", "timestamp": 0, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/main_api.py"}}}, {"sourceToolCallRequestId": "0bbeeffc-fc6c-49a8-b0ae-70ef670f2b60", "timestamp": 1753976292178, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/main_api.py"}}}, {"sourceToolCallRequestId": "14171002-6982-4f42-9201-c47d7d5da2d0", "timestamp": 1753976292323, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/main_api.py"}}}, {"sourceToolCallRequestId": "a45d9edf-72ad-4c12-845c-bd5411faf249", "timestamp": 1753976292323, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "lastIncludedInRequestId": "00419101-cf99-4df5-b9e6-6791df420c7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/main_api.py"}}}], "71d7e96c-3fd7-4aa1-9773-edebf183eeb3:/home/<USER>/algofactory/frontend/shared/js/user-details.js": [{"sourceToolCallRequestId": "ff2e2359-0a5a-4634-979d-d1b85d1c4ae1", "timestamp": 0, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "c4785a08-1ea2-46e7-bd75-ad70e6fc69bd", "timestamp": 1753976331149, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "b3cf663f-8443-4779-9d53-eb813216bda7", "timestamp": 1753976331256, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "1d4e84ab-593b-4257-beb9-1cdec23ae0da", "timestamp": 1753976361685, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "716e60dd-8c0d-4f7e-95c5-358e5c756b31", "timestamp": 1753976361801, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "0b341428-2202-40d1-9e5d-c2fef8f139e0", "timestamp": 1753976384983, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "c5323f47-b918-43cc-bb15-647ab4cfadba", "timestamp": 1753976385106, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "f3835984-59fa-43f3-98ba-df398c0bc820", "timestamp": 1753976396110, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "83654e4e-e0df-4030-bc87-dfdfcfd4d421", "timestamp": 1753976396224, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "6257ff7b-d678-40d9-a427-5696c0ae14ef", "timestamp": 1753976411602, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "3f0c83e8-2b62-416f-8c27-35fb211642db", "timestamp": 1753976411718, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "99a5c17a-913e-46dd-aeec-28782b7d895d", "timestamp": 1753976444167, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "f657c83a-b038-4b80-bcaf-b1b2af3404d7", "timestamp": 1753976444318, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "lastIncludedInRequestId": "00419101-cf99-4df5-b9e6-6791df420c7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "00419101-cf99-4df5-b9e6-6791df420c7b", "timestamp": 1753976724961, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "006c777c-2f93-4430-9c35-85fb8fa612c7", "timestamp": 1753976725328, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "2fb46085-4544-4930-b4c5-7c5a741f1c78", "timestamp": 1753976753383, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "f7f32f1b-aae7-4ad9-8b5b-0945b8438730", "timestamp": 1753976753540, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "4116a156-a3f6-4452-8d60-6ba628affecf", "timestamp": 1753976771503, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "616c4aad-37fe-403c-a088-c469ddb2dd88", "timestamp": 1753976771923, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "6ac25083-1c63-4a0b-9cd5-850b63b53270", "timestamp": 1753976794178, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "733af41e-7be2-4d50-877f-311569eb1c47", "timestamp": 1753976795539, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "b0f9653d-eb5c-412f-8a2d-a35512db9641", "timestamp": 1753976895805, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "44d6501b-acdb-43f0-a71d-f886804929a8", "timestamp": 1753976895971, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "cb3fe46d-d2bf-40eb-96b0-7b0842895419", "timestamp": 1753976909644, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "d91954be-aa03-4130-b2b2-4c2c337b3e61", "timestamp": 1753976909757, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "4958c5b3-462a-43db-8cc9-67c7e6674a3b", "timestamp": 1753976922766, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}, {"sourceToolCallRequestId": "b164eff8-30cd-4944-92f2-78ed6cd9ba64", "timestamp": 1753976922876, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/frontend/shared/js/user-details.js"}}}], "71d7e96c-3fd7-4aa1-9773-edebf183eeb3:/home/<USER>/algofactory/test_manual_openalgo_connection.py": [{"sourceToolCallRequestId": "8c271769-30a2-490f-a41e-e01943f10f43", "timestamp": 1753976959019, "conversationId": "71d7e96c-3fd7-4aa1-9773-edebf183eeb3", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/test_manual_openalgo_connection.py"}}}]}, "metadata": {"checkpointDocumentIds": ["71d7e96c-3fd7-4aa1-9773-edebf183eeb3:/home/<USER>/algofactory/scripts/automation/broker_automation.py", "71d7e96c-3fd7-4aa1-9773-edebf183eeb3:/home/<USER>/algofactory/backend/api/services/broker_service.py", "71d7e96c-3fd7-4aa1-9773-edebf183eeb3:/home/<USER>/algofactory/backend/api/main_api.py", "71d7e96c-3fd7-4aa1-9773-edebf183eeb3:/home/<USER>/algofactory/frontend/shared/js/user-details.js", "71d7e96c-3fd7-4aa1-9773-edebf183eeb3:/home/<USER>/algofactory/test_manual_openalgo_connection.py"], "size": 25473546, "checkpointCount": 58, "lastModified": 1753976959050}}