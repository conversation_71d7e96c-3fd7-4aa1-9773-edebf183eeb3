{"path": {"rootPath": "/home", "relPath": "ubuntu/algofactory/backend/api/services/broker_service.py"}, "originalCode": "\"\"\"\nBroker Service for AlgoFactory API\nBusiness logic for broker-related operations\n\"\"\"\n\nimport sys\nimport logging\nfrom typing import List, Optional, Dict, Any\n\n# Setup logging\nlogger = logging.getLogger(__name__)\n\nclass BrokerService:\n    \"\"\"Service class for broker operations\"\"\"\n    \n    def __init__(self):\n        \"\"\"Initialize broker service\"\"\"\n        # Import broker automation\n        sys.path.append('/home/<USER>/algofactory/scripts/automation')\n        from broker_automation import BrokerAutomation\n        self.broker_automation = BrokerAutomation()\n        logger.info(\"✅ Broker service initialized with automation\")\n    \n    def get_all_broker_setups(self) -> List[Dict[str, Any]]:\n        \"\"\"Get all broker setups from database\"\"\"\n        try:\n            logger.info(\"📋 Getting all broker setups...\")\n            setups = self.broker_automation.get_all_broker_setups()\n\n            logger.info(f\"✅ Retrieved {len(setups)} broker setups\")\n            return setups\n\n        except Exception as e:\n            logger.error(f\"❌ Error getting broker setups: {e}\")\n            raise Exception(f\"Failed to get broker setups: {str(e)}\")\n    \n    def create_broker_setup(self, setup_data: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"Create a new broker setup using the multi-tenant API approach\"\"\"\n        try:\n            logger.info(f\"🆕 Creating new broker setup for user: {setup_data.get('username')}\")\n\n            # Import required modules\n            import sqlite3\n            import json\n            from datetime import datetime, timezone, timedelta\n\n            # Database path\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            # Get IST timestamp\n            ist = timezone(timedelta(hours=5, minutes=30))\n            created_at_ist = datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S')\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n\n                # Get setup data\n                username = setup_data.get('username')\n                broker_name = setup_data.get('broker_name')\n                admin_id = setup_data.get('admin_id', 'Admin_1')\n                creator_name = setup_data.get('created_by', 'superadmin')\n\n                # Validate username format\n                if not username or not username.isdigit():\n                    return {\n                        \"success\": False,\n                        \"message\": f\"Invalid username format: {username}. Should be numeric like '1001'\"\n                    }\n\n                # Check if user exists\n                cursor.execute(\"SELECT username FROM users WHERE username = ?\", (username,))\n                if not cursor.fetchone():\n                    return {\n                        \"success\": False,\n                        \"message\": f\"User {username} not found\"\n                    }\n\n                # Insert broker setup into user_brokers table first to get the ID\n                cursor.execute(\"\"\"\n                    INSERT INTO user_brokers (\n                        username, broker_name, setup_name, instance_number,\n                        instance_name, broker_client_id, broker_api_key,\n                        broker_api_secret, broker_api_key_market, trading_pin,\n                        totp_secret, setup_status, connection_status, additional_config\n                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'not_connected', ?)\n                \"\"\", (\n                    username,\n                    broker_name,\n                    setup_data.get('setup_name'),\n                    1,  # temporary instance number\n                    'temp',  # temporary instance name\n                    setup_data.get('broker_client_id'),\n                    setup_data.get('broker_api_key'),\n                    setup_data.get('broker_api_secret'),\n                    setup_data.get('broker_api_key_market', ''),\n                    setup_data.get('trading_pin'),\n                    setup_data.get('totp_secret'),\n                    json.dumps({\n                        \"creator\": creator_name,\n                        \"created_at_ist\": created_at_ist,\n                        \"created_for\": username\n                    })\n                ))\n\n                setup_id = cursor.lastrowid\n\n                # Now update with correct instance name using the actual setup_id\n                correct_instance_name = f\"{username}-algo-{broker_name}-{setup_id}\"\n                cursor.execute(\"\"\"\n                    UPDATE user_brokers\n                    SET instance_name = ?, instance_number = ?\n                    WHERE id = ?\n                \"\"\", (correct_instance_name, setup_id, setup_id))\n\n                # Update with final instance info using setup_id\n                final_instance_name = f\"{username}-algo-{broker_name}-{setup_id}\"\n\n                cursor.execute(\"\"\"\n                    UPDATE user_brokers\n                    SET instance_number = ?, instance_name = ?\n                    WHERE id = ?\n                \"\"\", (setup_id, final_instance_name, setup_id))\n\n                conn.commit()\n\n                logger.info(f\"✅ Broker setup created successfully with ID: {setup_id}\")\n\n                # Try to run automation\n                try:\n                    logger.info(f\"🚀 Starting broker automation for setup_id: {setup_id}\")\n\n                    # STEP 1: SET_ENV - Create instance folder, clone OpenAlgo, configure .env\n                    automation_result = self.broker_automation.step_1_set_env(setup_id)\n\n                    if automation_result['status'] == 'success':\n                        # Update status to automated\n                        cursor.execute(\"\"\"\n                            UPDATE user_brokers\n                            SET setup_status = 'env_configured', detailed_status = ?\n                            WHERE id = ?\n                        \"\"\", (json.dumps(automation_result), setup_id))\n                        conn.commit()\n\n                        return {\n                            \"success\": True,\n                            \"message\": \"Broker setup created and environment configured successfully\",\n                            \"setup_id\": setup_id,\n                            \"automation_status\": \"env_configured\",\n                            \"instance_path\": automation_result.get('data', {}).get('instance_path'),\n                            \"algo_url\": automation_result.get('data', {}).get('algo_url') or f\"http://127.0.0.1:{5000 + setup_id}\",\n                            \"data\": {\n                                \"username\": username,\n                                \"broker_name\": broker_name,\n                                \"setup_name\": setup_data.get('setup_name'),\n                                \"instance_name\": final_instance_name,\n                                \"admin_id\": admin_id\n                            }\n                        }\n                    else:\n                        # Update status to failed\n                        cursor.execute(\"\"\"\n                            UPDATE user_brokers\n                            SET setup_status = 'failed', detailed_status = ?\n                            WHERE id = ?\n                        \"\"\", (json.dumps(automation_result), setup_id))\n                        conn.commit()\n\n                        return {\n                            \"success\": True,\n                            \"message\": \"Broker setup created but automation failed\",\n                            \"setup_id\": setup_id,\n                            \"automation_status\": \"failed\",\n                            \"automation_error\": automation_result.get('message'),\n                            \"data\": {\n                                \"username\": username,\n                                \"broker_name\": broker_name,\n                                \"setup_name\": setup_data.get('setup_name'),\n                                \"instance_name\": final_instance_name,\n                                \"admin_id\": admin_id\n                            }\n                        }\n\n                except Exception as automation_error:\n                    logger.error(f\"❌ Automation failed: {automation_error}\")\n\n                    # Update status to error\n                    cursor.execute(\"\"\"\n                        UPDATE user_brokers\n                        SET setup_status = 'error', detailed_status = ?\n                        WHERE id = ?\n                    \"\"\", (json.dumps({\"error\": str(automation_error)}), setup_id))\n                    conn.commit()\n\n                    return {\n                        \"success\": True,\n                        \"message\": \"Broker setup created but automation system failed\",\n                        \"setup_id\": setup_id,\n                        \"automation_status\": \"error\",\n                        \"automation_error\": str(automation_error),\n                        \"data\": {\n                            \"username\": username,\n                            \"broker_name\": broker_name,\n                            \"setup_name\": setup_data.get('setup_name'),\n                            \"instance_name\": final_instance_name,\n                            \"admin_id\": admin_id\n                        }\n                    }\n\n        except Exception as e:\n            logger.error(f\"❌ Error creating broker setup: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to create broker setup: {str(e)}\"\n            }\n\n    def create_broker_setup_from_saved(self, saved_broker_id: int, user_id: str, admin_id: str) -> Dict[str, Any]:\n        \"\"\"Create a new broker setup from saved broker configuration\"\"\"\n        try:\n            logger.info(f\"🆕 Creating broker setup from saved configuration ID: {saved_broker_id}\")\n            logger.info(f\"🔍 Debug: user_id={user_id}, admin_id={admin_id}, saved_broker_id={saved_broker_id}\")\n\n            # Import required modules\n            import sqlite3\n            from datetime import datetime, timezone, timedelta\n\n            # Database path\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            # Get IST timestamp\n            ist = timezone(timedelta(hours=5, minutes=30))\n            created_at_ist = datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S')\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n\n                # Get saved broker configuration\n                cursor.execute(\"\"\"\n                    SELECT user_id, admin_id, broker_name, broker_type, trading_setup_name,\n                           client_id, api_key, api_secret, trading_pin, totp_secret\n                    FROM saved_broker_details\n                    WHERE id = ?\n                \"\"\", (saved_broker_id,))\n\n                saved_broker = cursor.fetchone()\n                if not saved_broker:\n                    return {\n                        \"success\": False,\n                        \"message\": f\"Saved broker configuration {saved_broker_id} not found\"\n                    }\n\n                # Extract saved broker data\n                saved_user_id = saved_broker[0]\n                saved_admin_id = saved_broker[1]\n                broker_name = saved_broker[2]\n                setup_name = saved_broker[4] or f\"{broker_name}_setup\"\n                client_id = saved_broker[5]\n                api_key = saved_broker[6]\n                api_secret = saved_broker[7]\n                trading_pin = saved_broker[8]\n                totp_secret = saved_broker[9]\n\n                # Use provided user_id and admin_id, or fall back to saved ones\n                target_user_id = user_id if user_id else saved_user_id\n                target_admin_id = admin_id if admin_id else saved_admin_id\n\n                logger.info(f\"📋 Using saved broker config: {setup_name} for user {target_user_id}\")\n                logger.info(f\"🔍 Debug: saved_user_id={saved_user_id}, provided_user_id={user_id}, final_target_user_id={target_user_id}\")\n\n                # Validate username format\n                if not target_user_id or not str(target_user_id).isdigit():\n                    return {\n                        \"success\": False,\n                        \"message\": f\"Invalid username format: {target_user_id}. Should be numeric like '1001'\"\n                    }\n\n                # Check if user exists\n                cursor.execute(\"SELECT username FROM users WHERE username = ?\", (str(target_user_id),))\n                if not cursor.fetchone():\n                    return {\n                        \"success\": False,\n                        \"message\": f\"User {target_user_id} not found\"\n                    }\n\n                # Get next instance number for this user and broker\n                cursor.execute(\"\"\"\n                    SELECT COALESCE(MAX(instance_number), 0) + 1\n                    FROM user_brokers\n                    WHERE username = ? AND broker_name = ?\n                \"\"\", (str(target_user_id), broker_name))\n                instance_number = cursor.fetchone()[0]\n\n                # Insert broker setup into user_brokers table first with temporary instance name\n                cursor.execute(\"\"\"\n                    INSERT INTO user_brokers (\n                        username, broker_name, setup_name, instance_number,\n                        instance_name, broker_client_id, broker_api_key,\n                        broker_api_secret, trading_pin, totp_secret,\n                        setup_status, connection_status, http_port, created_at\n                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'stopped', 'not_connected', ?, ?)\n                \"\"\", (\n                    str(target_user_id),\n                    broker_name,\n                    setup_name,\n                    instance_number,\n                    f\"temp-{target_user_id}-{broker_name}\",  # Temporary name\n                    client_id,\n                    api_key,\n                    api_secret,\n                    trading_pin,\n                    totp_secret,\n                    5000 + int(target_user_id),  # Port based on user ID\n                    created_at_ist\n                ))\n\n                setup_id = cursor.lastrowid\n\n                # Now update with correct instance name using the actual setup_id\n                correct_instance_name = f\"{target_user_id}-algo-{broker_name}-{setup_id}\"\n                cursor.execute(\"\"\"\n                    UPDATE user_brokers\n                    SET instance_name = ?\n                    WHERE id = ?\n                \"\"\", (correct_instance_name, setup_id))\n\n                conn.commit()\n\n                logger.info(f\"✅ Broker setup created successfully with ID: {setup_id}\")\n\n                # Now run SET_ENV step to create the instance folder\n                logger.info(f\"🔧 Running SET_ENV step for setup ID: {setup_id}\")\n                env_result = self.broker_automation.step_1_set_env(setup_id)\n\n                if env_result.get('status') == 'success':\n                    logger.info(f\"✅ SET_ENV completed successfully for setup ID: {setup_id}\")\n                    final_status = \"env_configured\"\n\n                    # Update status in database\n                    cursor.execute(\"UPDATE user_brokers SET setup_status = ? WHERE id = ?\",\n                                 (final_status, setup_id))\n                    conn.commit()\n                else:\n                    logger.warning(f\"⚠️ SET_ENV failed for setup ID: {setup_id}: {env_result.get('message')}\")\n                    final_status = \"setup_failed\"\n\n                return {\n                    \"success\": True,\n                    \"message\": f\"Broker setup '{setup_name}' created and configured successfully\",\n                    \"data\": {\n                        \"setup_id\": setup_id,\n                        \"username\": target_user_id,\n                        \"broker_name\": broker_name,\n                        \"setup_name\": setup_name,\n                        \"instance_name\": correct_instance_name,\n                        \"instance_number\": instance_number,\n                        \"status\": final_status,\n                        \"connection_status\": \"not_connected\",\n                        \"saved_broker_id\": saved_broker_id,\n                        \"env_setup\": env_result.get('status') == 'success'\n                    }\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error creating broker setup from saved config: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to create broker setup: {str(e)}\"\n            }\n\n    def get_broker_setup_by_id(self, setup_id: int) -> Optional[Dict[str, Any]]:\n        \"\"\"Get a specific broker setup by ID\"\"\"\n        try:\n            logger.info(f\"🔍 Getting broker setup: {setup_id}\")\n            \n            setup = self.broker_automation.get_setup_by_id(setup_id)\n            \n            if setup:\n                logger.info(f\"✅ Broker setup found: {setup_id}\")\n                return setup\n            else:\n                logger.warning(f\"⚠️ Broker setup not found: {setup_id}\")\n                return None\n                \n        except Exception as e:\n            logger.error(f\"❌ Error getting broker setup {setup_id}: {e}\")\n            raise Exception(f\"Failed to get broker setup: {str(e)}\")\n    \n    # REMOVED: connect_broker_manual() - Old method, use run_complete_manual_setup() instead\n\n    def start_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Start broker instance with proper status tracking\"\"\"\n        try:\n            logger.info(f\"▶️ Starting broker for setup: {setup_id}\")\n\n            # Update status to starting\n            self.broker_automation.update_setup_status(setup_id, 'starting', 'not_connected')\n\n            # Step 2: Start Algo\n            result = self.broker_automation.step_2_start_algo(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker started successfully for setup: {setup_id}\")\n\n                # Update status to running\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Broker started successfully\",\n                    \"data\": result,\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"not_connected\"\n                }\n            else:\n                logger.error(f\"❌ Failed to start broker for setup: {setup_id}\")\n\n                # Update status to failed\n                self.broker_automation.update_setup_status(setup_id, 'failed', 'not_connected')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to start broker'),\n                    \"setup_status\": \"failed\",\n                    \"connection_status\": \"not_connected\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error starting broker for setup {setup_id}: {e}\")\n\n            # Update status to error\n            self.broker_automation.update_setup_status(setup_id, 'error', 'not_connected')\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to start broker: {str(e)}\",\n                \"setup_status\": \"error\",\n                \"connection_status\": \"not_connected\"\n            }\n\n    def disconnect_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Disconnect broker and logout from OpenAlgo\"\"\"\n        try:\n            logger.info(f\"🔌 Disconnecting broker and logging out for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            # Real logout from OpenAlgo with proper auth token revocation\n            http_port = setup_data.get('http_port')\n            base_url = f\"http://127.0.0.1:{http_port}\"\n\n            try:\n                import requests\n                import sqlite3\n\n                # Step 1: Call logout endpoints\n                logout_response = requests.post(f\"{base_url}/logout\", timeout=10)\n                logger.info(f\"🔌 Logout response: {logout_response.status_code}\")\n\n                # Step 2: Also try broker logout if available\n                try:\n                    broker_logout = requests.post(f\"{base_url}/auth/logout\", timeout=5)\n                    logger.info(f\"🏦 Broker logout response: {broker_logout.status_code}\")\n                except:\n                    pass  # Broker logout might not exist\n\n                # Step 3: CRITICAL - Directly revoke auth tokens in OpenAlgo database\n                instance_name = f\"{setup_data['username']}-algo-{setup_data['broker_name']}-{setup_id}\"\n                openalgo_db_path = f\"/home/<USER>/algofactory/algo/instances/{instance_name}/db/openalgo.db\"\n\n                try:\n                    with sqlite3.connect(openalgo_db_path) as conn:\n                        cursor = conn.cursor()\n\n                        # Set is_revoked = 1 for all auth tokens\n                        cursor.execute(\"UPDATE auth SET is_revoked = 1\")\n\n                        # Clear auth tokens for extra security\n                        cursor.execute(\"UPDATE auth SET auth = '', feed_token = ''\")\n\n                        conn.commit()\n                        logger.info(f\"✅ Auth tokens revoked in OpenAlgo database\")\n\n                        # Verify revocation\n                        cursor.execute(\"SELECT name, is_revoked FROM auth\")\n                        auth_status = cursor.fetchall()\n                        logger.info(f\"🔍 Auth status after revocation: {auth_status}\")\n\n                except Exception as db_error:\n                    logger.error(f\"❌ Failed to revoke auth tokens in database: {db_error}\")\n\n            except Exception as logout_error:\n                logger.warning(f\"⚠️ Logout API call failed (continuing): {logout_error}\")\n\n            # Update status to disconnected (keep algo running)\n            self.broker_automation.update_setup_status(setup_id, 'running', 'disconnected')\n\n            return {\n                \"success\": True,\n                \"message\": \"✅ Broker disconnected and logged out successfully. Algo service still running.\",\n                \"setup_status\": \"running\",\n                \"connection_status\": \"disconnected\",\n                \"logout_performed\": True,\n                \"data\": {\n                    \"action\": \"logout\",\n                    \"algo_status\": \"running\",\n                    \"broker_status\": \"disconnected\",\n                    \"auth_tokens_revoked\": True\n                }\n            }\n\n        except Exception as e:\n            logger.error(f\"❌ Error disconnecting broker for setup {setup_id}: {e}\")\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to disconnect broker: {str(e)}\"\n            }\n\n    def stop_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Stop and disable broker instance\"\"\"\n        try:\n            logger.info(f\"⏹️ Stopping and disabling broker for setup: {setup_id}\")\n\n            # Update status to stopping\n            self.broker_automation.update_setup_status(setup_id, 'stopping', 'not_connected')\n\n            result = self.broker_automation.stop_algo(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker stopped and disabled successfully for setup: {setup_id}\")\n\n                # Update status to stopped\n                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Broker stopped and disabled successfully\",\n                    \"data\": result,\n                    \"setup_status\": \"stopped\",\n                    \"connection_status\": \"not_connected\"\n                }\n            else:\n                logger.error(f\"❌ Failed to stop broker for setup: {setup_id}\")\n\n                # Update status to error\n                self.broker_automation.update_setup_status(setup_id, 'error', 'not_connected')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to stop broker'),\n                    \"setup_status\": \"error\",\n                    \"connection_status\": \"not_connected\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error stopping broker for setup {setup_id}: {e}\")\n\n            # Update status to error\n            self.broker_automation.update_setup_status(setup_id, 'error', 'not_connected')\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to stop broker: {str(e)}\",\n                \"setup_status\": \"error\",\n                \"connection_status\": \"not_connected\"\n            }\n\n    # REMOVED: connect_broker_auto() - Old version, keeping the newer ULTRA FAST version below\n\n    def get_broker_logs(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Get broker logs\"\"\"\n        try:\n            logger.info(f\"📄 Getting logs for setup: {setup_id}\")\n\n            result = self.broker_automation.get_logs(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Logs retrieved successfully for setup: {setup_id}\")\n                return {\n                    \"success\": True,\n                    \"message\": \"Logs retrieved successfully\",\n                    \"logs\": result.get('logs', 'No logs available')\n                }\n            else:\n                logger.error(f\"❌ Failed to get logs for setup: {setup_id}\")\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to get logs')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error getting logs for setup {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to get logs: {str(e)}\"\n            }\n\n    def delete_broker_setup(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Fast delete broker setup completely\"\"\"\n        try:\n            logger.info(f\"🗑️ Fast deleting broker setup: {setup_id}\")\n\n            # Get setup data to find service name and paths\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            service_name = setup_data.get('instance_name', f\"algo-{setup_data.get('username')}-{setup_data.get('broker_name')}-{setup_id}\")\n\n            # Step 1: Stop and disable service\n            import subprocess\n            try:\n                subprocess.run(['sudo', 'systemctl', 'stop', service_name], capture_output=True, text=True, timeout=10)\n                subprocess.run(['sudo', 'systemctl', 'disable', service_name], capture_output=True, text=True, timeout=10)\n\n                # Remove service file\n                service_file = f\"/etc/systemd/system/{service_name}.service\"\n                subprocess.run(['sudo', 'rm', '-f', service_file], capture_output=True, text=True, timeout=10)\n\n                # Reload systemd\n                subprocess.run(['sudo', 'systemctl', 'daemon-reload'], capture_output=True, text=True, timeout=10)\n                subprocess.run(['sudo', 'systemctl', 'reset-failed'], capture_output=True, text=True, timeout=10)\n\n            except Exception as service_error:\n                logger.warning(f\"Service cleanup failed (continuing): {service_error}\")\n\n            # Step 2: Remove instance folder\n            try:\n                instance_path = f\"/home/<USER>/algofactory/algo/instances/{service_name}\"\n                subprocess.run(['sudo', 'rm', '-rf', instance_path], capture_output=True, text=True, timeout=10)\n            except Exception as folder_error:\n                logger.warning(f\"Folder cleanup failed (continuing): {folder_error}\")\n\n            # Step 3: Remove from database\n            import sqlite3\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n                cursor.execute(\"DELETE FROM user_brokers WHERE id = ?\", (setup_id,))\n                conn.commit()\n\n            logger.info(f\"✅ Broker setup deleted successfully: {setup_id}\")\n            return {\n                \"success\": True,\n                \"message\": f\"Broker setup {service_name} deleted successfully\",\n                \"service_name\": service_name,\n                \"setup_id\": setup_id,\n                \"details\": {\n                    \"service_stopped\": True,\n                    \"service_file_removed\": True,\n                    \"folder_removed\": True,\n                    \"database_removed\": True\n                }\n            }\n\n        except Exception as e:\n            logger.error(f\"❌ Error deleting broker setup {setup_id}: {e}\")\n            return {\n                \"success\": True,  # Still return success if most cleanup worked\n                \"message\": f\"Broker setup deleted with some warnings: {str(e)}\",\n                \"setup_id\": setup_id,\n                \"warning\": str(e)\n            }\n\n    def start_broker_automation(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Start full broker automation\"\"\"\n        try:\n            logger.info(f\"🚀 Starting broker automation for setup: {setup_id}\")\n            \n            result = self.broker_automation.run_full_automation(setup_id)\n            \n            if result.get('success'):\n                logger.info(f\"✅ Broker automation completed successfully: {setup_id}\")\n            else:\n                logger.error(f\"❌ Broker automation failed: {result.get('message')}\")\n            \n            return result\n                \n        except Exception as e:\n            logger.error(f\"❌ Error starting broker automation {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to start broker automation: {str(e)}\"\n            }\n    \n    def get_supported_brokers(self) -> List[Dict[str, Any]]:\n        \"\"\"Get list of supported brokers\"\"\"\n        try:\n            logger.info(\"📋 Getting supported brokers...\")\n            \n            brokers = [\n                {\n                    \"name\": \"angel\",\n                    \"display_name\": \"Angel One\",\n                    \"status\": \"active\",\n                    \"description\": \"Angel One broker integration\"\n                },\n                {\n                    \"name\": \"dhan\",\n                    \"display_name\": \"Dhan\",\n                    \"status\": \"active\",\n                    \"description\": \"Dhan broker integration\"\n                },\n                {\n                    \"name\": \"zerodha\",\n                    \"display_name\": \"Zerodha\",\n                    \"status\": \"coming_soon\",\n                    \"description\": \"Zerodha broker integration (coming soon)\"\n                },\n                {\n                    \"name\": \"upstox\",\n                    \"display_name\": \"Upstox\",\n                    \"status\": \"coming_soon\",\n                    \"description\": \"Upstox broker integration (coming soon)\"\n                }\n            ]\n            \n            logger.info(f\"✅ Retrieved {len(brokers)} supported brokers\")\n            return brokers\n            \n        except Exception as e:\n            logger.error(f\"❌ Error getting supported brokers: {e}\")\n            raise Exception(f\"Failed to get supported brokers: {str(e)}\")\n    \n    def delete_broker_setup(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Delete a broker setup\"\"\"\n        try:\n            logger.info(f\"🗑️ Deleting broker setup: {setup_id}\")\n            \n            result = self.broker_automation.delete_setup(setup_id)\n\n            # Fix: broker_automation returns 'status' not 'success'\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker setup deleted successfully: {setup_id}\")\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', f\"Broker setup {setup_id} deleted successfully\")\n                }\n            else:\n                logger.error(f\"❌ Failed to delete broker setup: {result.get('message')}\")\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to delete broker setup')\n                }\n                \n        except Exception as e:\n            logger.error(f\"❌ Error deleting broker setup {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to delete broker setup: {str(e)}\"\n            }\n\n    def fast_start_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Fast start broker using simple systemd commands\"\"\"\n        try:\n            logger.info(f\"⚡ Fast starting broker for setup: {setup_id}\")\n\n            # Get setup data to find service name\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            service_name = setup_data.get('instance_name', f\"{setup_data.get('username')}-algo-{setup_data.get('broker_name')}-{setup_id}\")\n\n            # Check if systemd service exists, if not create it\n            import subprocess\n            import os\n\n            service_file = f\"/etc/systemd/system/{service_name}.service\"\n            if not os.path.exists(service_file):\n                logger.info(f\"🔧 Service file not found, creating: {service_name}\")\n\n                # Create systemd service\n                instance_path = f\"/home/<USER>/algofactory/algo/instances/{service_name}\"\n                service_result = self.broker_automation.create_systemd_service(setup_id, instance_path, setup_data)\n\n                if not service_result:\n                    return {\n                        \"success\": False,\n                        \"message\": \"Failed to create systemd service\"\n                    }\n\n            # Use simple systemd start\n            result = self.broker_automation.start_service(service_name)\n\n            if result.get('status') == 'success':\n                # Update status in database\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Broker started successfully\",\n                    \"service_name\": service_name,\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"not_connected\"\n                }\n            else:\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to start service')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error fast starting broker {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to start broker: {str(e)}\"\n            }\n\n    def fast_stop_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Fast stop broker using simple systemd commands\"\"\"\n        try:\n            logger.info(f\"⚡ Fast stopping broker for setup: {setup_id}\")\n\n            # Get setup data to find service name\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            service_name = setup_data.get('instance_name', f\"algo-{setup_data.get('username')}-{setup_data.get('broker_name')}-{setup_id}\")\n\n            # Use simple systemd stop\n            result = self.broker_automation.stop_service(service_name)\n\n            if result.get('status') == 'success':\n                # Update status in database\n                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Broker stopped successfully\",\n                    \"service_name\": service_name,\n                    \"setup_status\": \"stopped\",\n                    \"connection_status\": \"not_connected\"\n                }\n            else:\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to stop service')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error fast stopping broker {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to stop broker: {str(e)}\"\n            }\n\n    def sync_broker_status(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Sync broker status with actual systemd service status\"\"\"\n        try:\n            logger.info(f\"🔄 Syncing broker status for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"success\": False, \"message\": \"Setup not found\"}\n\n            service_name = setup_data.get('instance_name', f\"{setup_data.get('username')}-algo-{setup_data.get('broker_name')}-{setup_id}\")\n\n            # Check actual systemd service status\n            import subprocess\n            try:\n                result = subprocess.run(['sudo', 'systemctl', 'is-active', service_name],\n                                      capture_output=True, text=True, timeout=5)\n\n                is_active = result.stdout.strip() == 'active'\n\n                # Update database with real status (preserve existing connection_status)\n                current_connection_status = self.broker_automation.get_setup_connection_status(setup_id)\n                if is_active:\n                    # Only update setup_status, preserve connection_status\n                    self.broker_automation.update_setup_status(setup_id, 'running', current_connection_status or 'not_connected')\n                    actual_status = 'running'\n                else:\n                    # If service is stopped, connection must be not_connected\n                    self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n                    actual_status = 'stopped'\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Status synced successfully\",\n                    \"service_name\": service_name,\n                    \"actual_status\": actual_status,\n                    \"is_active\": is_active\n                }\n\n            except subprocess.TimeoutExpired:\n                return {\"success\": False, \"message\": \"Timeout checking service status\"}\n\n        except Exception as e:\n            logger.error(f\"❌ Error syncing broker status {setup_id}: {e}\")\n            return {\"success\": False, \"message\": f\"Failed to sync status: {str(e)}\"}\n\n    def sync_broker_status(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Sync broker status with actual systemd service status\"\"\"\n        try:\n            logger.info(f\"🔄 Syncing broker status for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            service_name = setup_data.get('instance_name', f\"{setup_data.get('username')}-algo-{setup_data.get('broker_name')}-{setup_id}\")\n\n            # Check actual systemd service status\n            import subprocess\n            try:\n                result = subprocess.run([\n                    'sudo', 'systemctl', 'is-active', service_name\n                ], capture_output=True, text=True, timeout=5)\n\n                is_active = result.stdout.strip() == 'active'\n\n                # Update database with real status (preserve existing connection_status)\n                current_connection_status = self.broker_automation.get_setup_connection_status(setup_id)\n                if is_active:\n                    # Only update setup_status, preserve connection_status\n                    self.broker_automation.update_setup_status(setup_id, 'running', current_connection_status or 'not_connected')\n                    actual_status = 'running'\n                else:\n                    # If service is stopped, connection must be not_connected\n                    self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n                    actual_status = 'stopped'\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Status synced successfully\",\n                    \"service_name\": service_name,\n                    \"actual_status\": actual_status,\n                    \"is_active\": is_active\n                }\n\n            except subprocess.TimeoutExpired:\n                return {\n                    \"success\": False,\n                    \"message\": \"Timeout checking service status\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error syncing broker status {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to sync status: {str(e)}\"\n            }\n\n    def start_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Fast start broker instance - just start systemd service\"\"\"\n        try:\n            logger.info(f\"🚀 Fast starting broker for setup: {setup_id}\")\n\n            # Get setup data to determine service name\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"success\": False, \"message\": \"Setup not found\"}\n\n            # Calculate service name\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            service_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n\n            logger.info(f\"🔄 Starting service: {service_name}\")\n\n            # Check if systemd service exists first\n            check_result = self.broker_automation.run_systemctl('status', service_name)\n\n            if \"could not be found\" in check_result.stderr.lower() or \"not found\" in check_result.stderr.lower():\n                # Service doesn't exist - need to create it first\n                logger.info(f\"🔧 Service not found, creating systemd service: {service_name}\")\n\n                # Get instance path\n                instance_path = f\"/home/<USER>/algofactory/algo/instances/{service_name}\"\n\n                # Create systemd service\n                service_created = self.broker_automation.create_systemd_service(setup_id, instance_path, setup_data)\n\n                if not service_created:\n                    result = {\n                        \"status\": \"error\",\n                        \"message\": \"Failed to create systemd service\"\n                    }\n                else:\n                    logger.info(f\"✅ Systemd service created successfully: {service_name}\")\n\n                    # Now start the service\n                    start_result = self.broker_automation.run_systemctl('start', service_name)\n\n                    if start_result.returncode == 0:\n                        port = 5000 + setup_id\n                        result = {\n                            \"status\": \"success\",\n                            \"message\": f\"Service {service_name} created and started successfully\",\n                            \"service_name\": service_name,\n                            \"port\": port,\n                            \"service_created\": True\n                        }\n                    else:\n                        result = {\n                            \"status\": \"error\",\n                            \"message\": f\"Service created but failed to start: {start_result.stderr}\"\n                        }\n            else:\n                # Service exists - just start it (ULTRA-FAST!)\n                logger.info(f\"⚡ ULTRA-FAST START: Service exists, just starting: {service_name}\")\n                start_result = self.broker_automation.run_systemctl('start', service_name)\n\n                if start_result.returncode == 0:\n                    port = 5000 + setup_id\n                    result = {\n                        \"status\": \"success\",\n                        \"message\": f\"Service {service_name} started successfully (ultra-fast)\",\n                        \"service_name\": service_name,\n                        \"port\": port,\n                        \"ultra_fast_start\": True\n                    }\n                else:\n                    result = {\n                        \"status\": \"error\",\n                        \"message\": f\"Failed to start service: {start_result.stderr}\"\n                    }\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker started successfully for setup: {setup_id}\")\n\n                # Update status to running\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                # START BUTTON ONLY STARTS SERVICE - NO AUTO REGISTER\n                # User can manually register later if needed\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker started successfully'),\n                    \"data\": result\n                }\n            else:\n                # Update status back to stopped on failure\n                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to start broker')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error starting broker {setup_id}: {e}\")\n            # Update status back to stopped on error\n            self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to start broker: {str(e)}\"\n            }\n\n    def stop_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Stop broker instance with proper status tracking\"\"\"\n        try:\n            logger.info(f\"🛑 Stopping broker for setup: {setup_id}\")\n\n            # Update status to stopping\n            self.broker_automation.update_setup_status(setup_id, 'stopping', 'not_connected')\n\n            result = self.broker_automation.stop_algo(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker stopped successfully for setup: {setup_id}\")\n\n                # Update status to stopped\n                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker stopped successfully'),\n                    \"data\": result\n                }\n            else:\n                # Update status back to running on failure\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to stop broker')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error stopping broker {setup_id}: {e}\")\n            # Update status back to running on error\n            self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to stop broker: {str(e)}\"\n            }\n\n    def connect_broker_auto(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"ULTRA FAST auto connect broker - optimized for speed\"\"\"\n        try:\n            logger.info(f\"⚡ Ultra fast auto connecting broker for setup: {setup_id}\")\n\n            # Update status to connecting\n            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')\n\n            # Use ultra fast connect function\n            result = self.broker_automation.ultra_fast_connect_broker(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker connected ultra-fast for setup: {setup_id}\")\n\n                # Update status to connected\n                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker connected ultra-fast'),\n                    \"data\": result\n                }\n            else:\n                # Update status back to not_connected on failure\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to connect broker')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error auto connecting broker {setup_id}: {e}\")\n            # Update status back to not_connected on error\n            self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to connect broker: {str(e)}\"\n            }\n\n    def auto_connect_broker_with_automation(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Auto connect broker using the automation system (step_4_connect_broker)\"\"\"\n        try:\n            logger.info(f\"🤖 Auto connecting broker with automation for setup: {setup_id}\")\n\n            # Update status to connecting\n            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')\n\n            # Use the automation system's step_4_connect_broker method\n            result = self.broker_automation.step_4_connect_broker(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker auto-connected with automation for setup: {setup_id}\")\n\n                # Update status to connected\n                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker connected successfully'),\n                    \"data\": result.get('data', {})\n                }\n            else:\n                logger.error(f\"❌ Failed to auto-connect broker with automation: {result.get('message')}\")\n\n                # Update status back to not connected\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to connect broker with automation')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error in auto broker connection with automation: {e}\")\n\n            # Update status back to not connected on error\n            self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to auto-connect broker with automation: {str(e)}\"\n            }\n\n    def get_broker_oauth_url(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Get OAuth URL and credentials for manual broker connection\"\"\"\n        try:\n            logger.info(f\"🔗 Getting OAuth URL for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            broker_name = setup_data.get('broker_name', '').lower()\n\n            if broker_name == 'flattrade':\n                # Generate Flattrade OAuth URL\n                api_key = setup_data.get('broker_api_key', '').split(':::')[1] if ':::' in setup_data.get('broker_api_key', '') else setup_data.get('broker_api_key', '')\n                oauth_url = f\"https://auth.flattrade.in/?app_key={api_key}\"\n\n                return {\n                    \"success\": True,\n                    \"message\": \"OAuth URL generated successfully\",\n                    \"data\": {\n                        \"oauth_url\": oauth_url,\n                        \"broker_name\": broker_name,\n                        \"credentials\": {\n                            \"client_code\": setup_data.get('broker_client_id'),\n                            \"password\": setup_data.get('trading_pin'),\n                            \"dob\": setup_data.get('totp_secret')\n                        }\n                    }\n                }\n            else:\n                return {\n                    \"success\": False,\n                    \"message\": f\"OAuth URL not supported for broker: {broker_name}\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error getting OAuth URL: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to get OAuth URL: {str(e)}\"\n            }\n\n    # REMOVED: connect_broker_manual() - Duplicate method #2, use run_complete_manual_setup() instead\n\n    def connect_broker_manual_otp(self, setup_id: int, manual_otp: str) -> Dict[str, Any]:\n        \"\"\"Manual broker connection with OTP only (uses saved credentials)\"\"\"\n        try:\n            logger.info(f\"🔗 Manual OTP connect for setup: {setup_id}\")\n\n            # Update status to connecting\n            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')\n\n            # Get saved setup data (includes all credentials)\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\",\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"error\"\n                }\n\n            # Validate that we have saved credentials\n            required_fields = ['broker_client_id', 'broker_api_key', 'broker_api_secret', 'trading_pin']\n            missing_fields = [field for field in required_fields if not setup_data.get(field)]\n\n            if missing_fields:\n                return {\n                    \"success\": False,\n                    \"message\": f\"Missing saved credentials: {', '.join(missing_fields)}. Please update broker setup.\",\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"error\"\n                }\n\n            # Use manual OTP with saved credentials\n            result = self.broker_automation.connect_with_manual_otp(setup_id, manual_otp)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Manual OTP connection successful for setup: {setup_id}\")\n\n                # Update status to connected\n                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker connected with manual OTP'),\n                    \"data\": result\n                }\n            else:\n                logger.error(f\"❌ Manual OTP connection failed for setup: {setup_id}\")\n\n                # Update status to error\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Manual OTP connection failed'),\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"not_connected\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error in manual OTP connection for setup {setup_id}: {e}\")\n\n            # Update status to error\n            try:\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n            except:\n                pass\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to connect with manual OTP: {str(e)}\",\n                \"setup_status\": \"running\",\n                \"connection_status\": \"not_connected\"\n            }\n\n    def run_complete_manual_setup(self, setup_id: int, manual_otp: str) -> Dict[str, Any]:\n        \"\"\"Run complete manual setup for new instances (register + login + broker auth)\"\"\"\n        try:\n            logger.info(f\"🔄 Complete manual setup for setup: {setup_id}\")\n\n            # Update status to connecting\n            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')\n\n            # Run complete manual setup flow\n            result = self.broker_automation.run_complete_manual_setup(setup_id, manual_otp)\n\n            if result.get('status') == 'success':\n                # Update final status\n                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Complete setup successful'),\n                    \"data\": result.get('data', {})\n                }\n            else:\n                # Update status back to not_connected on failure\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Complete setup failed')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Complete manual setup error: {e}\")\n\n            # Update status back to not_connected on error\n            try:\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n            except:\n                pass\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to complete manual setup: {str(e)}\"\n            }\n\n    def connect_broker_manual_openalgo_style(self, setup_id: int, manual_credentials: Dict[str, Any] = None) -> Dict[str, Any]:\n        \"\"\"Manual broker connection using OpenAlgo instance approach\"\"\"\n        try:\n            logger.info(f\"🔗 Manual OpenAlgo-style broker connection for setup: {setup_id}\")\n            logger.info(f\"📋 Manual credentials provided: {manual_credentials is not None}\")\n\n            # Update status to connecting\n            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')\n\n            # Use the new manual connection method\n            result = self.broker_automation.connect_broker_manual_openalgo_style(setup_id, manual_credentials)\n            logger.info(f\"📡 Broker automation result: {result}\")\n\n            if result.get('status') == 'success':\n                # Update status to connected\n                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker connected successfully using OpenAlgo style'),\n                    \"data\": result.get('data', {}),\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"connected\"\n                }\n            else:\n                # Update status to error\n                self.broker_automation.update_setup_status(setup_id, 'running', 'error')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to connect broker'),\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"error\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error manual OpenAlgo-style connecting broker {setup_id}: {e}\")\n            # Update status to error\n            self.broker_automation.update_setup_status(setup_id, 'running', 'error')\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to connect broker: {str(e)}\",\n                \"setup_status\": \"running\",\n                \"connection_status\": \"error\"\n            }\n\n\n", "modifiedCode": "\"\"\"\nBroker Service for AlgoFactory API\nBusiness logic for broker-related operations\n\"\"\"\n\nimport sys\nimport logging\nfrom typing import List, Optional, Dict, Any\n\n# Setup logging\nlogger = logging.getLogger(__name__)\n\nclass BrokerService:\n    \"\"\"Service class for broker operations\"\"\"\n    \n    def __init__(self):\n        \"\"\"Initialize broker service\"\"\"\n        # Import broker automation\n        sys.path.append('/home/<USER>/algofactory/scripts/automation')\n        from broker_automation import BrokerAutomation\n        self.broker_automation = BrokerAutomation()\n        logger.info(\"✅ Broker service initialized with automation\")\n    \n    def get_all_broker_setups(self) -> List[Dict[str, Any]]:\n        \"\"\"Get all broker setups from database\"\"\"\n        try:\n            logger.info(\"📋 Getting all broker setups...\")\n            setups = self.broker_automation.get_all_broker_setups()\n\n            logger.info(f\"✅ Retrieved {len(setups)} broker setups\")\n            return setups\n\n        except Exception as e:\n            logger.error(f\"❌ Error getting broker setups: {e}\")\n            raise Exception(f\"Failed to get broker setups: {str(e)}\")\n    \n    def create_broker_setup(self, setup_data: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"Create a new broker setup using the multi-tenant API approach\"\"\"\n        try:\n            logger.info(f\"🆕 Creating new broker setup for user: {setup_data.get('username')}\")\n\n            # Import required modules\n            import sqlite3\n            import json\n            from datetime import datetime, timezone, timedelta\n\n            # Database path\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            # Get IST timestamp\n            ist = timezone(timedelta(hours=5, minutes=30))\n            created_at_ist = datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S')\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n\n                # Get setup data\n                username = setup_data.get('username')\n                broker_name = setup_data.get('broker_name')\n                admin_id = setup_data.get('admin_id', 'Admin_1')\n                creator_name = setup_data.get('created_by', 'superadmin')\n\n                # Validate username format\n                if not username or not username.isdigit():\n                    return {\n                        \"success\": False,\n                        \"message\": f\"Invalid username format: {username}. Should be numeric like '1001'\"\n                    }\n\n                # Check if user exists\n                cursor.execute(\"SELECT username FROM users WHERE username = ?\", (username,))\n                if not cursor.fetchone():\n                    return {\n                        \"success\": False,\n                        \"message\": f\"User {username} not found\"\n                    }\n\n                # Insert broker setup into user_brokers table first to get the ID\n                cursor.execute(\"\"\"\n                    INSERT INTO user_brokers (\n                        username, broker_name, setup_name, instance_number,\n                        instance_name, broker_client_id, broker_api_key,\n                        broker_api_secret, broker_api_key_market, trading_pin,\n                        totp_secret, setup_status, connection_status, additional_config\n                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'not_connected', ?)\n                \"\"\", (\n                    username,\n                    broker_name,\n                    setup_data.get('setup_name'),\n                    1,  # temporary instance number\n                    'temp',  # temporary instance name\n                    setup_data.get('broker_client_id'),\n                    setup_data.get('broker_api_key'),\n                    setup_data.get('broker_api_secret'),\n                    setup_data.get('broker_api_key_market', ''),\n                    setup_data.get('trading_pin'),\n                    setup_data.get('totp_secret'),\n                    json.dumps({\n                        \"creator\": creator_name,\n                        \"created_at_ist\": created_at_ist,\n                        \"created_for\": username\n                    })\n                ))\n\n                setup_id = cursor.lastrowid\n\n                # Now update with correct instance name using the actual setup_id\n                correct_instance_name = f\"{username}-algo-{broker_name}-{setup_id}\"\n                cursor.execute(\"\"\"\n                    UPDATE user_brokers\n                    SET instance_name = ?, instance_number = ?\n                    WHERE id = ?\n                \"\"\", (correct_instance_name, setup_id, setup_id))\n\n                # Update with final instance info using setup_id\n                final_instance_name = f\"{username}-algo-{broker_name}-{setup_id}\"\n\n                cursor.execute(\"\"\"\n                    UPDATE user_brokers\n                    SET instance_number = ?, instance_name = ?\n                    WHERE id = ?\n                \"\"\", (setup_id, final_instance_name, setup_id))\n\n                conn.commit()\n\n                logger.info(f\"✅ Broker setup created successfully with ID: {setup_id}\")\n\n                # Try to run automation\n                try:\n                    logger.info(f\"🚀 Starting broker automation for setup_id: {setup_id}\")\n\n                    # STEP 1: SET_ENV - Create instance folder, clone OpenAlgo, configure .env\n                    automation_result = self.broker_automation.step_1_set_env(setup_id)\n\n                    if automation_result['status'] == 'success':\n                        # Update status to automated\n                        cursor.execute(\"\"\"\n                            UPDATE user_brokers\n                            SET setup_status = 'env_configured', detailed_status = ?\n                            WHERE id = ?\n                        \"\"\", (json.dumps(automation_result), setup_id))\n                        conn.commit()\n\n                        return {\n                            \"success\": True,\n                            \"message\": \"Broker setup created and environment configured successfully\",\n                            \"setup_id\": setup_id,\n                            \"automation_status\": \"env_configured\",\n                            \"instance_path\": automation_result.get('data', {}).get('instance_path'),\n                            \"algo_url\": automation_result.get('data', {}).get('algo_url') or f\"http://127.0.0.1:{5000 + setup_id}\",\n                            \"data\": {\n                                \"username\": username,\n                                \"broker_name\": broker_name,\n                                \"setup_name\": setup_data.get('setup_name'),\n                                \"instance_name\": final_instance_name,\n                                \"admin_id\": admin_id\n                            }\n                        }\n                    else:\n                        # Update status to failed\n                        cursor.execute(\"\"\"\n                            UPDATE user_brokers\n                            SET setup_status = 'failed', detailed_status = ?\n                            WHERE id = ?\n                        \"\"\", (json.dumps(automation_result), setup_id))\n                        conn.commit()\n\n                        return {\n                            \"success\": True,\n                            \"message\": \"Broker setup created but automation failed\",\n                            \"setup_id\": setup_id,\n                            \"automation_status\": \"failed\",\n                            \"automation_error\": automation_result.get('message'),\n                            \"data\": {\n                                \"username\": username,\n                                \"broker_name\": broker_name,\n                                \"setup_name\": setup_data.get('setup_name'),\n                                \"instance_name\": final_instance_name,\n                                \"admin_id\": admin_id\n                            }\n                        }\n\n                except Exception as automation_error:\n                    logger.error(f\"❌ Automation failed: {automation_error}\")\n\n                    # Update status to error\n                    cursor.execute(\"\"\"\n                        UPDATE user_brokers\n                        SET setup_status = 'error', detailed_status = ?\n                        WHERE id = ?\n                    \"\"\", (json.dumps({\"error\": str(automation_error)}), setup_id))\n                    conn.commit()\n\n                    return {\n                        \"success\": True,\n                        \"message\": \"Broker setup created but automation system failed\",\n                        \"setup_id\": setup_id,\n                        \"automation_status\": \"error\",\n                        \"automation_error\": str(automation_error),\n                        \"data\": {\n                            \"username\": username,\n                            \"broker_name\": broker_name,\n                            \"setup_name\": setup_data.get('setup_name'),\n                            \"instance_name\": final_instance_name,\n                            \"admin_id\": admin_id\n                        }\n                    }\n\n        except Exception as e:\n            logger.error(f\"❌ Error creating broker setup: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to create broker setup: {str(e)}\"\n            }\n\n    def create_broker_setup_from_saved(self, saved_broker_id: int, user_id: str, admin_id: str) -> Dict[str, Any]:\n        \"\"\"Create a new broker setup from saved broker configuration\"\"\"\n        try:\n            logger.info(f\"🆕 Creating broker setup from saved configuration ID: {saved_broker_id}\")\n            logger.info(f\"🔍 Debug: user_id={user_id}, admin_id={admin_id}, saved_broker_id={saved_broker_id}\")\n\n            # Import required modules\n            import sqlite3\n            from datetime import datetime, timezone, timedelta\n\n            # Database path\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            # Get IST timestamp\n            ist = timezone(timedelta(hours=5, minutes=30))\n            created_at_ist = datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S')\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n\n                # Get saved broker configuration\n                cursor.execute(\"\"\"\n                    SELECT user_id, admin_id, broker_name, broker_type, trading_setup_name,\n                           client_id, api_key, api_secret, trading_pin, totp_secret\n                    FROM saved_broker_details\n                    WHERE id = ?\n                \"\"\", (saved_broker_id,))\n\n                saved_broker = cursor.fetchone()\n                if not saved_broker:\n                    return {\n                        \"success\": False,\n                        \"message\": f\"Saved broker configuration {saved_broker_id} not found\"\n                    }\n\n                # Extract saved broker data\n                saved_user_id = saved_broker[0]\n                saved_admin_id = saved_broker[1]\n                broker_name = saved_broker[2]\n                setup_name = saved_broker[4] or f\"{broker_name}_setup\"\n                client_id = saved_broker[5]\n                api_key = saved_broker[6]\n                api_secret = saved_broker[7]\n                trading_pin = saved_broker[8]\n                totp_secret = saved_broker[9]\n\n                # Use provided user_id and admin_id, or fall back to saved ones\n                target_user_id = user_id if user_id else saved_user_id\n                target_admin_id = admin_id if admin_id else saved_admin_id\n\n                logger.info(f\"📋 Using saved broker config: {setup_name} for user {target_user_id}\")\n                logger.info(f\"🔍 Debug: saved_user_id={saved_user_id}, provided_user_id={user_id}, final_target_user_id={target_user_id}\")\n\n                # Validate username format\n                if not target_user_id or not str(target_user_id).isdigit():\n                    return {\n                        \"success\": False,\n                        \"message\": f\"Invalid username format: {target_user_id}. Should be numeric like '1001'\"\n                    }\n\n                # Check if user exists\n                cursor.execute(\"SELECT username FROM users WHERE username = ?\", (str(target_user_id),))\n                if not cursor.fetchone():\n                    return {\n                        \"success\": False,\n                        \"message\": f\"User {target_user_id} not found\"\n                    }\n\n                # Get next instance number for this user and broker\n                cursor.execute(\"\"\"\n                    SELECT COALESCE(MAX(instance_number), 0) + 1\n                    FROM user_brokers\n                    WHERE username = ? AND broker_name = ?\n                \"\"\", (str(target_user_id), broker_name))\n                instance_number = cursor.fetchone()[0]\n\n                # Insert broker setup into user_brokers table first with temporary instance name\n                cursor.execute(\"\"\"\n                    INSERT INTO user_brokers (\n                        username, broker_name, setup_name, instance_number,\n                        instance_name, broker_client_id, broker_api_key,\n                        broker_api_secret, trading_pin, totp_secret,\n                        setup_status, connection_status, http_port, created_at\n                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'stopped', 'not_connected', ?, ?)\n                \"\"\", (\n                    str(target_user_id),\n                    broker_name,\n                    setup_name,\n                    instance_number,\n                    f\"temp-{target_user_id}-{broker_name}\",  # Temporary name\n                    client_id,\n                    api_key,\n                    api_secret,\n                    trading_pin,\n                    totp_secret,\n                    5000 + int(target_user_id),  # Port based on user ID\n                    created_at_ist\n                ))\n\n                setup_id = cursor.lastrowid\n\n                # Now update with correct instance name using the actual setup_id\n                correct_instance_name = f\"{target_user_id}-algo-{broker_name}-{setup_id}\"\n                cursor.execute(\"\"\"\n                    UPDATE user_brokers\n                    SET instance_name = ?\n                    WHERE id = ?\n                \"\"\", (correct_instance_name, setup_id))\n\n                conn.commit()\n\n                logger.info(f\"✅ Broker setup created successfully with ID: {setup_id}\")\n\n                # Now run SET_ENV step to create the instance folder\n                logger.info(f\"🔧 Running SET_ENV step for setup ID: {setup_id}\")\n                env_result = self.broker_automation.step_1_set_env(setup_id)\n\n                if env_result.get('status') == 'success':\n                    logger.info(f\"✅ SET_ENV completed successfully for setup ID: {setup_id}\")\n                    final_status = \"env_configured\"\n\n                    # Update status in database\n                    cursor.execute(\"UPDATE user_brokers SET setup_status = ? WHERE id = ?\",\n                                 (final_status, setup_id))\n                    conn.commit()\n                else:\n                    logger.warning(f\"⚠️ SET_ENV failed for setup ID: {setup_id}: {env_result.get('message')}\")\n                    final_status = \"setup_failed\"\n\n                return {\n                    \"success\": True,\n                    \"message\": f\"Broker setup '{setup_name}' created and configured successfully\",\n                    \"data\": {\n                        \"setup_id\": setup_id,\n                        \"username\": target_user_id,\n                        \"broker_name\": broker_name,\n                        \"setup_name\": setup_name,\n                        \"instance_name\": correct_instance_name,\n                        \"instance_number\": instance_number,\n                        \"status\": final_status,\n                        \"connection_status\": \"not_connected\",\n                        \"saved_broker_id\": saved_broker_id,\n                        \"env_setup\": env_result.get('status') == 'success'\n                    }\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error creating broker setup from saved config: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to create broker setup: {str(e)}\"\n            }\n\n    def get_broker_setup_by_id(self, setup_id: int) -> Optional[Dict[str, Any]]:\n        \"\"\"Get a specific broker setup by ID\"\"\"\n        try:\n            logger.info(f\"🔍 Getting broker setup: {setup_id}\")\n            \n            setup = self.broker_automation.get_setup_by_id(setup_id)\n            \n            if setup:\n                logger.info(f\"✅ Broker setup found: {setup_id}\")\n                return setup\n            else:\n                logger.warning(f\"⚠️ Broker setup not found: {setup_id}\")\n                return None\n                \n        except Exception as e:\n            logger.error(f\"❌ Error getting broker setup {setup_id}: {e}\")\n            raise Exception(f\"Failed to get broker setup: {str(e)}\")\n    \n    # REMOVED: connect_broker_manual() - Old method, use run_complete_manual_setup() instead\n\n    def start_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Start broker instance with proper status tracking\"\"\"\n        try:\n            logger.info(f\"▶️ Starting broker for setup: {setup_id}\")\n\n            # Update status to starting\n            self.broker_automation.update_setup_status(setup_id, 'starting', 'not_connected')\n\n            # Step 2: Start Algo\n            result = self.broker_automation.step_2_start_algo(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker started successfully for setup: {setup_id}\")\n\n                # Update status to running\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Broker started successfully\",\n                    \"data\": result,\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"not_connected\"\n                }\n            else:\n                logger.error(f\"❌ Failed to start broker for setup: {setup_id}\")\n\n                # Update status to failed\n                self.broker_automation.update_setup_status(setup_id, 'failed', 'not_connected')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to start broker'),\n                    \"setup_status\": \"failed\",\n                    \"connection_status\": \"not_connected\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error starting broker for setup {setup_id}: {e}\")\n\n            # Update status to error\n            self.broker_automation.update_setup_status(setup_id, 'error', 'not_connected')\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to start broker: {str(e)}\",\n                \"setup_status\": \"error\",\n                \"connection_status\": \"not_connected\"\n            }\n\n    def disconnect_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Disconnect broker and logout from OpenAlgo\"\"\"\n        try:\n            logger.info(f\"🔌 Disconnecting broker and logging out for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            # Real logout from OpenAlgo with proper auth token revocation\n            http_port = setup_data.get('http_port')\n            base_url = f\"http://127.0.0.1:{http_port}\"\n\n            try:\n                import requests\n                import sqlite3\n\n                # Step 1: Call logout endpoints\n                logout_response = requests.post(f\"{base_url}/logout\", timeout=10)\n                logger.info(f\"🔌 Logout response: {logout_response.status_code}\")\n\n                # Step 2: Also try broker logout if available\n                try:\n                    broker_logout = requests.post(f\"{base_url}/auth/logout\", timeout=5)\n                    logger.info(f\"🏦 Broker logout response: {broker_logout.status_code}\")\n                except:\n                    pass  # Broker logout might not exist\n\n                # Step 3: CRITICAL - Directly revoke auth tokens in OpenAlgo database\n                instance_name = f\"{setup_data['username']}-algo-{setup_data['broker_name']}-{setup_id}\"\n                openalgo_db_path = f\"/home/<USER>/algofactory/algo/instances/{instance_name}/db/openalgo.db\"\n\n                try:\n                    with sqlite3.connect(openalgo_db_path) as conn:\n                        cursor = conn.cursor()\n\n                        # Set is_revoked = 1 for all auth tokens\n                        cursor.execute(\"UPDATE auth SET is_revoked = 1\")\n\n                        # Clear auth tokens for extra security\n                        cursor.execute(\"UPDATE auth SET auth = '', feed_token = ''\")\n\n                        conn.commit()\n                        logger.info(f\"✅ Auth tokens revoked in OpenAlgo database\")\n\n                        # Verify revocation\n                        cursor.execute(\"SELECT name, is_revoked FROM auth\")\n                        auth_status = cursor.fetchall()\n                        logger.info(f\"🔍 Auth status after revocation: {auth_status}\")\n\n                except Exception as db_error:\n                    logger.error(f\"❌ Failed to revoke auth tokens in database: {db_error}\")\n\n            except Exception as logout_error:\n                logger.warning(f\"⚠️ Logout API call failed (continuing): {logout_error}\")\n\n            # Update status to disconnected (keep algo running)\n            self.broker_automation.update_setup_status(setup_id, 'running', 'disconnected')\n\n            return {\n                \"success\": True,\n                \"message\": \"✅ Broker disconnected and logged out successfully. Algo service still running.\",\n                \"setup_status\": \"running\",\n                \"connection_status\": \"disconnected\",\n                \"logout_performed\": True,\n                \"data\": {\n                    \"action\": \"logout\",\n                    \"algo_status\": \"running\",\n                    \"broker_status\": \"disconnected\",\n                    \"auth_tokens_revoked\": True\n                }\n            }\n\n        except Exception as e:\n            logger.error(f\"❌ Error disconnecting broker for setup {setup_id}: {e}\")\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to disconnect broker: {str(e)}\"\n            }\n\n    def stop_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Stop and disable broker instance\"\"\"\n        try:\n            logger.info(f\"⏹️ Stopping and disabling broker for setup: {setup_id}\")\n\n            # Update status to stopping\n            self.broker_automation.update_setup_status(setup_id, 'stopping', 'not_connected')\n\n            result = self.broker_automation.stop_algo(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker stopped and disabled successfully for setup: {setup_id}\")\n\n                # Update status to stopped\n                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Broker stopped and disabled successfully\",\n                    \"data\": result,\n                    \"setup_status\": \"stopped\",\n                    \"connection_status\": \"not_connected\"\n                }\n            else:\n                logger.error(f\"❌ Failed to stop broker for setup: {setup_id}\")\n\n                # Update status to error\n                self.broker_automation.update_setup_status(setup_id, 'error', 'not_connected')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to stop broker'),\n                    \"setup_status\": \"error\",\n                    \"connection_status\": \"not_connected\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error stopping broker for setup {setup_id}: {e}\")\n\n            # Update status to error\n            self.broker_automation.update_setup_status(setup_id, 'error', 'not_connected')\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to stop broker: {str(e)}\",\n                \"setup_status\": \"error\",\n                \"connection_status\": \"not_connected\"\n            }\n\n    # REMOVED: connect_broker_auto() - Old version, keeping the newer ULTRA FAST version below\n\n    def get_broker_logs(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Get broker logs\"\"\"\n        try:\n            logger.info(f\"📄 Getting logs for setup: {setup_id}\")\n\n            result = self.broker_automation.get_logs(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Logs retrieved successfully for setup: {setup_id}\")\n                return {\n                    \"success\": True,\n                    \"message\": \"Logs retrieved successfully\",\n                    \"logs\": result.get('logs', 'No logs available')\n                }\n            else:\n                logger.error(f\"❌ Failed to get logs for setup: {setup_id}\")\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to get logs')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error getting logs for setup {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to get logs: {str(e)}\"\n            }\n\n    def delete_broker_setup(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Fast delete broker setup completely\"\"\"\n        try:\n            logger.info(f\"🗑️ Fast deleting broker setup: {setup_id}\")\n\n            # Get setup data to find service name and paths\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            service_name = setup_data.get('instance_name', f\"algo-{setup_data.get('username')}-{setup_data.get('broker_name')}-{setup_id}\")\n\n            # Step 1: Stop and disable service\n            import subprocess\n            try:\n                subprocess.run(['sudo', 'systemctl', 'stop', service_name], capture_output=True, text=True, timeout=10)\n                subprocess.run(['sudo', 'systemctl', 'disable', service_name], capture_output=True, text=True, timeout=10)\n\n                # Remove service file\n                service_file = f\"/etc/systemd/system/{service_name}.service\"\n                subprocess.run(['sudo', 'rm', '-f', service_file], capture_output=True, text=True, timeout=10)\n\n                # Reload systemd\n                subprocess.run(['sudo', 'systemctl', 'daemon-reload'], capture_output=True, text=True, timeout=10)\n                subprocess.run(['sudo', 'systemctl', 'reset-failed'], capture_output=True, text=True, timeout=10)\n\n            except Exception as service_error:\n                logger.warning(f\"Service cleanup failed (continuing): {service_error}\")\n\n            # Step 2: Remove instance folder\n            try:\n                instance_path = f\"/home/<USER>/algofactory/algo/instances/{service_name}\"\n                subprocess.run(['sudo', 'rm', '-rf', instance_path], capture_output=True, text=True, timeout=10)\n            except Exception as folder_error:\n                logger.warning(f\"Folder cleanup failed (continuing): {folder_error}\")\n\n            # Step 3: Remove from database\n            import sqlite3\n            db_path = \"/home/<USER>/algofactory/database/algofactory.db\"\n\n            with sqlite3.connect(db_path) as conn:\n                cursor = conn.cursor()\n                cursor.execute(\"DELETE FROM user_brokers WHERE id = ?\", (setup_id,))\n                conn.commit()\n\n            logger.info(f\"✅ Broker setup deleted successfully: {setup_id}\")\n            return {\n                \"success\": True,\n                \"message\": f\"Broker setup {service_name} deleted successfully\",\n                \"service_name\": service_name,\n                \"setup_id\": setup_id,\n                \"details\": {\n                    \"service_stopped\": True,\n                    \"service_file_removed\": True,\n                    \"folder_removed\": True,\n                    \"database_removed\": True\n                }\n            }\n\n        except Exception as e:\n            logger.error(f\"❌ Error deleting broker setup {setup_id}: {e}\")\n            return {\n                \"success\": True,  # Still return success if most cleanup worked\n                \"message\": f\"Broker setup deleted with some warnings: {str(e)}\",\n                \"setup_id\": setup_id,\n                \"warning\": str(e)\n            }\n\n    def start_broker_automation(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Start full broker automation\"\"\"\n        try:\n            logger.info(f\"🚀 Starting broker automation for setup: {setup_id}\")\n            \n            result = self.broker_automation.run_full_automation(setup_id)\n            \n            if result.get('success'):\n                logger.info(f\"✅ Broker automation completed successfully: {setup_id}\")\n            else:\n                logger.error(f\"❌ Broker automation failed: {result.get('message')}\")\n            \n            return result\n                \n        except Exception as e:\n            logger.error(f\"❌ Error starting broker automation {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to start broker automation: {str(e)}\"\n            }\n    \n    def get_supported_brokers(self) -> List[Dict[str, Any]]:\n        \"\"\"Get list of supported brokers\"\"\"\n        try:\n            logger.info(\"📋 Getting supported brokers...\")\n            \n            brokers = [\n                {\n                    \"name\": \"angel\",\n                    \"display_name\": \"Angel One\",\n                    \"status\": \"active\",\n                    \"description\": \"Angel One broker integration\"\n                },\n                {\n                    \"name\": \"dhan\",\n                    \"display_name\": \"Dhan\",\n                    \"status\": \"active\",\n                    \"description\": \"Dhan broker integration\"\n                },\n                {\n                    \"name\": \"zerodha\",\n                    \"display_name\": \"Zerodha\",\n                    \"status\": \"coming_soon\",\n                    \"description\": \"Zerodha broker integration (coming soon)\"\n                },\n                {\n                    \"name\": \"upstox\",\n                    \"display_name\": \"Upstox\",\n                    \"status\": \"coming_soon\",\n                    \"description\": \"Upstox broker integration (coming soon)\"\n                }\n            ]\n            \n            logger.info(f\"✅ Retrieved {len(brokers)} supported brokers\")\n            return brokers\n            \n        except Exception as e:\n            logger.error(f\"❌ Error getting supported brokers: {e}\")\n            raise Exception(f\"Failed to get supported brokers: {str(e)}\")\n    \n    def delete_broker_setup(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Delete a broker setup\"\"\"\n        try:\n            logger.info(f\"🗑️ Deleting broker setup: {setup_id}\")\n            \n            result = self.broker_automation.delete_setup(setup_id)\n\n            # Fix: broker_automation returns 'status' not 'success'\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker setup deleted successfully: {setup_id}\")\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', f\"Broker setup {setup_id} deleted successfully\")\n                }\n            else:\n                logger.error(f\"❌ Failed to delete broker setup: {result.get('message')}\")\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to delete broker setup')\n                }\n                \n        except Exception as e:\n            logger.error(f\"❌ Error deleting broker setup {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to delete broker setup: {str(e)}\"\n            }\n\n    def fast_start_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Fast start broker using simple systemd commands\"\"\"\n        try:\n            logger.info(f\"⚡ Fast starting broker for setup: {setup_id}\")\n\n            # Get setup data to find service name\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            service_name = setup_data.get('instance_name', f\"{setup_data.get('username')}-algo-{setup_data.get('broker_name')}-{setup_id}\")\n\n            # Check if systemd service exists, if not create it\n            import subprocess\n            import os\n\n            service_file = f\"/etc/systemd/system/{service_name}.service\"\n            if not os.path.exists(service_file):\n                logger.info(f\"🔧 Service file not found, creating: {service_name}\")\n\n                # Create systemd service\n                instance_path = f\"/home/<USER>/algofactory/algo/instances/{service_name}\"\n                service_result = self.broker_automation.create_systemd_service(setup_id, instance_path, setup_data)\n\n                if not service_result:\n                    return {\n                        \"success\": False,\n                        \"message\": \"Failed to create systemd service\"\n                    }\n\n            # Use simple systemd start\n            result = self.broker_automation.start_service(service_name)\n\n            if result.get('status') == 'success':\n                # Update status in database\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Broker started successfully\",\n                    \"service_name\": service_name,\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"not_connected\"\n                }\n            else:\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to start service')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error fast starting broker {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to start broker: {str(e)}\"\n            }\n\n    def fast_stop_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Fast stop broker using simple systemd commands\"\"\"\n        try:\n            logger.info(f\"⚡ Fast stopping broker for setup: {setup_id}\")\n\n            # Get setup data to find service name\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            service_name = setup_data.get('instance_name', f\"algo-{setup_data.get('username')}-{setup_data.get('broker_name')}-{setup_id}\")\n\n            # Use simple systemd stop\n            result = self.broker_automation.stop_service(service_name)\n\n            if result.get('status') == 'success':\n                # Update status in database\n                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Broker stopped successfully\",\n                    \"service_name\": service_name,\n                    \"setup_status\": \"stopped\",\n                    \"connection_status\": \"not_connected\"\n                }\n            else:\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to stop service')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error fast stopping broker {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to stop broker: {str(e)}\"\n            }\n\n    def sync_broker_status(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Sync broker status with actual systemd service status\"\"\"\n        try:\n            logger.info(f\"🔄 Syncing broker status for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"success\": False, \"message\": \"Setup not found\"}\n\n            service_name = setup_data.get('instance_name', f\"{setup_data.get('username')}-algo-{setup_data.get('broker_name')}-{setup_id}\")\n\n            # Check actual systemd service status\n            import subprocess\n            try:\n                result = subprocess.run(['sudo', 'systemctl', 'is-active', service_name],\n                                      capture_output=True, text=True, timeout=5)\n\n                is_active = result.stdout.strip() == 'active'\n\n                # Update database with real status (preserve existing connection_status)\n                current_connection_status = self.broker_automation.get_setup_connection_status(setup_id)\n                if is_active:\n                    # Only update setup_status, preserve connection_status\n                    self.broker_automation.update_setup_status(setup_id, 'running', current_connection_status or 'not_connected')\n                    actual_status = 'running'\n                else:\n                    # If service is stopped, connection must be not_connected\n                    self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n                    actual_status = 'stopped'\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Status synced successfully\",\n                    \"service_name\": service_name,\n                    \"actual_status\": actual_status,\n                    \"is_active\": is_active\n                }\n\n            except subprocess.TimeoutExpired:\n                return {\"success\": False, \"message\": \"Timeout checking service status\"}\n\n        except Exception as e:\n            logger.error(f\"❌ Error syncing broker status {setup_id}: {e}\")\n            return {\"success\": False, \"message\": f\"Failed to sync status: {str(e)}\"}\n\n    def sync_broker_status(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Sync broker status with actual systemd service status\"\"\"\n        try:\n            logger.info(f\"🔄 Syncing broker status for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            service_name = setup_data.get('instance_name', f\"{setup_data.get('username')}-algo-{setup_data.get('broker_name')}-{setup_id}\")\n\n            # Check actual systemd service status\n            import subprocess\n            try:\n                result = subprocess.run([\n                    'sudo', 'systemctl', 'is-active', service_name\n                ], capture_output=True, text=True, timeout=5)\n\n                is_active = result.stdout.strip() == 'active'\n\n                # Update database with real status (preserve existing connection_status)\n                current_connection_status = self.broker_automation.get_setup_connection_status(setup_id)\n                if is_active:\n                    # Only update setup_status, preserve connection_status\n                    self.broker_automation.update_setup_status(setup_id, 'running', current_connection_status or 'not_connected')\n                    actual_status = 'running'\n                else:\n                    # If service is stopped, connection must be not_connected\n                    self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n                    actual_status = 'stopped'\n\n                return {\n                    \"success\": True,\n                    \"message\": \"Status synced successfully\",\n                    \"service_name\": service_name,\n                    \"actual_status\": actual_status,\n                    \"is_active\": is_active\n                }\n\n            except subprocess.TimeoutExpired:\n                return {\n                    \"success\": False,\n                    \"message\": \"Timeout checking service status\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error syncing broker status {setup_id}: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to sync status: {str(e)}\"\n            }\n\n    def start_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Fast start broker instance - just start systemd service\"\"\"\n        try:\n            logger.info(f\"🚀 Fast starting broker for setup: {setup_id}\")\n\n            # Get setup data to determine service name\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\"success\": False, \"message\": \"Setup not found\"}\n\n            # Calculate service name\n            username = setup_data.get('user_username', setup_data.get('username', '1000'))\n            service_name = f\"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}\"\n\n            logger.info(f\"🔄 Starting service: {service_name}\")\n\n            # Check if systemd service exists first\n            check_result = self.broker_automation.run_systemctl('status', service_name)\n\n            if \"could not be found\" in check_result.stderr.lower() or \"not found\" in check_result.stderr.lower():\n                # Service doesn't exist - need to create it first\n                logger.info(f\"🔧 Service not found, creating systemd service: {service_name}\")\n\n                # Get instance path\n                instance_path = f\"/home/<USER>/algofactory/algo/instances/{service_name}\"\n\n                # Create systemd service\n                service_created = self.broker_automation.create_systemd_service(setup_id, instance_path, setup_data)\n\n                if not service_created:\n                    result = {\n                        \"status\": \"error\",\n                        \"message\": \"Failed to create systemd service\"\n                    }\n                else:\n                    logger.info(f\"✅ Systemd service created successfully: {service_name}\")\n\n                    # Now start the service\n                    start_result = self.broker_automation.run_systemctl('start', service_name)\n\n                    if start_result.returncode == 0:\n                        port = 5000 + setup_id\n                        result = {\n                            \"status\": \"success\",\n                            \"message\": f\"Service {service_name} created and started successfully\",\n                            \"service_name\": service_name,\n                            \"port\": port,\n                            \"service_created\": True\n                        }\n                    else:\n                        result = {\n                            \"status\": \"error\",\n                            \"message\": f\"Service created but failed to start: {start_result.stderr}\"\n                        }\n            else:\n                # Service exists - just start it (ULTRA-FAST!)\n                logger.info(f\"⚡ ULTRA-FAST START: Service exists, just starting: {service_name}\")\n                start_result = self.broker_automation.run_systemctl('start', service_name)\n\n                if start_result.returncode == 0:\n                    port = 5000 + setup_id\n                    result = {\n                        \"status\": \"success\",\n                        \"message\": f\"Service {service_name} started successfully (ultra-fast)\",\n                        \"service_name\": service_name,\n                        \"port\": port,\n                        \"ultra_fast_start\": True\n                    }\n                else:\n                    result = {\n                        \"status\": \"error\",\n                        \"message\": f\"Failed to start service: {start_result.stderr}\"\n                    }\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker started successfully for setup: {setup_id}\")\n\n                # Update status to running\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                # START BUTTON ONLY STARTS SERVICE - NO AUTO REGISTER\n                # User can manually register later if needed\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker started successfully'),\n                    \"data\": result\n                }\n            else:\n                # Update status back to stopped on failure\n                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to start broker')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error starting broker {setup_id}: {e}\")\n            # Update status back to stopped on error\n            self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to start broker: {str(e)}\"\n            }\n\n    def stop_broker(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Stop broker instance with proper status tracking\"\"\"\n        try:\n            logger.info(f\"🛑 Stopping broker for setup: {setup_id}\")\n\n            # Update status to stopping\n            self.broker_automation.update_setup_status(setup_id, 'stopping', 'not_connected')\n\n            result = self.broker_automation.stop_algo(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker stopped successfully for setup: {setup_id}\")\n\n                # Update status to stopped\n                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker stopped successfully'),\n                    \"data\": result\n                }\n            else:\n                # Update status back to running on failure\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to stop broker')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error stopping broker {setup_id}: {e}\")\n            # Update status back to running on error\n            self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to stop broker: {str(e)}\"\n            }\n\n    def connect_broker_auto(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"ULTRA FAST auto connect broker - optimized for speed\"\"\"\n        try:\n            logger.info(f\"⚡ Ultra fast auto connecting broker for setup: {setup_id}\")\n\n            # Update status to connecting\n            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')\n\n            # Use ultra fast connect function\n            result = self.broker_automation.ultra_fast_connect_broker(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker connected ultra-fast for setup: {setup_id}\")\n\n                # Update status to connected\n                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker connected ultra-fast'),\n                    \"data\": result\n                }\n            else:\n                # Update status back to not_connected on failure\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to connect broker')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error auto connecting broker {setup_id}: {e}\")\n            # Update status back to not_connected on error\n            self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to connect broker: {str(e)}\"\n            }\n\n    def auto_connect_broker_with_automation(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Auto connect broker using the automation system (step_4_connect_broker)\"\"\"\n        try:\n            logger.info(f\"🤖 Auto connecting broker with automation for setup: {setup_id}\")\n\n            # Update status to connecting\n            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')\n\n            # Use the automation system's step_4_connect_broker method\n            result = self.broker_automation.step_4_connect_broker(setup_id)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Broker auto-connected with automation for setup: {setup_id}\")\n\n                # Update status to connected\n                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker connected successfully'),\n                    \"data\": result.get('data', {})\n                }\n            else:\n                logger.error(f\"❌ Failed to auto-connect broker with automation: {result.get('message')}\")\n\n                # Update status back to not connected\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to connect broker with automation')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error in auto broker connection with automation: {e}\")\n\n            # Update status back to not connected on error\n            self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to auto-connect broker with automation: {str(e)}\"\n            }\n\n    def get_broker_oauth_url(self, setup_id: int) -> Dict[str, Any]:\n        \"\"\"Get OAuth URL and credentials for manual broker connection\"\"\"\n        try:\n            logger.info(f\"🔗 Getting OAuth URL for setup: {setup_id}\")\n\n            # Get setup data\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\"\n                }\n\n            broker_name = setup_data.get('broker_name', '').lower()\n\n            if broker_name == 'flattrade':\n                # Generate Flattrade OAuth URL\n                api_key = setup_data.get('broker_api_key', '').split(':::')[1] if ':::' in setup_data.get('broker_api_key', '') else setup_data.get('broker_api_key', '')\n                oauth_url = f\"https://auth.flattrade.in/?app_key={api_key}\"\n\n                return {\n                    \"success\": True,\n                    \"message\": \"OAuth URL generated successfully\",\n                    \"data\": {\n                        \"oauth_url\": oauth_url,\n                        \"broker_name\": broker_name,\n                        \"credentials\": {\n                            \"client_code\": setup_data.get('broker_client_id'),\n                            \"password\": setup_data.get('trading_pin'),\n                            \"dob\": setup_data.get('totp_secret')\n                        }\n                    }\n                }\n            else:\n                return {\n                    \"success\": False,\n                    \"message\": f\"OAuth URL not supported for broker: {broker_name}\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error getting OAuth URL: {e}\")\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to get OAuth URL: {str(e)}\"\n            }\n\n    # REMOVED: connect_broker_manual() - Duplicate method #2, use run_complete_manual_setup() instead\n\n    def connect_broker_manual_otp(self, setup_id: int, manual_otp: str) -> Dict[str, Any]:\n        \"\"\"Manual broker connection with OTP only (uses saved credentials)\"\"\"\n        try:\n            logger.info(f\"🔗 Manual OTP connect for setup: {setup_id}\")\n\n            # Update status to connecting\n            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')\n\n            # Get saved setup data (includes all credentials)\n            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)\n            if not setup_data:\n                return {\n                    \"success\": False,\n                    \"message\": \"Setup not found\",\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"error\"\n                }\n\n            # Validate that we have saved credentials\n            required_fields = ['broker_client_id', 'broker_api_key', 'broker_api_secret', 'trading_pin']\n            missing_fields = [field for field in required_fields if not setup_data.get(field)]\n\n            if missing_fields:\n                return {\n                    \"success\": False,\n                    \"message\": f\"Missing saved credentials: {', '.join(missing_fields)}. Please update broker setup.\",\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"error\"\n                }\n\n            # Use manual OTP with saved credentials\n            result = self.broker_automation.connect_with_manual_otp(setup_id, manual_otp)\n\n            if result.get('status') == 'success':\n                logger.info(f\"✅ Manual OTP connection successful for setup: {setup_id}\")\n\n                # Update status to connected\n                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker connected with manual OTP'),\n                    \"data\": result\n                }\n            else:\n                logger.error(f\"❌ Manual OTP connection failed for setup: {setup_id}\")\n\n                # Update status to error\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Manual OTP connection failed'),\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"not_connected\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error in manual OTP connection for setup {setup_id}: {e}\")\n\n            # Update status to error\n            try:\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n            except:\n                pass\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to connect with manual OTP: {str(e)}\",\n                \"setup_status\": \"running\",\n                \"connection_status\": \"not_connected\"\n            }\n\n    def run_complete_manual_setup(self, setup_id: int, manual_otp: str) -> Dict[str, Any]:\n        \"\"\"Run complete manual setup for new instances (register + login + broker auth)\"\"\"\n        try:\n            logger.info(f\"🔄 Complete manual setup for setup: {setup_id}\")\n\n            # Update status to connecting\n            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')\n\n            # Run complete manual setup flow\n            result = self.broker_automation.run_complete_manual_setup(setup_id, manual_otp)\n\n            if result.get('status') == 'success':\n                # Update final status\n                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Complete setup successful'),\n                    \"data\": result.get('data', {})\n                }\n            else:\n                # Update status back to not_connected on failure\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Complete setup failed')\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Complete manual setup error: {e}\")\n\n            # Update status back to not_connected on error\n            try:\n                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')\n            except:\n                pass\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to complete manual setup: {str(e)}\"\n            }\n\n    def connect_broker_manual_openalgo_style(self, setup_id: int, manual_credentials: Dict[str, Any] = None) -> Dict[str, Any]:\n        \"\"\"Manual broker connection using OpenAlgo instance approach\"\"\"\n        try:\n            logger.info(f\"🔗 Manual OpenAlgo-style broker connection for setup: {setup_id}\")\n            logger.info(f\"📋 Manual credentials provided: {manual_credentials is not None}\")\n\n            # Update status to connecting\n            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')\n\n            # Use the new manual connection method\n            result = self.broker_automation.connect_broker_manual_openalgo_style(setup_id, manual_credentials)\n            logger.info(f\"📡 Broker automation result: {result}\")\n\n            if result.get('status') == 'success':\n                # Update status to connected\n                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')\n\n                return {\n                    \"success\": True,\n                    \"message\": result.get('message', 'Broker connected successfully using OpenAlgo style'),\n                    \"data\": result.get('data', {}),\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"connected\"\n                }\n            else:\n                # Update status to error\n                self.broker_automation.update_setup_status(setup_id, 'running', 'error')\n\n                return {\n                    \"success\": False,\n                    \"message\": result.get('message', 'Failed to connect broker'),\n                    \"setup_status\": \"running\",\n                    \"connection_status\": \"error\"\n                }\n\n        except Exception as e:\n            logger.error(f\"❌ Error manual OpenAlgo-style connecting broker {setup_id}: {e}\")\n            # Update status to error\n            self.broker_automation.update_setup_status(setup_id, 'running', 'error')\n\n            return {\n                \"success\": False,\n                \"message\": f\"Failed to connect broker: {str(e)}\",\n                \"setup_status\": \"running\",\n                \"connection_status\": \"error\"\n            }\n\n\n"}