*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[19:55:06] 




[19:55:06] Extension host agent started.
[19:55:06] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[19:55:06] [<unknown>][87bbd4ed][ManagementConnection] New connection established.
[19:55:06] [<unknown>][a5776b84][ExtensionHostConnection] New connection established.
[19:55:06] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[19:55:07] [<unknown>][a5776b84][ExtensionHostConnection] <178768> Launched Extension Host Process.
[19:55:12] Getting Manifest... github.copilot
[19:55:12] Getting Manifest... github.copilot-chat
[19:55:12] Installing extension: github.copilot {
  installPreReleaseVersion: false,
  donotIncludePackAndDependencies: true,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  isApplicationScoped: false,
  profileLocation: xr {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.102.3', date: '2025-07-29T03:00:23.339Z' }
}
[19:55:12] Installing the extension without checking dependencies and pack github.copilot
[19:55:12] Installing extension: github.copilot-chat {
  installPreReleaseVersion: false,
  donotIncludePackAndDependencies: true,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  isApplicationScoped: false,
  profileLocation: xr {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.102.3', date: '2025-07-29T03:00:23.339Z' }
}
[19:55:12] Installing the extension without checking dependencies and pack github.copilot-chat
[19:55:20] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 6613ms.
[19:55:20] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 6703ms.
[19:55:21] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1: github.copilot-chat
[19:55:21] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1
[19:55:21] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
[19:55:22] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.350.0: github.copilot
[19:55:22] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.350.0
[19:55:22] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
[19:55:26] Getting Manifest... augment.vscode-augment
[19:55:26] Installing extension: augment.vscode-augment {
  installPreReleaseVersion: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  isApplicationScoped: false,
  profileLocation: xr {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.102.3', date: '2025-07-29T03:00:23.339Z' }
}
[19:55:30] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 3406ms.
[19:55:36] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.516.2: augment.vscode-augment
[19:55:36] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.516.2
[19:55:36] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
New EH opened, aborting shutdown
[20:00:06] New EH opened, aborting shutdown
[20:46:35] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
[21:08:25] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
