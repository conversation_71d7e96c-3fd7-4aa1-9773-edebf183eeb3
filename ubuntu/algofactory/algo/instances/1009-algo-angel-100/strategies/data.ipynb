{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'status': 'error',\n", " 'message': 'HTTP 403: {\"message\":\"Invalid openalgo apikey\",\"status\":\"error\"}\\n'}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from openalgo import api\n", "\n", "# Initialize the API client\n", "client = api(api_key='openalgo-api-key', host='http://127.0.0.1:5000')\n", "\n", "# Fetch historical data for SBIN\n", "df = client.history(\n", "    symbol=\"SBIN\",\n", "    exchange=\"NSE\",\n", "    interval=\"5m\",\n", "    start_date=\"2025-01-01\",\n", "    end_date=\"2025-01-24\"\n", ")\n", "\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}