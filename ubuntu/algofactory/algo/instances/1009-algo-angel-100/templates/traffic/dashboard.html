{% extends "base.html" %}

{% block content %}
<div class="w-full">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div class="stats shadow">
            <div class="stat">
                <div class="stat-title">Total Requests</div>
                <div class="stat-value" id="total-requests">{{ stats.total_requests }}</div>
            </div>
        </div>
        <div class="stats shadow">
            <div class="stat">
                <div class="stat-title">Error Requests</div>
                <div class="stat-value text-error" id="error-requests">{{ stats.error_requests }}</div>
            </div>
        </div>
        <div class="stats shadow">
            <div class="stat">
                <div class="stat-title">Avg Duration</div>
                <div class="stat-value" id="avg-duration">{{ "%.2f"|format(stats.avg_duration) }}ms</div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="flex gap-4 mb-4">
        <button id="refresh-btn" class="btn btn-sm gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
        </button>
        <a href="{{ url_for('traffic_bp.export_logs') }}" class="btn btn-sm btn-primary gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export to CSV
        </a>
        <div class="dropdown">
            <button id="filter-btn" class="btn btn-sm gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                Filter: <span id="current-filter" class="capitalize">all</span>
            </button>
            <ul id="filter-menu" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52 hidden">
                <li><button class="w-full text-left px-4 py-2 hover:bg-base-200" data-filter="all">All</button></li>
                <li><button class="w-full text-left px-4 py-2 hover:bg-base-200" data-filter="error">Errors Only</button></li>
                <li><button class="w-full text-left px-4 py-2 hover:bg-base-200" data-filter="success">Success Only</button></li>
            </ul>
        </div>
    </div>

    <!-- Traffic Tabs -->
    <div role="tablist" class="tabs tabs-boxed mb-4">
        <a role="tab" class="tab tab-active" data-tab="all">All Traffic</a>
        <a role="tab" class="tab" data-tab="api">API Traffic</a>
    </div>

    <!-- Traffic Logs Table -->
    <div class="overflow-x-auto bg-base-100 rounded-lg shadow">
        <div class="flex justify-between items-center p-4 border-b border-base-200">
            <h2 class="text-xl font-semibold" id="table-title">Recent Traffic</h2>
        </div>
        <table class="table table-zebra w-full">
            <thead>
                <tr>
                    <th>Timestamp</th>
                    <th>Method</th>
                    <th>Path</th>
                    <th>Status</th>
                    <th>Duration</th>
                    <th>Client IP</th>
                    <th>Host</th>
                </tr>
            </thead>
            <tbody id="logs-table-body">
            </tbody>
        </table>
    </div>
</div>

<script>
(function() {
    const state = {
        activeTab: 'all',
        currentFilter: 'all',
        logs: JSON.parse('{{ logs|tojson|safe }}'),
        isDropdownOpen: false
    };

    const elements = {
        tableBody: document.getElementById('logs-table-body'),
        tableTitle: document.getElementById('table-title'),
        tabs: document.querySelectorAll('[data-tab]'),
        filterBtn: document.getElementById('filter-btn'),
        filterMenu: document.getElementById('filter-menu'),
        currentFilter: document.getElementById('current-filter'),
        refreshBtn: document.getElementById('refresh-btn')
    };

    function getMethodBadgeClass(method) {
        const classes = {
            'GET': 'badge-info',
            'POST': 'badge-success',
            'DELETE': 'badge-error'
        };
        return classes[method] || 'badge-warning';
    }

    function filterLogs() {
        return state.logs.filter(log => {
            if (state.activeTab === 'api' && !log.path.startsWith('/api/v1/')) {
                return false;
            }
            
            if (state.currentFilter === 'error') {
                return log.status_code >= 400;
            } else if (state.currentFilter === 'success') {
                return log.status_code < 400;
            }
            
            return true;
        });
    }

    function updateTable() {
        const filteredLogs = filterLogs();
        elements.tableBody.innerHTML = filteredLogs.map(log => `
            <tr class="hover">
                <td>${log.timestamp}</td>
                <td>
                    <span class="badge badge-sm ${getMethodBadgeClass(log.method)}">
                        ${log.method}
                    </span>
                </td>
                <td class="max-w-xs truncate">${log.path}</td>
                <td>
                    <span class="badge badge-sm ${log.status_code >= 400 ? 'badge-error' : 'badge-success'}">
                        ${log.status_code}
                    </span>
                </td>
                <td>${log.duration_ms.toFixed(2)}ms</td>
                <td>${log.client_ip}</td>
                <td class="max-w-xs truncate">${log.host || ''}</td>
            </tr>
        `).join('');
    }

    function updateStats(stats) {
        const data = state.activeTab === 'api' ? stats.api : stats.overall;
        document.getElementById('total-requests').textContent = data.total_requests;
        document.getElementById('error-requests').textContent = data.error_requests;
        document.getElementById('avg-duration').textContent = data.avg_duration.toFixed(2) + 'ms';
    }

    function updateUI() {
        elements.tableTitle.textContent = state.activeTab === 'api' ? 'API Traffic' : 'Recent Traffic';
        
        elements.tabs.forEach(tab => {
            if (tab.dataset.tab === state.activeTab) {
                tab.classList.add('tab-active');
            } else {
                tab.classList.remove('tab-active');
            }
        });
        
        elements.currentFilter.textContent = state.currentFilter;
        updateTable();
    }

    async function refreshData() {
        try {
            const [logsResponse, statsResponse] = await Promise.all([
                fetch('/traffic/api/logs'),
                fetch('/traffic/api/stats')
            ]);
            
            if (!logsResponse.ok || !statsResponse.ok) {
                throw new Error('Failed to fetch data');
            }

            const logsData = await logsResponse.json();
            const statsData = await statsResponse.json();
            
            state.logs = logsData;
            updateStats(statsData);
            updateTable();
        } catch (error) {
            console.error('Error refreshing data:', error);
        }
    }

    function toggleDropdown(event) {
        event.stopPropagation();
        state.isDropdownOpen = !state.isDropdownOpen;
        elements.filterMenu.classList.toggle('hidden');
    }

    function closeDropdown() {
        state.isDropdownOpen = false;
        elements.filterMenu.classList.add('hidden');
    }

    function init() {
        // Tab switching
        elements.tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                state.activeTab = tab.dataset.tab;
                updateUI();
            });
        });

        // Filter button
        elements.filterBtn.addEventListener('click', toggleDropdown);

        // Filter menu items
        elements.filterMenu.querySelectorAll('[data-filter]').forEach(item => {
            item.addEventListener('click', (event) => {
                event.stopPropagation();
                state.currentFilter = item.dataset.filter;
                closeDropdown();
                updateUI();
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.dropdown')) {
                closeDropdown();
            }
        });

        // Refresh button
        elements.refreshBtn.addEventListener('click', refreshData);

        // Initial render
        updateUI();

        // Auto-refresh every 30 seconds
        setInterval(refreshData, 30000);
    }

    document.addEventListener('DOMContentLoaded', init);
})();
</script>
{% endblock %}
