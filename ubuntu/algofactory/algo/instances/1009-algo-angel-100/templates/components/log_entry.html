{% set request_data = log.request_data %}
<div class="card bg-base-100 shadow-lg mb-4 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
    <div class="card-body p-6">
        <!-- Header with Badges -->
        <div class="flex flex-wrap gap-2 mb-4">
            <!-- API Type Badge -->
            <div class="badge badge-function gap-2">
                {% if log.api_type == 'placeorder' %}
                <svg class="api-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                {% elif log.api_type == 'placesmartorder' %}
                <svg class="api-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                </svg>
                {% elif log.api_type == 'modifyorder' %}
                <svg class="api-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                {% elif log.api_type == 'cancelorder' %}
                <svg class="api-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                {% elif log.api_type == 'closeposition' %}
                <svg class="api-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
                </svg>
                {% else %}
                <svg class="api-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                </svg>
                {% endif %}
                {{ log.api_type }}
            </div>

            <!-- Strategy Badge -->
            <div class="badge badge-strategy gap-2">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
                </svg>
                {{ log.strategy }}
            </div>

            {% if request_data.action %}
            <div class="badge {% if request_data.action == 'BUY' %}badge-buy{% else %}badge-sell{% endif %} gap-2">
                {% if request_data.action == 'BUY' %}
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                {% else %}
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                {% endif %}
                {{ request_data.action }}
            </div>
            {% endif %}

            {% if request_data.exchange %}
            <div class="badge badge-exchange gap-2">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V6.414l-1.293 1.293a1 1 0 01-1.414-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L13 6.414V16a1 1 0 01-1 1H4a1 1 0 01-1-1V4z" clip-rule="evenodd"/>
                </svg>
                {{ request_data.exchange }}
            </div>
            {% endif %}

            <div class="badge badge-ghost gap-2">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                </svg>
                {{ log.created_at }}
            </div>
        </div>

        <!-- Always Visible Order Details -->
        {% if request_data.symbol or request_data.quantity or request_data.price or request_data.product %}
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {% if request_data.symbol %}
            <div class="stat bg-base-200 rounded-box shadow-sm">
                <div class="stat-title text-xs uppercase tracking-wide">Symbol</div>
                <div class="stat-value text-lg font-bold">{{ request_data.symbol }}</div>
            </div>
            {% endif %}

            {% if request_data.quantity %}
            <div class="stat bg-base-200 rounded-box shadow-sm">
                <div class="stat-title text-xs uppercase tracking-wide">Quantity</div>
                <div class="stat-value text-lg font-bold">{{ request_data.quantity }}</div>
            </div>
            {% endif %}

            {% if request_data.price and request_data.price != "0" %}
            <div class="stat bg-base-200 rounded-box shadow-sm">
                <div class="stat-title text-xs uppercase tracking-wide">Price</div>
                <div class="stat-value text-lg font-bold">{{ request_data.price }}</div>
            </div>
            {% endif %}

            {% if request_data.product %}
            <div class="stat bg-base-200 rounded-box shadow-sm">
                <div class="stat-title text-xs uppercase tracking-wide">Product</div>
                <div class="stat-value text-lg font-bold">{{ request_data.product }}</div>
            </div>
            {% endif %}

            {% if request_data.pricetype %}
            <div class="stat bg-base-200 rounded-box shadow-sm">
                <div class="stat-title text-xs uppercase tracking-wide">Price Type</div>
                <div class="stat-value text-lg font-bold">{{ request_data.pricetype }}</div>
            </div>
            {% endif %}

            {% if request_data.orderid %}
            <div class="stat bg-base-200 rounded-box shadow-sm">
                <div class="stat-title text-xs uppercase tracking-wide">Order ID</div>
                <div class="stat-value text-lg font-bold">{{ request_data.orderid }}</div>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- Collapsible Request/Response Data -->
        <div class="collapse collapse-plus bg-base-200 rounded-box">
            <input type="checkbox" class="peer" aria-label="View JSON Data" /> 
            <div class="collapse-title font-medium">
                View Request/Response Data
            </div>
            <div class="collapse-content">
                <div class="space-y-4">
                    <div class="bg-base-100 rounded-box p-4">
                        <h4 class="text-sm font-medium flex items-center mb-2">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 00-1 1v4H5a1 1 0 100 2h4v4a1 1 0 102 0v-4h4a1 1 0 100-2h-4V4a1 1 0 00-1-1z" clip-rule="evenodd"/>
                            </svg>
                            Request Data
                        </h4>
                        <div class="json-content">
                            <pre class="text-sm">{{ request_data | tojson(indent=2) }}</pre>
                        </div>
                    </div>
                    <div class="bg-base-100 rounded-box p-4">
                        <h4 class="text-sm font-medium flex items-center mb-2">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"/>
                            </svg>
                            Response Data
                        </h4>
                        <div class="json-content">
                            <pre class="text-sm">{{ log.response_data | tojson(indent=2) }}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
