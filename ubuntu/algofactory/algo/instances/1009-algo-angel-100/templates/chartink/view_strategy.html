{% extends "base.html" %}

{% block title %}{{ strategy.name }} - Strategy Details{% endblock %}

{% block content %}
<!-- Delete Strategy Confirmation Modal -->
<dialog id="delete_strategy_modal" class="modal modal-bottom sm:modal-middle">
    <form method="dialog" class="modal-box">
        <h3 class="font-bold text-lg text-error">Delete Strategy</h3>
        <p class="py-4">Are you sure you want to delete this strategy? This action cannot be undone.</p>
        <div class="modal-action">
            <button class="btn" id="cancel_strategy_delete">Cancel</button>
            <button class="btn btn-error" id="confirm_strategy_delete">Delete Strategy</button>
        </div>
    </form>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">{{ strategy.name }}</h1>
        <div class="space-x-2">
            <a href="{{ url_for('chartink_bp.configure_symbols', strategy_id=strategy.id) }}" class="btn">
                Configure Symbols
            </a>
            <a href="{{ url_for('chartink_bp.index') }}" class="btn">
                Back to Strategies
            </a>
        </div>
    </div>

    <div class="grid gap-6 lg:grid-cols-2">
        <!-- Left Column -->
        <div class="space-y-6">
            <!-- Strategy Details -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title">Strategy Details</h2>
                    
                    <div class="stats stats-vertical shadow">
                        <div class="stat">
                            <div class="stat-title">Status</div>
                            <div class="stat-value text-sm">
                                <div class="badge {% if strategy.is_active %}badge-success{% else %}badge-error{% endif %} gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-4 h-4 stroke-current">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    {{ 'Active' if strategy.is_active else 'Inactive' }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat">
                            <div class="stat-title">Type</div>
                            <div class="stat-value text-sm">
                                {{ 'Intraday' if strategy.is_intraday else 'Positional' }}
                            </div>
                        </div>

                        {% if strategy.is_intraday %}
                        <div class="stat">
                            <div class="stat-title">Trading Hours</div>
                            <div class="stat-value text-sm">
                                {{ strategy.start_time }} - {{ strategy.end_time }}
                            </div>
                        </div>

                        <div class="stat">
                            <div class="stat-title">Square Off Time</div>
                            <div class="stat-value text-sm">
                                {{ strategy.squareoff_time }}
                            </div>
                        </div>
                        {% endif %}

                        <div class="stat">
                            <div class="stat-title">Created At</div>
                            <div class="stat-value text-sm">
                                {{ strategy.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                            </div>
                        </div>
                    </div>

                    <div class="card-actions justify-end mt-4 space-y-2">
                        <form method="POST" action="{{ url_for('chartink_bp.toggle_strategy_route', strategy_id=strategy.id) }}" class="w-full">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" class="btn btn-primary btn-block">
                                {{ 'Deactivate' if strategy.is_active else 'Activate' }} Strategy
                            </button>
                        </form>
                        <button class="btn btn-error btn-block" onclick="document.getElementById('delete_strategy_modal').showModal()">
                            Delete Strategy
                        </button>
                    </div>
                </div>
            </div>

            <!-- Important Notes -->
            <div class="alert alert-warning shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                <div>
                    <h3 class="font-bold">Important Notes</h3>
                    <ul class="list-disc list-inside text-sm mt-2 space-y-1">
                        <li>Keep your webhook URL private</li>
                        <li>Test with small quantities first</li>
                        <li>Monitor the first few alerts</li>
                        {% if strategy.is_intraday %}
                        <li>Orders only placed during trading hours</li>
                        <li>Positions squared off at {{ strategy.squareoff_time }}</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
            <!-- Webhook Configuration -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title">Webhook Configuration</h2>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="label">
                                <span class="label-text">Webhook URL</span>
                                <span class="label-text-alt">Click to copy</span>
                            </label>
                            <div class="mockup-code bg-neutral text-neutral-content dark:bg-primary dark:text-primary-content cursor-pointer" id="webhookUrl">
                                <pre><code>{{ request.host_url }}chartink/webhook/{{ strategy.webhook_id }}</code></pre>
                                <div class="absolute right-2 top-1/2 -translate-y-1/2 text-sm opacity-70 hidden copy-hint">
                                    <span class="badge badge-neutral dark:badge-primary">Copied!</span>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <div>
                                <h3 class="font-bold">Setup Instructions</h3>
                                <ol class="list-decimal list-inside text-sm mt-2 space-y-1">
                                    <li>Go to your Chartink scan</li>
                                    <li>Click "Create Alert" button</li>
                                    <li>Enter an alert name that includes the action keyword</li>
                                    <li>Find the "Webhook url(optional)" field</li>
                                    <li>Copy and paste the URL above</li>
                                    <li>Configure other alert settings as needed</li>
                                    <li>Click "Save alert" button</li>
                                </ol>
                            </div>
                        </div>

                        <div class="alert alert-success">
                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                            <div>
                                <h3 class="font-bold">Alert Name Keywords</h3>
                                <p class="text-sm mt-1">The alert name MUST include one of these keywords:</p>
                                <ul class="list-disc list-inside text-sm mt-2 space-y-1">
                                    <li>
                                        <span class="font-semibold">BUY</span> - For long entry
                                        <div class="text-xs ml-4">Examples: "BUY Alert", "Alert for BUY 2024-12-13", "Supertrend BUY Signal"</div>
                                    </li>
                                    <li>
                                        <span class="font-semibold">SELL</span> - For long exit or regular selling
                                        <div class="text-xs ml-4">Examples: "SELL Alert", "Alert for SELL 2024-12-13", "Supertrend SELL Signal"</div>
                                    </li>
                                    <li>
                                        <span class="font-semibold">SHORT</span> - For short entry (selling first)
                                        <div class="text-xs ml-4">Examples: "SHORT Alert", "Alert for SHORT 2024-12-13", "Supertrend SHORT Signal"</div>
                                    </li>
                                    <li>
                                        <span class="font-semibold">COVER</span> - For short exit (buying to cover)
                                        <div class="text-xs ml-4">Examples: "COVER Alert", "Alert for COVER 2024-12-13", "Supertrend COVER Signal"</div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Keyword Notes -->
            <div class="alert alert-warning shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                <div>
                    <h3 class="font-bold">Keyword Rules</h3>
                    <ul class="list-disc list-inside text-sm mt-2 space-y-1">
                        <li>Keywords can be anywhere in the alert name</li>
                        <li>Keywords are not case-sensitive (buy/BUY/Buy all work)</li>
                        <li>Alert will fail if no valid keyword is found</li>
                        <li>Only one action will be taken even if multiple keywords present</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Symbol Mappings -->
    <div class="card bg-base-100 shadow-xl mt-6">
        <div class="card-body">
            <div class="flex justify-between items-center mb-4">
                <h2 class="card-title">Symbol Mappings</h2>
                <a href="{{ url_for('chartink_bp.configure_symbols', strategy_id=strategy.id) }}" class="btn btn-primary btn-sm">
                    Add Symbols
                </a>
            </div>

            {% if symbol_mappings %}
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full" id="symbolTable">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Exchange</th>
                            <th>Quantity</th>
                            <th>Product</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for mapping in symbol_mappings %}
                        <tr data-mapping-id="{{ mapping.id }}">
                            <td>{{ mapping.chartink_symbol }}</td>
                            <td><div class="badge badge-ghost">{{ mapping.exchange }}</div></td>
                            <td>{{ mapping.quantity }}</td>
                            <td>{{ mapping.product_type }}</td>
                            <td>
                                <button class="btn btn-error btn-xs delete-symbol">
                                    Delete
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-8">
                <h3 class="text-lg font-semibold mb-2">No symbols configured</h3>
                <p class="text-base-content/70">Add symbols to start trading with this strategy</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const csrfTokenInput = document.querySelector('input[name="csrf_token"]');
    const csrfToken = csrfTokenInput ? csrfTokenInput.value : null;

    if (!csrfToken && document.getElementById('confirm_strategy_delete')) {
        console.error('CSRF token not found! Strategy deletion and other protected actions may fail.');
        showToast('error', 'Security token missing. Actions may fail.');
    }
    const deleteModal = document.getElementById('delete_strategy_modal');
    const webhookUrlContainer = document.getElementById('webhookUrl');
    const copyHint = webhookUrlContainer.querySelector('.copy-hint');
    const webhookUrl = webhookUrlContainer.querySelector('code').textContent;

    // Copy webhook URL on click
    webhookUrlContainer.addEventListener('click', function() {
        navigator.clipboard.writeText(webhookUrl).then(() => {
            copyHint.classList.remove('hidden');
            setTimeout(() => copyHint.classList.add('hidden'), 2000);
            showToast('success', 'Webhook URL copied to clipboard!');
        }).catch(() => {
            showToast('error', 'Failed to copy webhook URL');
        });
    });

    // Handle strategy deletion
    document.getElementById('confirm_strategy_delete').addEventListener('click', function() {
        fetch(`/chartink/{{ strategy.id }}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                window.location.href = "{{ url_for('chartink_bp.index') }}";
            } else {
                showToast('error', data.error || 'Error deleting strategy');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Error deleting strategy');
        });
    });
});

function showToast(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
    const icon = type === 'success' ? 
        '<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>' :
        '<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';

    const toast = document.createElement('div');
    toast.className = 'toast toast-end z-50';
    toast.innerHTML = `
        <div class="alert ${alertClass}">
            ${icon}
            <span>${message}</span>
        </div>
    `;
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
}
</script>
{% endblock %}

{% block styles %}
<style>
#webhookUrl {
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
}

#webhookUrl:hover {
    opacity: 0.95;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

#webhookUrl pre {
    margin: 0;
    background: transparent;
    padding: 1rem;
}

#webhookUrl code {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.copy-hint {
    z-index: 10;
}

.copy-hint .badge {
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
}

.mockup-code .text-success {
    color: #4ade80 !important;
}

.mockup-code .text-info {
    color: #60a5fa !important;
}

/* Theme-specific styles */
[data-theme="light"] .mockup-code,
[data-theme="garden"] .mockup-code {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

[data-theme="light"] .mockup-code .badge,
[data-theme="garden"] .mockup-code .badge {
    background-color: #ffffff !important;
    color: #1a1a1a !important;
}

[data-theme="dark"] .mockup-code {
    background-color: hsl(var(--p)) !important;
    color: hsl(var(--pc)) !important;
}
</style>
{% endblock %}
{% endblock %}
