{% extends "base.html" %}

{% block content %}
<div id="userInfo" data-login-username="{{ login_username }}" class="hidden"></div>

<div class="py-6">
    <div class="container mx-auto px-4">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title">API Key Management</h2>
                <p class="text-base-content/70">
                    {% if has_api_key %}
                        Your API key was automatically generated during account creation. You can view it below or generate a new one if needed.
                    {% else %}
                        Your API key could not be found. Please generate a new one.
                    {% endif %}
                </p>

                <!-- API Key Display -->
                <div class="mt-4">
                    <div class="collapse bg-base-200 rounded-box">
                        <input type="checkbox" id="apiKeyCollapse" /> 
                        <div class="collapse-title font-medium">
                            <div class="flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                </svg>
                                {% if has_api_key %}Click to Show/Hide API Key{% else %}No Active API Key{% endif %}
                            </div>
                        </div>
                        <div class="collapse-content"> 
                            <div id="apiKeyDisplay" class="font-mono bg-base-300 p-4 rounded-box mt-2">
                                {% if api_key %}
                                    {{ api_key }}
                                {% else %}
                                    No API key generated yet
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Copy Button -->
                    <div class="mt-4 flex gap-2">
                        <button onclick="copyApiKeyToClipboard()" class="btn btn-primary gap-2" id="copyButton" {% if not has_api_key %}disabled{% endif %}>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                            Copy API Key
                        </button>

                        <!-- Generate/Regenerate Button -->
                        <button onclick="document.getElementById('confirm-modal').showModal()" class="btn btn-secondary gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            {% if has_api_key %}Regenerate API Key{% else %}Generate API Key{% endif %}
                        </button>
                    </div>

                    <!-- Loading Indicator -->
                    <div id="loadingIndicator" class="hidden mt-4">
                        <span class="loading loading-spinner loading-md"></span>
                        <span class="ml-2">{% if has_api_key %}Regenerating{% else %}Generating{% endif %} API key...</span>
                    </div>

                    <!-- Success Alert -->
                    <div id="copySuccessAlert" class="alert alert-success mt-4 hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                            <h3 class="font-bold">Copied!</h3>
                            <div class="text-sm">The API key has been copied to your clipboard.</div>
                        </div>
                    </div>

                    <!-- Error Alert -->
                    <div id="errorAlert" class="alert alert-error mt-4 hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                            <h3 class="font-bold">Error!</h3>
                            <div class="text-sm" id="errorMessage">Failed to regenerate API key. Please try again.</div>
                        </div>
                    </div>

                    <!-- Success Alert for Generation -->
                    <div id="regenerateSuccessAlert" class="alert alert-success mt-4 hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                            <h3 class="font-bold">Success!</h3>
                            <div class="text-sm">API key has been generated successfully.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<dialog id="confirm-modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
        <h3 class="font-bold text-lg">Confirm {% if has_api_key %}Regeneration{% else %}Generation{% endif %}</h3>
        <p class="py-4">
            {% if has_api_key %}
            Are you sure you want to regenerate your API key? The current key will be invalidated and any applications using it will need to be updated.
            {% else %}
            Generate a new API key for authenticating your API requests?
            {% endif %}
        </p>
        <div class="modal-action">
            <form method="dialog">
                <button class="btn btn-ghost">Cancel</button>
            </form>
            <button onclick="regenerateApiKey()" class="btn btn-primary">{% if has_api_key %}Regenerate{% else %}Generate{% endif %}</button>
        </div>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>
{% endblock %}

{% block head %}
<script src="{{ url_for('static', filename='js/app.js') }}"></script>
<script>
    function showLoading(show) {
        document.getElementById('loadingIndicator').classList.toggle('hidden', !show);
    }

    function showAlert(alertId, duration = 3000) {
        const alert = document.getElementById(alertId);
        alert.classList.remove('hidden');
        setTimeout(() => { alert.classList.add('hidden'); }, duration);
    }

    function updateApiKeyDisplay(newKey) {
        const display = document.getElementById('apiKeyDisplay');
        display.textContent = newKey;
        
        // Enable copy button
        document.getElementById('copyButton').disabled = false;
        
        // Ensure the collapse is open to show the new key
        document.getElementById('apiKeyCollapse').checked = true;
    }

    function copyApiKeyToClipboard() {
        const apiKey = document.getElementById('apiKeyDisplay').textContent.trim();
        if (apiKey && !apiKey.includes('No API key')) {
            navigator.clipboard.writeText(apiKey).then(function() {
                showAlert('copySuccessAlert');
            }, function(err) {
                console.error('Could not copy text: ', err);
                showError('Failed to copy API key to clipboard');
            });
        }
    }

    function showError(message) {
        const errorMessage = document.getElementById("errorMessage");
        errorMessage.textContent = message;
        showAlert('errorAlert');
    }

    function regenerateApiKey() {
        // Close the modal
        document.getElementById('confirm-modal').close();
        
        // Show loading indicator
        showLoading(true);
        
        fetch('/apikey', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                user_id: "{{ login_username }}"
            })
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.api_key) {
                updateApiKeyDisplay(data.api_key);
                showAlert('regenerateSuccessAlert');
            } else {
                showError(data.error || 'Failed to regenerate API key');
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('Error:', error);
            showError('Failed to regenerate API key. Please try again.');
        });
    }
</script>
{% endblock %}
