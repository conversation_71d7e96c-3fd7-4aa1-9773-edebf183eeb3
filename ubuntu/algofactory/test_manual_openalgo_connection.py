#!/usr/bin/env python3
"""
Test script for manual OpenAlgo-style broker connections
"""

import sys
import os
import requests
import json

# Add the project root to Python path
sys.path.append('/home/<USER>/algofactory')

from scripts.automation.broker_automation import Broker<PERSON>uto<PERSON>

def test_angel_manual_connection():
    """Test Angel manual connection"""
    print("🧪 Testing Angel Manual OpenAlgo-Style Connection")
    print("=" * 60)
    
    # Initialize broker automation
    broker_automation = BrokerAutomation()
    
    # Test setup ID (replace with actual setup ID)
    setup_id = input("Enter Angel setup ID to test: ").strip()
    if not setup_id:
        print("❌ Setup ID is required")
        return
    
    try:
        setup_id = int(setup_id)
    except ValueError:
        print("❌ Setup ID must be a number")
        return
    
    # Get manual credentials
    print("\n📋 Enter Angel One credentials:")
    clientid = input("Client ID: ").strip()
    pin = input("Trading PIN: ").strip()
    totp = input("TOTP (6 digits): ").strip()
    
    if not all([clientid, pin, totp]):
        print("❌ All credentials are required")
        return
    
    if len(totp) != 6 or not totp.isdigit():
        print("❌ TOTP must be exactly 6 digits")
        return
    
    manual_credentials = {
        'clientid': clientid,
        'pin': pin,
        'totp': totp
    }
    
    print(f"\n🔗 Testing manual connection for setup {setup_id}...")
    
    # Test the connection
    result = broker_automation.connect_broker_manual_openalgo_style(setup_id, manual_credentials)
    
    print(f"\n📊 Result:")
    print(f"Status: {result.get('status')}")
    print(f"Message: {result.get('message')}")
    
    if result.get('status') == 'success':
        print("✅ Angel manual connection test PASSED!")
        data = result.get('data', {})
        print(f"Connection URL: {data.get('url')}")
        print(f"Connection Method: {data.get('connection_method')}")
    else:
        print("❌ Angel manual connection test FAILED!")

def test_dhan_manual_connection():
    """Test Dhan manual connection"""
    print("🧪 Testing Dhan Manual OpenAlgo-Style Connection")
    print("=" * 60)
    
    # Initialize broker automation
    broker_automation = BrokerAutomation()
    
    # Test setup ID (replace with actual setup ID)
    setup_id = input("Enter Dhan setup ID to test: ").strip()
    if not setup_id:
        print("❌ Setup ID is required")
        return
    
    try:
        setup_id = int(setup_id)
    except ValueError:
        print("❌ Setup ID must be a number")
        return
    
    print(f"\n🔗 Testing manual connection for setup {setup_id}...")
    
    # Test the connection (no credentials needed for Dhan)
    result = broker_automation.connect_broker_manual_openalgo_style(setup_id, None)
    
    print(f"\n📊 Result:")
    print(f"Status: {result.get('status')}")
    print(f"Message: {result.get('message')}")
    
    if result.get('status') == 'success':
        print("✅ Dhan manual connection test PASSED!")
        data = result.get('data', {})
        print(f"Connection URL: {data.get('url')}")
        print(f"Connection Method: {data.get('connection_method')}")
    else:
        print("❌ Dhan manual connection test FAILED!")

def test_api_endpoint():
    """Test the API endpoint"""
    print("🧪 Testing API Endpoint")
    print("=" * 60)
    
    # Test setup ID
    setup_id = input("Enter setup ID to test API: ").strip()
    if not setup_id:
        print("❌ Setup ID is required")
        return
    
    broker_type = input("Enter broker type (angel/dhan): ").strip().lower()
    if broker_type not in ['angel', 'dhan']:
        print("❌ Broker type must be 'angel' or 'dhan'")
        return
    
    # Prepare request data
    if broker_type == 'angel':
        print("\n📋 Enter Angel One credentials:")
        clientid = input("Client ID: ").strip()
        pin = input("Trading PIN: ").strip()
        totp = input("TOTP (6 digits): ").strip()
        
        request_data = {
            'clientid': clientid,
            'pin': pin,
            'totp': totp
        }
    else:
        request_data = {}  # Dhan doesn't need credentials
    
    # Make API request
    url = f"http://localhost:8000/api/broker-setup/{setup_id}/connect-manual-openalgo"
    
    print(f"\n🌐 Making API request to: {url}")
    print(f"📤 Request data: {request_data}")
    
    try:
        response = requests.post(url, json=request_data, timeout=30)
        
        print(f"\n📡 Response:")
        print(f"Status Code: {response.status_code}")
        
        try:
            result = response.json()
            print(f"Response Data: {json.dumps(result, indent=2)}")
            
            if response.status_code == 200 and result.get('success'):
                print("✅ API endpoint test PASSED!")
            else:
                print("❌ API endpoint test FAILED!")
                
        except json.JSONDecodeError:
            print(f"Response Text: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API request failed: {e}")

def main():
    """Main test function"""
    print("🧪 Manual OpenAlgo-Style Connection Test Suite")
    print("=" * 60)
    
    while True:
        print("\nSelect test to run:")
        print("1. Test Angel Manual Connection (Direct)")
        print("2. Test Dhan Manual Connection (Direct)")
        print("3. Test API Endpoint")
        print("4. Exit")
        
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == '1':
            test_angel_manual_connection()
        elif choice == '2':
            test_dhan_manual_connection()
        elif choice == '3':
            test_api_endpoint()
        elif choice == '4':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-4.")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
