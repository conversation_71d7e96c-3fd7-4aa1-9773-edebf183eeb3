#!/usr/bin/env python3
"""
Broker Automation System for AlgoFactory
Handles OpenAlgo instance creation, setup, and broker authentication
"""

import os
import sys
import json
import time
import shutil
import sqlite3
import requests
import subprocess
import re
import logging
from pathlib import Path
from datetime import datetime
import pyotp

# Setup logger
logger = logging.getLogger(__name__)

class BrokerAutomation:
    def __init__(self):
        self.base_dir = Path("/home/<USER>/algofactory")
        self.algo_dir = self.base_dir / "algo"
        self.instances_dir = self.algo_dir / "instances"
        self.template_dir = self.algo_dir / "template"
        self.db_path = self.base_dir / "database/algofactory.db"

        # Ensure directories exist
        self.instances_dir.mkdir(parents=True, exist_ok=True)
        self.template_dir.mkdir(parents=True, exist_ok=True)

        # Port allocation (starting from 5001)
        self.base_port = 5001

        # Environment for subprocess calls - include virtual environment
        import os
        self.subprocess_env = os.environ.copy()
        self.subprocess_env.update({
            'PATH': '/home/<USER>/algofactory/env/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/bin',
            'HOME': '/home/<USER>',
            'USER': 'ubuntu',
            'VIRTUAL_ENV': '/home/<USER>/algofactory/env'
        })

    def run_systemctl(self, action, service_name, timeout=30):
        """Helper method to run systemctl commands with proper environment"""
        try:
            # Use full paths and proper environment for API compatibility
            cmd = f"/usr/bin/sudo /usr/bin/systemctl {action} {service_name}"
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                env=self.subprocess_env,
                cwd=str(self.base_dir)
            )
            return result
        except Exception as e:
            print(f"Error running systemctl {action} {service_name}: {e}")
            return None
        
    def get_ist_timestamp(self):
        """Get IST timestamp"""
        from datetime import datetime, timezone, timedelta
        ist = timezone(timedelta(hours=5, minutes=30))
        return datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S')

    def get_all_broker_setups(self):
        """Get all broker setups from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get all broker setups with user information
            cursor.execute("""
                SELECT
                    ub.id,
                    ub.username,
                    ub.broker_name,
                    ub.setup_name,
                    ub.instance_name,
                    ub.broker_client_id,
                    ub.setup_status,
                    ub.connection_status,
                    ub.http_port,
                    ub.websocket_port,
                    ub.zmq_port,
                    ub.detailed_status,
                    ub.created_at,
                    u.admin_id,
                    u.full_name as user_full_name
                FROM user_brokers ub
                LEFT JOIN users u ON ub.username = u.username
                ORDER BY ub.created_at DESC
            """)

            setups = []
            for row in cursor.fetchall():
                setup = dict(row)
                # Parse detailed_status if it's JSON
                try:
                    if setup['detailed_status']:
                        setup['detailed_status'] = json.loads(setup['detailed_status'])
                except:
                    setup['detailed_status'] = {}

                setups.append(setup)

            conn.close()
            return setups

        except Exception as e:
            print(f"❌ Error getting broker setups: {e}")
            return []

    def update_all_instances(self):
        """Update all OpenAlgo instances with git pull and requirements - for morning updates"""
        try:
            print(f"🌅 Morning update: Updating all OpenAlgo instances...")

            updated_instances = []
            failed_instances = []

            # Get all instances
            for instance_dir in self.instances_dir.iterdir():
                if instance_dir.is_dir() and not instance_dir.name.startswith('.'):
                    try:
                        print(f"🔄 Updating instance: {instance_dir.name}")

                        # Git pull
                        git_result = subprocess.run([
                            '/usr/bin/git', 'pull', 'origin', 'main'
                        ], cwd=instance_dir, capture_output=True, text=True)

                        # Update requirements
                        requirements_file = instance_dir / "requirements.txt"
                        if requirements_file.exists():
                            pip_result = subprocess.run([
                                'pip', 'install', '-r', str(requirements_file), '--upgrade'
                            ], capture_output=True, text=True)

                        updated_instances.append(instance_dir.name)
                        print(f"✅ Updated: {instance_dir.name}")

                    except Exception as e:
                        failed_instances.append(f"{instance_dir.name}: {str(e)}")
                        print(f"❌ Failed to update {instance_dir.name}: {e}")

            return {
                "status": "success",
                "message": f"Morning update completed: {len(updated_instances)} updated, {len(failed_instances)} failed",
                "updated": updated_instances,
                "failed": failed_instances,
                "timestamp": self.get_ist_timestamp()
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Morning update failed: {str(e)}"
            }

    def get_instance_name(self, setup_data):
        """Generate consistent multi-user instance name: username-algo-broker-setupid"""
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        return f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"

    def get_service_name(self, setup_data):
        """Generate consistent multi-user service name: username-algo-broker-setupid"""
        return self.get_instance_name(setup_data)

    def create_backup(self, setup_id):
        """Create backup - alias for backup_instance method"""
        return self.backup_instance(setup_id)

    def delete_systemd_service(self, service_name):
        """Comprehensive systemd service deletion"""
        try:
            print(f"🛑 Deleting systemd service: {service_name}")

            # Step 1: Stop the service
            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'stop', service_name],
                                  capture_output=True, text=True, check=False)
            if result.returncode == 0:
                print(f"✅ Service {service_name} stopped")
            else:
                print(f"⚠️ Service {service_name} stop: {result.stderr.strip()}")

            # Step 2: Disable the service
            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'disable', service_name],
                                  capture_output=True, text=True, check=False)
            if result.returncode == 0:
                print(f"✅ Service {service_name} disabled")
            else:
                print(f"⚠️ Service {service_name} disable: {result.stderr.strip()}")

            # Step 3: Delete the service file
            service_file = f"/etc/systemd/system/{service_name}.service"
            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/rm', '-f', service_file],
                                  capture_output=True, text=True, check=False)
            if result.returncode == 0:
                print(f"✅ Service file deleted: {service_file}")
            else:
                print(f"⚠️ Error deleting service file: {result.stderr.strip()}")

            # Step 4: Reload systemd daemon
            result = subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'daemon-reload'],
                                  capture_output=True, text=True, check=False)
            if result.returncode == 0:
                print(f"✅ Systemd daemon reloaded")
            else:
                print(f"⚠️ Daemon reload warning: {result.stderr.strip()}")

            # Step 5: Reset failed state (if any)
            subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'reset-failed', service_name],
                          capture_output=True, text=True, check=False)

            return {"status": "success", "message": f"Service {service_name} completely removed"}

        except Exception as e:
            return {"status": "error", "message": f"Failed to delete service {service_name}: {str(e)}"}

    def get_all_user_services(self, username=None):
        """Get all systemd services for a specific user or all algo services"""
        try:
            # Get all systemd services
            result = subprocess.run(['sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'],
                                  capture_output=True, text=True, check=False)

            services = []
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'algo' in line.lower():
                        parts = line.split()
                        if len(parts) >= 4:
                            service_name = parts[0]
                            # Filter by username if provided
                            if username:
                                if service_name.startswith(f"{username}-algo-"):
                                    services.append({
                                        "name": service_name,
                                        "status": parts[2],
                                        "active": parts[1],
                                        "description": ' '.join(parts[4:]) if len(parts) > 4 else ""
                                    })
                            else:
                                if '-algo-' in service_name:
                                    services.append({
                                        "name": service_name,
                                        "status": parts[2],
                                        "active": parts[1],
                                        "description": ' '.join(parts[4:]) if len(parts) > 4 else ""
                                    })

            return {"status": "success", "services": services, "count": len(services)}

        except Exception as e:
            return {"status": "error", "message": f"Failed to get services: {str(e)}"}

    def stop_algo(self, setup_id):
        """Stop and disable OpenAlgo instance"""
        try:
            logger.info(f"⏹️ Stopping and disabling OpenAlgo for setup: {setup_id}")

            # Get setup data
            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"status": "error", "message": f"Setup {setup_id} not found"}

            instance_name = setup_data.get('instance_name')
            service_name = instance_name  # Use instance name directly as service name

            # Stop systemd service
            stop_result = self.run_systemctl('stop', service_name)

            # Disable systemd service to prevent auto-restart
            disable_result = self.run_systemctl('disable', service_name)

            if stop_result and stop_result.returncode == 0:
                logger.info(f"✅ Algo service stopped: {service_name}")

                if disable_result and disable_result.returncode == 0:
                    logger.info(f"✅ Algo service disabled: {service_name}")
                    message = f"Algo stopped and disabled successfully"
                else:
                    error_msg = disable_result.stderr if disable_result else "disable command failed"
                    logger.warning(f"⚠️ Service stopped but disable failed: {error_msg}")
                    message = f"Algo stopped but disable failed"

                # Update database status
                self.update_setup_status(setup_id, 'stopped', 'not_connected')

                return {
                    "status": "success",
                    "message": message,
                    "service_name": service_name,
                    "stopped": True,
                    "disabled": disable_result.returncode == 0
                }
            else:
                error_msg = stop_result.stderr if stop_result else "stop command failed"
                logger.error(f"❌ Failed to stop service: {error_msg}")
                return {
                    "status": "error",
                    "message": f"Failed to stop service: {error_msg}"
                }

        except Exception as e:
            logger.error(f"❌ Error stopping Algo: {e}")
            return {"status": "error", "message": f"Failed to stop Algo: {str(e)}"}

    def get_logs(self, setup_id):
        """Get OpenAlgo logs"""
        try:
            logger.info(f"📄 Getting logs for setup: {setup_id}")

            # Get setup data
            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"status": "error", "message": f"Setup {setup_id} not found"}

            instance_name = setup_data.get('instance_name')
            service_name = instance_name  # Use the instance name directly as service name

            # Get systemd service logs
            logs_cmd = f"sudo journalctl -u {service_name} --no-pager -n 50"
            result = subprocess.run(logs_cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                logs = result.stdout
                if not logs.strip():
                    logs = "No logs available for this service."

                logger.info(f"✅ Logs retrieved for setup: {setup_id}")
                return {
                    "status": "success",
                    "message": "Logs retrieved successfully",
                    "logs": logs,
                    "service_name": service_name
                }
            else:
                logger.error(f"❌ Failed to get logs: {result.stderr}")
                return {
                    "status": "error",
                    "message": f"Failed to get logs: {result.stderr}"
                }

        except Exception as e:
            logger.error(f"❌ Error getting logs: {e}")
            return {"status": "error", "message": f"Failed to get logs: {str(e)}"}

    def update_setup_status(self, setup_id, setup_status, connection_status):
        """Update setup status in database"""
        try:
            import sqlite3
            db_path = "/home/<USER>/algofactory/database/algofactory.db"

            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE user_brokers
                    SET setup_status = ?, connection_status = ?
                    WHERE id = ?
                """, (setup_status, connection_status, setup_id))
                conn.commit()

            logger.info(f"✅ Updated status for setup {setup_id}: {setup_status}/{connection_status}")
            return True

        except Exception as e:
            logger.error(f"❌ Error updating setup status: {e}")
            return False

    def update_detailed_status(self, setup_id, step, status):
        """Update detailed status in database"""
        try:
            import sqlite3
            import json
            db_path = "/home/<USER>/algofactory/database/algofactory.db"

            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                # Get current detailed status
                cursor.execute("SELECT detailed_status FROM user_brokers WHERE id = ?", (setup_id,))
                row = cursor.fetchone()

                if row and row[0]:
                    try:
                        detailed_status = json.loads(row[0])
                    except:
                        detailed_status = {}
                else:
                    detailed_status = {}

                # Update the specific step
                detailed_status[step] = status

                # Save back to database
                cursor.execute("""
                    UPDATE user_brokers
                    SET detailed_status = ?
                    WHERE id = ?
                """, (json.dumps(detailed_status), setup_id))
                conn.commit()

            logger.info(f"✅ Updated detailed status for setup {setup_id}: {step} = {status}")
            return True

        except Exception as e:
            logger.error(f"❌ Error updating detailed status: {e}")
            return False

    def fast_auto_connect(self, setup_id):
        """ULTRA FAST auto connection - get auth token only"""
        try:
            logger.info(f"🚀 ULTRA FAST auto connection for setup: {setup_id}")

            # Get setup data
            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"success": False, "message": f"Setup {setup_id} not found"}

            # Get OpenAlgo instance details
            http_port = setup_data.get('http_port')
            base_url = f"http://127.0.0.1:{http_port}"

            # FASTEST CONNECTION - Direct API calls only
            try:
                import requests

                # Step 1: Quick register (don't wait for response)
                try:
                    register_data = {
                        "username": setup_data.get('username'),
                        "password": setup_data.get('username')
                    }
                    requests.post(f"{base_url}/register", json=register_data, timeout=5)
                except:
                    pass  # User might already exist

                # DEPRECATED: This method returns fake responses
                logger.warning(f"⚠️ fast_auto_connect is deprecated and returns fake data")
                return {
                    "success": False,
                    "message": "fast_auto_connect is deprecated. Use step_3_register + step_4_connect_broker for real connections."
                }

            except Exception as e:
                logger.error(f"Auth error: {e}")
                return {
                    "success": False,
                    "message": f"Connection failed: {str(e)}"
                }

        except Exception as e:
            logger.error(f"❌ Error in ultra fast connection: {e}")
            return {"success": False, "message": f"Ultra fast connection failed: {str(e)}"}

    def fast_manual_connect(self, setup_id, trading_pin, totp_code=None):
        """Fast manual connection - simplified approach"""
        try:
            logger.info(f"🔗 Fast manual connection for setup: {setup_id}")

            # Get setup data
            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"success": False, "message": f"Setup {setup_id} not found"}

            # Get OpenAlgo instance details
            http_port = setup_data.get('http_port')

            # Quick service check - don't block on health check
            try:
                import subprocess
                # Just check if port is open, don't wait for HTTP response
                check_cmd = f"ss -tuln | grep :{http_port}"
                result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=3)

                if result.returncode != 0:
                    return {"success": False, "message": f"Algo service not running on port {http_port}"}

            except Exception as e:
                # If check fails, continue anyway - service might be starting
                logger.warning(f"Service check failed, continuing anyway: {e}")

            # Temporarily update trading pin in database for this connection
            original_pin = setup_data.get('trading_pin')
            try:
                # Update with user-provided PIN
                import sqlite3
                db_path = "/home/<USER>/algofactory/database/algofactory.db"

                with sqlite3.connect(db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        UPDATE user_brokers
                        SET trading_pin = ?
                        WHERE id = ?
                    """, (trading_pin, setup_id))
                    conn.commit()

                    # Ultra-fast manual connection
                logger.info(f"🔗 Manual connection with PIN: {trading_pin}")

                # ULTRA FAST manual connection - Direct API call
                import requests
                base_url = f"http://127.0.0.1:{http_port}"

                # Step 1: Quick register (don't wait for response)
                try:
                    register_data = {
                        "username": setup_data.get('username'),
                        "password": setup_data.get('username')
                    }
                    requests.post(f"{base_url}/register", json=register_data, timeout=5)
                except:
                    pass  # User might already exist

                # DEPRECATED: This method returns fake responses
                logger.warning(f"⚠️ fast_manual_connect is deprecated and returns fake data")
                return {
                    "success": False,
                    "message": "fast_manual_connect is deprecated. Use step_3_register + step_4_connect_broker for real connections."
                }

            finally:
                # Restore original PIN
                try:
                    with sqlite3.connect(db_path) as conn:
                        cursor = conn.cursor()
                        cursor.execute("""
                            UPDATE user_brokers
                            SET trading_pin = ?
                            WHERE id = ?
                        """, (original_pin, setup_id))
                        conn.commit()
                except Exception as e:
                    logger.error(f"Failed to restore original PIN: {e}")

        except Exception as e:
            logger.error(f"❌ Error in fast manual connection: {e}")
            return {"success": False, "message": f"Fast manual connection failed: {str(e)}"}

    def manual_broker_connect(self, setup_id, trading_pin, totp_code=None):
        """Manual broker connection with user-provided credentials"""
        try:
            logger.info(f"🔗 Manual broker connection for setup: {setup_id}")

            # Get setup data
            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"success": False, "message": f"Setup {setup_id} not found"}

            # Update status to connecting
            self.update_setup_status(setup_id, 'running', 'connecting')

            # For now, simulate manual connection process
            # In the future, this will redirect to broker login page

            # Step 3: Register user first
            register_result = self.step_3_register(setup_id)

            if register_result.get('status') != 'success':
                self.update_setup_status(setup_id, 'running', 'registration_failed')
                return {
                    "success": False,
                    "message": f"Registration failed: {register_result.get('message')}"
                }

            # Step 4: Connect broker with manual credentials
            connect_result = self.step_4_connect_broker(setup_id)

            if connect_result.get('status') == 'success':
                logger.info(f"✅ Manual broker connection successful for setup: {setup_id}")

                # Update status to connected
                self.update_setup_status(setup_id, 'running', 'connected')

                return {
                    "success": True,
                    "message": "Manual broker connection successful",
                    "data": connect_result
                }
            else:
                logger.error(f"❌ Manual broker connection failed for setup: {setup_id}")

                # Update status to connection failed
                self.update_setup_status(setup_id, 'running', 'connection_failed')

                return {
                    "success": False,
                    "message": connect_result.get('message', 'Manual connection failed')
                }

        except Exception as e:
            logger.error(f"❌ Error in manual broker connection: {e}")

            # Update status to error
            self.update_setup_status(setup_id, 'running', 'error')

            return {
                "success": False,
                "message": f"Manual connection failed: {str(e)}"
            }

    def delete_setup(self, setup_id):
        """Delete broker setup (alias for complete_delete_setup)"""
        return self.complete_delete_setup(setup_id)

    def complete_delete_setup(self, setup_id):
        """Complete deletion of broker setup including:
        - Stop systemd service
        - Delete service file
        - Delete instance folder
        - Create backup
        - Clean database entry
        """
        try:
            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"status": "error", "message": "Setup not found"}

            instance_name = self.get_instance_name(setup_data)
            service_name = self.get_service_name(setup_data)
            instance_path = self.instances_dir / instance_name

            print(f"🗑️ Starting complete deletion of setup: {setup_data['setup_name']}")
            print(f"   Instance: {instance_name}")
            print(f"   Service: {service_name}")

            # Step 1: Create backup before deletion
            backup_result = self.create_backup(setup_id)
            if backup_result.get('status') == 'success':
                print(f"✅ Backup created: {backup_result['data']['backup_file']}")
            else:
                print(f"⚠️ Backup failed: {backup_result.get('message', 'Unknown error')}")

            # Step 2: Delete systemd service completely
            print(f"🛑 Deleting systemd service: {service_name}")
            service_delete_result = self.delete_systemd_service(service_name)
            if service_delete_result.get('status') == 'success':
                print(f"✅ {service_delete_result['message']}")
            else:
                print(f"⚠️ Service deletion warning: {service_delete_result.get('message', 'Unknown error')}")

            # Step 4: Kill any remaining processes
            try:
                # Kill any python processes running from this instance
                subprocess.run(['/usr/bin/sudo', '/usr/bin/pkill', '-f', f'python.*{instance_name}'],
                             check=False, capture_output=True)

                # Kill any processes using the instance port
                port = 5000 + setup_id
                port_pids = subprocess.run(['/usr/bin/sudo', '/usr/bin/lsof', '-ti', f':{port}'],
                                         capture_output=True, text=True, check=False)
                if port_pids.stdout.strip():
                    for pid in port_pids.stdout.strip().split('\n'):
                        if pid.strip():
                            subprocess.run(['/usr/bin/sudo', '/usr/bin/kill', '-9', pid.strip()],
                                         check=False, capture_output=True)
                print(f"✅ All processes for {instance_name} terminated")
            except Exception as e:
                print(f"⚠️ Error killing processes: {e}")

            # Step 5: Delete instance folder
            if instance_path.exists():
                try:
                    shutil.rmtree(instance_path)
                    print(f"✅ Instance folder deleted: {instance_path}")
                except Exception as e:
                    print(f"⚠️ Error deleting instance folder: {e}")
            else:
                print(f"ℹ️ Instance folder not found: {instance_path}")

            # Step 6: Completely remove database entry
            try:
                import sqlite3
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # Actually delete the entry for clean system
                cursor.execute("DELETE FROM user_brokers WHERE id = ?", (setup_id,))

                conn.commit()
                conn.close()
                print(f"✅ Database entry completely removed")
            except Exception as e:
                print(f"⚠️ Error deleting from database: {e}")

            # Step 7: Clean up any remaining systemd files
            try:
                service_file = f"/etc/systemd/system/{service_name}.service"
                if os.path.exists(service_file):
                    subprocess.run(['/usr/bin/sudo', '/usr/bin/rm', '-f', service_file],
                                 check=False, capture_output=True)
                    print(f"✅ Systemd service file removed: {service_file}")

                # Reload systemd daemon
                subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'daemon-reload'],
                             check=False, capture_output=True)
                print(f"✅ Systemd daemon reloaded")
            except Exception as e:
                print(f"⚠️ Error cleaning systemd files: {e}")

            # Step 8: Final cleanup - remove any temp files
            try:
                temp_patterns = [
                    f"/tmp/*{instance_name}*",
                    f"/tmp/*{setup_id}*"
                ]
                for pattern in temp_patterns:
                    subprocess.run(['/usr/bin/sudo', '/usr/bin/rm', '-rf'] + [pattern],
                                 shell=True, check=False, capture_output=True)
                print(f"✅ Temporary files cleaned")
            except Exception as e:
                print(f"⚠️ Error cleaning temp files: {e}")

            print(f"🎉 Complete deletion successful for setup: {setup_data['setup_name']}")

            return {
                "status": "success",
                "message": f"Setup '{setup_data['setup_name']}' completely deleted",
                "data": {
                    "setup_id": setup_id,
                    "instance_name": instance_name,
                    "service_name": service_name,
                    "backup_created": backup_result.get('status') == 'success'
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to delete setup: {str(e)}"}

    def get_next_available_port(self):
        """Get next available port for OpenAlgo instance"""
        used_ports = []
        
        # Check existing instances
        if self.instances_dir.exists():
            for instance_dir in self.instances_dir.iterdir():
                if instance_dir.is_dir():
                    env_file = instance_dir / ".env"
                    if env_file.exists():
                        with open(env_file, 'r') as f:
                            content = f.read()
                            for line in content.split('\n'):
                                if line.startswith('PORT='):
                                    port = int(line.split('=')[1])
                                    used_ports.append(port)
        
        # Find next available port
        port = self.base_port
        while port in used_ports:
            port += 1
        
        return port

    def get_next_instance_number(self, username, broker_name):
        """Get the next available instance number for user-broker combination"""

        try:
            import sqlite3

            # Connect to our database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get highest instance number for this user-broker
            cursor.execute("""
                SELECT MAX(instance_number) FROM user_brokers
                WHERE username = ? AND broker_name = ?
            """, (username, broker_name))

            result = cursor.fetchone()
            max_instance = result[0] if result[0] is not None else 0

            conn.close()

            return max_instance + 1

        except Exception as e:
            print(f"⚠️ Failed to get next instance number: {e}")
            return 1  # Default to 1 if error

    def create_instance(self, setup_id):
        """Complete instance creation - folder, clone, configure"""

        try:
            # Get setup details with user information
            setup = self.get_setup_data_with_user(setup_id)
            if not setup:
                return {"status": "error", "message": "Setup not found"}

            # Get next instance number if not set
            if not setup.get('instance_number'):
                instance_number = self.get_next_instance_number(setup.get('username', '1000'), setup['broker_name'])
            else:
                instance_number = setup['instance_number']

            # Create instance name: username-broker-instance format
            instance_name = f"{setup.get('username', '1000')}-{setup['broker_name']}-{instance_number}"
            instance_path = self.instances_dir / instance_name

            print(f"🔧 Creating instance: {instance_name}")

            # Step 1: Create instance folder
            if instance_path.exists():
                print(f"📁 Instance folder already exists: {instance_path}")
            else:
                instance_path.mkdir(parents=True, exist_ok=True)
                print(f"✅ Created instance folder: {instance_path}")

            # Step 2: Clone OpenAlgo if not already present
            if not (instance_path / "app.py").exists():
                print(f"📥 Cloning OpenAlgo...")
                clone_success = self.clone_openalgo_source(instance_path)
                if not clone_success:
                    return {"status": "error", "message": "Failed to clone OpenAlgo"}
            else:
                print(f"✅ OpenAlgo already present in instance")

            # Step 3: Configure .env file
            print(f"⚙️ Configuring environment...")
            config_success = self.configure_env_file(instance_path, setup)
            if not config_success:
                return {"status": "error", "message": "Failed to configure environment"}

            print(f"✅ Instance setup completed: {instance_name}")

            return {
                "status": "success",
                "message": "Instance created successfully",
                "instance_name": instance_name,
                "instance_path": str(instance_path)
            }

        except Exception as e:
            print(f"❌ Instance creation failed: {e}")
            return {"status": "error", "message": str(e)}

    def create_instance_folder(self, setup_id, broker_name, setup_name):
        """Create OpenAlgo instance folder for broker setup"""
        
        # Get user data for multi-user naming
        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Create unique instance name with multi-user format: username-algo-broker-setupid
        instance_name = self.get_instance_name(setup_data)
        instance_path = self.instances_dir / instance_name
        
        # Remove existing instance if it exists
        if instance_path.exists():
            shutil.rmtree(instance_path)

        # Don't create the directory - let git clone create it
        print(f"✅ Prepared instance path: {instance_path}")
        return instance_path
    
    def clone_openalgo_source(self, instance_path):
        """Clone OpenAlgo source code directly into instance folder"""

        try:
            print(f"🔍 DEBUG: Starting clone process...")
            print(f"🔍 DEBUG: Target path: {instance_path}")
            print(f"🔍 DEBUG: Path exists: {instance_path.exists()}")
            print(f"🔍 DEBUG: Parent directory: {instance_path.parent}")
            print(f"🔍 DEBUG: Parent exists: {instance_path.parent.exists()}")

            # Ensure parent directory exists
            instance_path.parent.mkdir(parents=True, exist_ok=True)

            # Remove existing directory if it exists
            if instance_path.exists():
                print(f"🗑️ Removing existing directory: {instance_path}")
                shutil.rmtree(instance_path)

            # Clone OpenAlgo repository directly into instance folder
            print(f"📥 Cloning OpenAlgo from GitHub to: {instance_path}")
            print(f"🔍 DEBUG: Git command: /usr/bin/git clone https://github.com/marketcalls/openalgo.git {instance_path}")

            result = subprocess.run([
                '/usr/bin/git', 'clone',
                'https://github.com/marketcalls/openalgo.git',
                str(instance_path)
            ], capture_output=True, text=True, timeout=120)

            print(f"🔍 DEBUG: Git return code: {result.returncode}")
            print(f"🔍 DEBUG: Git STDOUT: {result.stdout}")
            print(f"🔍 DEBUG: Git STDERR: {result.stderr}")

            if result.returncode != 0:
                print(f"❌ Git clone failed with return code {result.returncode}")
                raise Exception(f"Git clone failed: {result.stderr}")

            print(f"✅ OpenAlgo cloned successfully to: {instance_path}")

            # Verify the clone was successful by checking for key files
            app_py_path = instance_path / "app.py"
            print(f"🔍 DEBUG: Checking for app.py at: {app_py_path}")
            print(f"🔍 DEBUG: app.py exists: {app_py_path.exists()}")

            if not app_py_path.exists():
                # List what files are actually there
                if instance_path.exists():
                    files = list(instance_path.iterdir())
                    print(f"🔍 DEBUG: Files in directory: {[f.name for f in files[:10]]}")
                raise Exception("Clone verification failed: app.py not found")

            return True

        except Exception as e:
            print(f"❌ Failed to clone OpenAlgo: {e}")
            import traceback
            traceback.print_exc()
            return False

    def configure_env_file(self, instance_path, setup_data):
        """Configure .env file with broker credentials, user data, and XTS support"""

        env_file = instance_path / ".env"
        sample_env_file = instance_path / ".sample.env"

        # Copy .sample.env to .env first
        if sample_env_file.exists():
            shutil.copy2(sample_env_file, env_file)
            print(f"📋 Copied .sample.env to .env")
        else:
            print(f"⚠️ .sample.env not found, creating basic .env")
            with open(env_file, 'w') as f:
                f.write("ENV_CONFIG_VERSION = '1.0.1'\n")

        # Read the .env content
        with open(env_file, 'r') as f:
            env_content = f.read()

        # Calculate ports based on setup_id
        setup_id = setup_data['id']
        port = 5000 + setup_id              # HTTP port: 5000 + setup_id
        websocket_port = 8000 + setup_id    # WebSocket port: 8000 + setup_id
        zmq_port = 6000 + setup_id          # ZMQ port: 6000 + setup_id

        # Determine broker type (XTS or standard)
        broker_type = self.get_broker_type(setup_data['broker_name'])

        # Base configurations - use calculated ports for all configurations
        modifications = {
            'BROKER_API_KEY': setup_data['broker_api_key'],
            'BROKER_API_SECRET': setup_data['broker_api_secret'],
            'REDIRECT_URL': f'http://127.0.0.1:{port}/{setup_data["broker_name"]}/callback',
            'HOST_SERVER': f'http://127.0.0.1:{port}',
            'FLASK_PORT': str(port),  # HTTP port
            'WEBSOCKET_PORT': str(websocket_port),  # WebSocket port
            'WEBSOCKET_URL': f'ws://localhost:{websocket_port}',  # WebSocket URL
            'ZMQ_PORT': str(zmq_port),  # ZMQ port
            'CORS_ALLOWED_ORIGINS': f'http://127.0.0.1:{port}',  # CORS for current port
            'CSRF_ENABLED': 'FALSE',  # Disable CSRF for automation
            # Increase rate limits by 10X for automation (prevents login failures)
            'LOGIN_RATE_LIMIT_MIN': '50 per minute',  # 5 -> 50 (10X)
            'LOGIN_RATE_LIMIT_HOUR': '250 per hour',  # 25 -> 250 (10X)
            'API_RATE_LIMIT': '100 per second',  # 10 -> 100 (10X)
        }

        # Add XTS broker market API keys if needed
        if broker_type == 'xts':
            print(f"🔧 Configuring XTS broker with market API keys")
            modifications.update({
                'BROKER_API_KEY_MARKET': setup_data.get('broker_api_key_market', ''),
                'BROKER_API_SECRET_MARKET': setup_data.get('broker_api_secret_market', '')
            })

        # Note: AlgoFactory data is now stored in our database, not in .env files

        # Update .env content
        lines = env_content.split('\n')
        updated_lines = []
        modified_keys = set()

        for line in lines:
            if '=' in line and not line.strip().startswith('#'):
                key = line.split('=')[0].strip()
                if key in modifications:
                    updated_lines.append(f"{key} = '{modifications[key]}'")
                    modified_keys.add(key)
                else:
                    updated_lines.append(line)
            else:
                updated_lines.append(line)

        # Clean .env file - no AlgoFactory data added

        # Write updated .env file
        with open(env_file, 'w') as f:
            f.write('\n'.join(updated_lines))

        print(f"✅ Configured .env file for {broker_type.upper()} broker: {env_file}")
        print(f"🚀 Rate limits increased 10X for automation: LOGIN=50/min, API=100/sec")
        return port

    def fix_socketio_async_mode(self, instance_path):
        """Skip SocketIO setup - let OpenAlgo run normally without auto-binding"""
        extensions_file = instance_path / "extensions.py"

        print(f"⚠️ Skipping SocketIO auto-setup - OpenAlgo will run normally without auto-binding")
        print(f"📝 Extensions file: {extensions_file}")
        print(f"🔧 Manual start/stop control enabled - no automatic SocketIO binding")

        # Do not modify extensions.py - let OpenAlgo run with its default configuration
        # This prevents automatic SocketIO startup and binding issues

    def get_broker_type(self, broker_name):
        """Determine if broker is XTS type or standard"""
        xts_brokers = ['fivepaisaxts', 'compositedge', 'iifl']  # Add more XTS brokers as needed
        return 'xts' if broker_name in xts_brokers else 'standard'

    def get_port_from_env_content(self, env_content):
        """Get port from .env file content, return OpenAlgo default if not found"""
        for line in env_content.split('\n'):
            if 'FLASK_PORT' in line and '=' in line:
                try:
                    port_str = line.split('=')[1].strip().strip("'\"")
                    return int(port_str)
                except:
                    pass
        # Return OpenAlgo default port
        return 5000

    def install_dependencies_shared_venv(self, instance_path):
        """Install OpenAlgo dependencies using shared virtual environment"""

        try:
            print(f"📦 Installing dependencies using shared virtual environment...")

            # Use the shared virtual environment
            venv_python = self.base_dir / "env/bin/python"
            venv_pip = self.base_dir / "env/bin/pip"

            if not venv_python.exists():
                print(f"❌ Shared virtual environment not found: {venv_python}")
                return False

            # Check if requirements.txt exists
            requirements_file = instance_path / "requirements.txt"
            if requirements_file.exists():
                # Install requirements using shared venv
                result = subprocess.run([
                    str(venv_pip), 'install', '-r', str(requirements_file)
                ], capture_output=True, text=True, cwd=str(instance_path))

                if result.returncode != 0:
                    print(f"⚠️ Warning: pip install failed: {result.stderr}")
                    return False
                else:
                    print(f"✅ Dependencies installed successfully in shared venv")
                    return True
            else:
                print(f"⚠️ No requirements.txt found, skipping dependency installation")
                return True

        except Exception as e:
            print(f"⚠️ Warning: Failed to install dependencies: {e}")
            return False

    def install_dependencies_instance_venv(self, instance_path):
        """Install OpenAlgo dependencies using instance-specific virtual environment"""

        try:
            print(f"📦 Creating virtual environment for {instance_path.name}...")

            # Create instance-specific virtual environment
            venv_path = instance_path / "venv"

            # Remove existing venv if it exists
            if venv_path.exists():
                shutil.rmtree(venv_path)

            # Create new virtual environment
            result = subprocess.run([
                "python3", "-m", "venv", str(venv_path)
            ], capture_output=True, text=True)

            if result.returncode != 0:
                print(f"❌ Failed to create virtual environment: {result.stderr}")
                return False

            # Install requirements in the new venv
            venv_python = venv_path / "bin" / "python"
            venv_pip = venv_path / "bin" / "pip"
            requirements_file = instance_path / "requirements.txt"

            if not requirements_file.exists():
                print(f"⚠️ No requirements.txt found in {instance_path}")
                return True

            # Upgrade pip first
            subprocess.run([str(venv_pip), "install", "--upgrade", "pip"], capture_output=True)

            # Install requirements
            result = subprocess.run([
                str(venv_pip), "install", "-r", str(requirements_file)
            ], capture_output=True, text=True, cwd=str(instance_path))

            if result.returncode == 0:
                print(f"✅ Dependencies installed successfully in instance venv")
                return True
            else:
                print(f"❌ Failed to install dependencies: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ Dependency installation failed: {e}")
            return False

    def start_openalgo_instance(self, instance_path, port, setup_data):
        """Start OpenAlgo instance with Gunicorn"""

        try:
            print(f"🚀 Starting OpenAlgo instance on port {port}...")

            # Create Gunicorn configuration
            gunicorn_conf = instance_path / "gunicorn.conf.py"
            gunicorn_config = f"""# Gunicorn configuration for {setup_data['setup_name']}
bind = "0.0.0.0:{port}"
workers = 1
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
daemon = True
pidfile = "{instance_path}/gunicorn.pid"
accesslog = "{instance_path}/access.log"
errorlog = "{instance_path}/error.log"
loglevel = "info"
"""

            with open(gunicorn_conf, 'w') as f:
                f.write(gunicorn_config)

            # Start Gunicorn process
            cmd = [
                'gunicorn',
                '-c', str(gunicorn_conf),
                'app:app'  # Assuming main file is app.py
            ]

            result = subprocess.run(
                cmd,
                cwd=str(instance_path),
                capture_output=True,
                text=True
            )

            if result.returncode != 0:
                print(f"⚠️ Gunicorn start warning: {result.stderr}")

            # Wait a moment for startup
            time.sleep(2)

            # Check if process is running
            if self.check_instance_running(port):
                print(f"✅ Algo instance started successfully on port {port}")
                return True
            else:
                print(f"⚠️ Algo instance may not be running properly")
                return False

        except Exception as e:
            print(f"❌ Failed to start OpenAlgo instance: {e}")
            return False

    def check_instance_running(self, port):
        """Check if OpenAlgo instance is running on specified port"""

        try:
            response = requests.get(f"http://127.0.0.1:{port}", timeout=5)
            return response.status_code == 200
        except:
            return False

    def get_actual_running_port(self, setup_id):
        """Get the actual port where the service is running from database"""
        try:
            import sqlite3
            db_path = "/home/<USER>/algofactory/database/algofactory.db"

            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT http_port, setup_status
                    FROM user_brokers
                    WHERE id = ?
                """, (setup_id,))

                result = cursor.fetchone()
                if result:
                    http_port, setup_status = result
                    if http_port:
                        print(f"🔍 Found HTTP port {http_port} from database for setup {setup_id}")

                        # Verify the port is actually running
                        if self.check_instance_running(http_port):
                            print(f"✅ Confirmed port {http_port} is running")
                            return http_port
                        else:
                            print(f"⚠️ Port {http_port} from database is not responding")
                    else:
                        print(f"⚠️ No HTTP port stored in database for setup {setup_id}")
                else:
                    print(f"⚠️ Setup {setup_id} not found in database")

            # Fallback: try calculated port
            calculated_port = 5000 + setup_id
            print(f"🔄 Trying fallback calculated port: {calculated_port}")
            if self.check_instance_running(calculated_port):
                print(f"✅ Fallback port {calculated_port} is working")
                return calculated_port

            print(f"❌ No working port found for setup {setup_id}")
            return None

        except Exception as e:
            print(f"❌ Error getting port from database: {e}")
            return None

    def auto_register_user(self, port, setup_data):
        """Auto-register user in Algo"""

        try:
            print(f"👤 Auto-registering user in Algo...")

            algo_url = f"http://127.0.0.1:{port}"
            session = requests.Session()

            # Wait for Algo to fully start (reduced for faster connection)
            print("⏳ Waiting for Algo to initialize...")
            time.sleep(3)

            # Check if Algo is responding
            for attempt in range(5):
                try:
                    response = session.get(algo_url, timeout=5)
                    if response.status_code == 200:
                        break
                    print(f"⏳ Attempt {attempt + 1}: Algo not ready yet...")
                    time.sleep(2)
                except:
                    print(f"⏳ Attempt {attempt + 1}: Algo not responding yet...")
                    time.sleep(2)
            else:
                print("❌ Algo instance not responding after multiple attempts")
                return False

            # Get setup page (initial setup for OpenAlgo)
            response = session.get(f"{algo_url}/setup", timeout=10)
            if response.status_code != 200:
                print(f"❌ Cannot access setup page: {response.status_code}")
                print(f"Response text: {response.text[:200]}...")
                return False

            # Setup admin user with standard password pattern (CSRF disabled)
            username = setup_data.get('user_username', setup_data.get('username', '1000'))
            user_password = f"algofactory{username}"  # Standard password pattern
            setup_data_form = {
                "username": username,
                "email": setup_data['email'],
                "password": user_password
            }
            print(f"🔐 Setting up user with username: {username} and password: algofactory{username}")

            setup_response = session.post(
                f"{algo_url}/setup",
                data=setup_data_form,
                timeout=10
            )

            if setup_response.status_code == 200 or "login" in setup_response.url:
                print("✅ Admin user setup completed successfully in Algo")
                return True
            else:
                print(f"❌ Setup failed: {setup_response.status_code}")
                print(f"Response text: {setup_response.text[:200]}...")
                return False

        except Exception as e:
            print(f"❌ Auto-registration failed: {e}")
            return False

    def auto_login(self, port, setup_data):
        """Auto-login to OpenAlgo and return session"""

        try:
            print(f"🔐 Auto-logging into OpenAlgo...")

            openalgo_url = f"http://127.0.0.1:{port}"
            session = requests.Session()

            # Get login page
            login_page = session.get(f"{openalgo_url}/auth/login", timeout=10)
            if login_page.status_code != 200:
                print(f"❌ Cannot access login page: {login_page.status_code}")
                return None

            # Submit login (CSRF disabled) - Use standard password pattern
            # Use the actual username from users table, not the one from user_brokers
            username = setup_data.get('user_username', setup_data.get('username', '1000'))  # This is from users table via JOIN
            user_password = f"algofactory{username}"  # Standard password pattern
            login_data = {
                "username": username,
                "password": user_password
            }
            print(f"🔐 Logging in with username: {username} and password: algofactory{username}")

            # Handle rate limiting with retry mechanism
            for attempt in range(3):
                if attempt > 0:
                    print(f"⏳ Retrying login (attempt {attempt + 1}/3) after rate limit...")
                    time.sleep(5)  # Wait 5 seconds between attempts

                login_response = session.post(
                    f"{openalgo_url}/auth/login",
                    data=login_data,
                    allow_redirects=True,
                    timeout=10
                )

                if login_response.status_code == 200:
                    # Check if we're redirected away from login page (successful login)
                    if "login" not in login_response.url or "dashboard" in login_response.url or "broker" in login_response.url:
                        print("✅ OpenAlgo login successful")
                        return session
                    else:
                        print(f"⚠️ Login response 200 but still on login page: {login_response.url}")
                        continue
                elif login_response.status_code == 429:
                    print(f"⚠️ Rate limited (429), waiting before retry...")
                    continue
                else:
                    print(f"❌ Login failed: {login_response.status_code}")
                    return None

            print("❌ Login failed after all retry attempts")
            return None

        except Exception as e:
            print(f"❌ Auto-login failed: {e}")
            return None

    def get_setup_connection_status(self, setup_id):
        """Get connection status from our database"""
        try:
            import sqlite3
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT connection_status FROM user_brokers WHERE id = ?", (setup_id,))
                row = cursor.fetchone()
                return row[0] if row else None
        except Exception as e:
            print(f"⚠️ Failed to get setup status: {e}")
            return None

    def check_existing_connection(self, port, setup_data, session):
        """Ultra-fast check if broker is already connected"""
        try:
            print(f"🔍 Ultra-fast connection check...")

            # FASTEST: Check OpenAlgo database directly for auth tokens
            instance_name = self.get_instance_name(setup_data)
            instance_path = self.instances_dir / instance_name
            algo_db_path = instance_path / "db" / "openalgo.db"

            if algo_db_path.exists():
                import sqlite3
                with sqlite3.connect(algo_db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.cursor()

                    # Check if auth tokens exist and are not revoked
                    cursor.execute("SELECT auth, is_revoked FROM auth WHERE auth != '' AND auth IS NOT NULL AND (is_revoked = 0 OR is_revoked IS NULL) LIMIT 1")
                    auth_row = cursor.fetchone()

                    if auth_row and auth_row['auth'] and len(auth_row['auth']) > 10:
                        print("✅ Found valid auth tokens in OpenAlgo database")
                        return True
                    else:
                        print("❌ No valid auth tokens found (empty, revoked, or missing)")
                        return False

            print("❌ No valid connection found - proceeding with authentication")
            return False

        except Exception as e:
            print(f"⚠️ Fast connection check failed: {e} - proceeding with authentication")
            return False

    def auto_broker_auth(self, port, setup_data, session):
        """Auto-authenticate broker in OpenAlgo"""

        try:
            print(f"🏦 Auto-authenticating {setup_data['broker_name']} broker...")

            # FAST CHECK: See if already connected (2-3 seconds)
            # BUT ONLY if our database also shows connected
            our_db_status = self.get_setup_connection_status(setup_data.get('id'))
            if our_db_status == 'connected' and self.check_existing_connection(port, setup_data, session):
                print("⚡ Broker already connected - skipping authentication!")
                return True
            elif our_db_status == 'disconnected':
                print("🔄 Database shows disconnected - forcing full authentication")
            else:
                print("🔄 No existing connection or status mismatch - proceeding with authentication")

            print(f"🔄 No existing connection - proceeding with authentication...")
            openalgo_url = f"http://127.0.0.1:{port}"

            # Navigate to broker selection page
            broker_page = session.get(f"{openalgo_url}/auth/broker", timeout=10)
            if broker_page.status_code != 200:
                print(f"❌ Cannot access broker page: {broker_page.status_code}")
                return False

            print(f"✅ Accessed broker selection page")

            # Find and click the Angel broker connect button
            # Look for the connect URL in the page
            if "angel" in broker_page.text.lower():
                print(f"✅ Found Angel broker option")

                # Try to find the connect link for Angel
                import re
                connect_match = re.search(r'href="([^"]*angel[^"]*)"', broker_page.text)
                if connect_match:
                    connect_path = connect_match.group(1)
                    if not connect_path.startswith('http'):
                        connect_url = f"{openalgo_url}{connect_path}"
                    else:
                        connect_url = connect_path
                else:
                    # Fallback to standard Angel callback URL
                    connect_url = f"{openalgo_url}/angel/callback"

                # Navigate directly to Angel callback URL to get the form
                angel_auth_url = f"{openalgo_url}/angel/callback"
                print(f"🔗 Getting Angel authentication form from: {angel_auth_url}")

                # GET request to get the authentication form (faster timeout)
                auth_form_page = session.get(angel_auth_url, timeout=5)

                if auth_form_page.status_code != 200:
                    print(f"❌ Cannot access Angel auth form: {auth_form_page.status_code}")
                    return False

                print(f"✅ Retrieved Angel authentication form")

                # The form submits back to the same URL via POST
                submit_url = angel_auth_url
            else:
                print(f"❌ Angel broker not found on broker page")
                return False

            # Generate TOTP
            totp_code = self.generate_totp(setup_data['totp_secret'])
            if not totp_code:
                print("❌ Failed to generate TOTP")
                return False

            print(f"🔐 Generated TOTP: {totp_code}")
            print(f"🔑 Using Client ID: {setup_data['broker_client_id']}")

            # Submit broker authentication (CSRF disabled)
            # Use the exact field names from OpenAlgo Angel form
            auth_data = {
                "clientid": setup_data['broker_client_id'],  # OpenAlgo uses 'clientid' not 'client_id'
                "pin": setup_data['trading_pin'],
                "totp": totp_code
            }

            print(f"🚀 Submitting broker authentication to: {submit_url}")
            print(f"📝 Auth data: {auth_data}")

            auth_response = session.post(
                submit_url,
                data=auth_data,
                allow_redirects=True,
                timeout=15  # Reduced timeout for faster response
            )

            print(f"📡 Auth response status: {auth_response.status_code}")
            print(f"📡 Auth response URL: {auth_response.url}")
            print(f"📄 Response preview: {auth_response.text[:500]}...")

            if auth_response.status_code == 200:
                # Check if authentication was successful
                if "success" in auth_response.text.lower() or "authenticated" in auth_response.text.lower():
                    print("✅ Broker authentication successful")

                    # Wait for OpenAlgo to process and store tokens (reduced)
                    import time
                    time.sleep(2)

                    # Retrieve and save tokens from Algo database using updated method
                    tokens = self.retrieve_and_save_tokens(port, setup_data)
                    if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):
                        print("✅ OpenAlgo API key retrieved and saved successfully")
                        return True
                    else:
                        print("⚠️ Authentication appeared successful but no tokens found in database")
                        print("🔍 This indicates the broker authentication didn't complete properly in Algo")
                        return False
                else:
                    print("❌ Broker authentication failed - no success indication")
                    return False
            else:
                print(f"❌ Broker authentication failed: {auth_response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Auto-broker authentication failed: {e}")

            # If it's a timeout, check if tokens already exist
            if "timeout" in str(e).lower() or "timed out" in str(e).lower():
                print("⚠️ Broker authentication timed out, but checking if tokens already exist...")

                # Even if the API call timed out, check if tokens already exist
                try:
                    tokens = self.retrieve_and_save_tokens(port, setup_data)
                    if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):
                        print("✅ Found existing tokens! Broker was already authenticated")
                        return True
                    else:
                        print("❌ No existing tokens found")
                        return False
                except Exception as token_error:
                    print(f"❌ Token retrieval also failed: {token_error}")
                    return False

            return False

    def generate_totp(self, totp_secret):
        """Generate TOTP code"""

        try:
            totp = pyotp.TOTP(totp_secret)
            return totp.now()
        except Exception as e:
            print(f"❌ TOTP generation failed: {e}")
            return None

    def retrieve_and_save_tokens(self, port, setup_data):
        """Retrieve authentication tokens from Algo database and save decrypted keys to our database"""

        try:
            print(f"🔍 Retrieving tokens from Algo database...")

            # Algo database path (in db subfolder)
            instance_name = self.get_instance_name(setup_data)
            instance_path = self.instances_dir / instance_name
            algo_db_path = instance_path / "db" / "openalgo.db"  # Still named openalgo.db in the repo

            if not algo_db_path.exists():
                print(f"❌ Algo database not found: {algo_db_path}")
                return None

            # Connect to Algo database
            conn = sqlite3.connect(algo_db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Check auth table for broker authentication tokens
            username = setup_data.get('user_username', setup_data.get('username', '1000'))
            cursor.execute("SELECT * FROM auth WHERE user_id = ? OR name = ? LIMIT 1", (username, username))
            auth_row = cursor.fetchone()

            # Check symtoken table for master contracts (optimized for speed)
            print(f"📊 Checking symbol download status...")
            symbol_download_complete = False
            max_wait_time = 30  # Reduced wait time for faster response
            wait_start = time.time()

            # First quick check - if symbols already exist, don't wait
            cursor.execute("SELECT COUNT(*) as count FROM symtoken")
            symtoken_count = cursor.fetchone()['count']

            if symtoken_count > 100000:  # Angel typically has 120k+ symbols
                symbol_download_complete = True
                print(f"✅ Symbol download already complete: {symtoken_count} symbols")
            else:
                print(f"⏳ Symbol download in progress: {symtoken_count} symbols")
                # Only wait if symbols are actively downloading
                while not symbol_download_complete and (time.time() - wait_start) < max_wait_time:
                    time.sleep(3)  # Reduced check interval
                    cursor.execute("SELECT COUNT(*) as count FROM symtoken")
                    symtoken_count = cursor.fetchone()['count']

                    if symtoken_count > 100000:
                        symbol_download_complete = True
                        print(f"✅ Symbol download complete: {symtoken_count} symbols")
                    else:
                        print(f"⏳ Symbol download progress: {symtoken_count} symbols")

            if not symbol_download_complete:
                print(f"⚠️ Symbol download timeout after {max_wait_time}s: {symtoken_count} symbols (continuing anyway)")

            # Check api_keys table for API keys
            cursor.execute("SELECT * FROM api_keys WHERE user_id = ? LIMIT 1", (username,))
            api_key_row = cursor.fetchone()

            conn.close()

            # Decrypt and save API keys to our database
            decrypted_tokens = self.decrypt_and_save_api_keys(auth_row, api_key_row, setup_data, symtoken_count)

            if decrypted_tokens:
                print(f"✅ OpenAlgo setup complete: Broker_Auth={decrypted_tokens['has_auth_token']}, API_Key={decrypted_tokens['has_openalgo_api_key']}, Symbols={symtoken_count}")
                return decrypted_tokens
            else:
                print("❌ No authentication tokens found in Algo database")
                return None

        except Exception as e:
            print(f"❌ Token retrieval failed: {e}")
            return None

    # REMOVED: step_4_connect_broker_manual_init() - Duplicate method, use run_complete_manual_setup() instead

    # REMOVED: step_4_connect_broker_manual_verify() - Duplicate method, use connect_with_manual_otp() instead

    # REMOVED: step_4_connect_broker_with_credentials() - Duplicate method #1

    # REMOVED: step_4_connect_broker_with_terminal_totp() - Legacy method, not needed

    def decrypt_and_save_api_keys(self, auth_row, api_key_row, setup_data, symbol_count):
        """Decrypt OpenAlgo API keys from Algo database and save to our database"""

        try:
            from cryptography.fernet import Fernet
            import os

            # Get encryption keys from Algo .env file
            instance_name = self.get_instance_name(setup_data)
            instance_path = self.instances_dir / instance_name
            env_file = instance_path / ".env"

            app_key = None
            api_key_pepper = None

            if env_file.exists():
                with open(env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line.startswith('APP_KEY ='):
                            app_key = line.split('=', 1)[1].strip().strip("'\"")
                        elif line.startswith('API_KEY_PEPPER ='):
                            api_key_pepper = line.split('=', 1)[1].strip().strip("'\"")

            if not app_key or not api_key_pepper:
                print(f"❌ Encryption keys not found in .env file: APP_KEY={bool(app_key)}, API_KEY_PEPPER={bool(api_key_pepper)}")
                return None

            print(f"✅ Found encryption keys in .env file")

            # Initialize Fernet cipher using OpenAlgo's method
            from cryptography.fernet import Fernet
            from cryptography.hazmat.primitives import hashes
            from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
            import base64

            # Use OpenAlgo's encryption method
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'openalgo_static_salt',  # OpenAlgo's static salt
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(api_key_pepper.encode()))
            cipher = Fernet(key)

            # Decrypt OpenAlgo API key (this is what we need for trading)
            decrypted_openalgo_api_key = None

            if api_key_row:
                try:
                    if 'api_key_encrypted' in api_key_row.keys() and api_key_row['api_key_encrypted']:
                        decrypted_openalgo_api_key = cipher.decrypt(api_key_row['api_key_encrypted'].encode()).decode()
                        print(f"✅ OpenAlgo API key decrypted successfully")
                except Exception as e:
                    print(f"⚠️ OpenAlgo API key decryption failed: {e}")

            # Check broker authentication status (auth_row indicates broker is connected)
            broker_auth_status = "connected" if auth_row else "failed"

            # Get port information from .env
            websocket_port, zmq_port = self.get_port_info_from_env(instance_path)
            http_port = 5000 + setup_data['id']  # Calculate HTTP port

            # Save to our database
            self.save_decrypted_tokens_to_database(
                setup_data['id'],
                broker_auth_status,  # Auth status (broker connected)
                decrypted_openalgo_api_key,  # OpenAlgo API key for trading
                app_key,  # APP_KEY for future use
                api_key_pepper,  # API_KEY_PEPPER for future use
                symbol_count,
                websocket_port,  # WebSocket port
                zmq_port,  # ZMQ port
                http_port  # HTTP port
            )

            # Test OpenAlgo API if we have the key
            api_test_result = None
            if decrypted_openalgo_api_key:
                api_test_result = self.test_openalgo_api(setup_data, decrypted_openalgo_api_key)

            # Return status
            return {
                "has_auth_token": bool(auth_row),  # Broker authentication
                "has_openalgo_api_key": bool(decrypted_openalgo_api_key),  # OpenAlgo API key
                "symbol_count": symbol_count,
                "auth_token_status": broker_auth_status,
                "symbol_token_status": "connected" if symbol_count > 100000 else "downloading" if symbol_count > 0 else "failed",
                "api_test_result": api_test_result
            }

        except Exception as e:
            print(f"❌ Token decryption failed: {e}")
            return None

    def get_port_info_from_env(self, instance_path):
        """Get WebSocket and ZMQ port information from .env file"""

        try:
            env_file = instance_path / ".env"
            websocket_port = None
            zmq_port = None

            if env_file.exists():
                with open(env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line.startswith('WEBSOCKET_PORT='):
                            websocket_port = int(line.split('=', 1)[1].strip().strip("'\""))
                        elif line.startswith('ZMQ_PORT='):
                            zmq_port = int(line.split('=', 1)[1].strip().strip("'\""))

            print(f"📡 Port info: WebSocket={websocket_port}, ZMQ={zmq_port}")
            return websocket_port, zmq_port

        except Exception as e:
            print(f"⚠️ Failed to get port info: {e}")
            return None, None

    def save_decrypted_tokens_to_database(self, setup_id, auth_status, openalgo_api_key, app_key, api_key_pepper, symbol_count, websocket_port, zmq_port, http_port=None):
        """Save decrypted OpenAlgo API key, encryption keys, and port information to our database"""

        try:
            import sqlite3
            from datetime import datetime

            # Connect to our database
            db_path = "/home/<USER>/algofactory/database/algofactory.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Determine status indicators
            symbol_status = "connected" if symbol_count > 100000 else "downloading" if symbol_count > 0 else "failed"

            # Update broker setup with OpenAlgo API key, encryption keys, and port information
            # Calculate HTTP port if not provided
            if http_port is None:
                http_port = 5000 + setup_id

            cursor.execute("""
                UPDATE user_brokers
                SET algo_api_key = ?,
                    algo_app_key = ?,
                    algo_api_key_pepper = ?,
                    http_port = ?,
                    websocket_port = ?,
                    zmq_port = ?,
                    auth_token_status = ?,
                    symbol_token_status = ?,
                    symbol_count = ?,
                    last_connected_at = ?,
                    status = 'connected',
                    updated_at = ?
                WHERE id = ?
            """, (
                openalgo_api_key,  # OpenAlgo API key for trading
                app_key,           # APP_KEY for future decryption
                api_key_pepper,    # API_KEY_PEPPER for future decryption
                http_port,         # HTTP port for web interface
                websocket_port,    # WebSocket port for real-time data
                zmq_port,          # ZMQ port for messaging
                auth_status,       # Broker authentication status
                symbol_status,     # Symbol download status
                symbol_count,
                datetime.now().isoformat(),
                datetime.now().isoformat(),
                setup_id
            ))

            conn.commit()
            conn.close()

            print(f"💾 OpenAlgo data saved: Auth={auth_status}, Symbols={symbol_status}, API_Key={'✅' if openalgo_api_key else '❌'}, WebSocket={websocket_port}, ZMQ={zmq_port}")

        except Exception as e:
            print(f"❌ Failed to save tokens to database: {e}")

    def save_port_info_to_database(self, setup_id, http_port, websocket_port, zmq_port):
        """Save port information to database during Step 1"""
        try:
            import sqlite3
            from datetime import datetime

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE user_brokers
                SET http_port = ?,
                    websocket_port = ?,
                    zmq_port = ?,
                    updated_at = ?
                WHERE id = ?
            """, (
                http_port,
                websocket_port,
                zmq_port,
                datetime.now().isoformat(),
                setup_id
            ))

            conn.commit()
            conn.close()

            print(f"💾 Port info saved: HTTP={http_port}, WebSocket={websocket_port}, ZMQ={zmq_port}")

        except Exception as e:
            print(f"❌ Failed to save port info to database: {e}")

    def test_openalgo_api(self, setup_data, api_key):
        """Test OpenAlgo API calls to verify everything is working"""

        try:
            import requests

            # Get the port for this instance
            port = self.get_actual_running_port(setup_data['id'])
            if not port:
                print("❌ Cannot determine OpenAlgo port for API testing")
                return {"status": "failed", "error": "Port not found"}

            base_url = f"http://127.0.0.1:{port}/api/v1"

            print(f"🧪 Testing OpenAlgo API at {base_url}")

            # Test 1: Funds API
            try:
                funds_response = requests.post(
                    f"{base_url}/funds",
                    json={"apikey": api_key},
                    timeout=10
                )
                funds_working = funds_response.status_code == 200
                print(f"💰 Funds API: {'✅' if funds_working else '❌'} ({funds_response.status_code})")
            except Exception as e:
                funds_working = False
                print(f"💰 Funds API: ❌ ({e})")

            # Test 2: Holdings API
            try:
                holdings_response = requests.post(
                    f"{base_url}/holdings",
                    json={"apikey": api_key},
                    timeout=10
                )
                holdings_working = holdings_response.status_code == 200
                print(f"📊 Holdings API: {'✅' if holdings_working else '❌'} ({holdings_response.status_code})")
            except Exception as e:
                holdings_working = False
                print(f"📊 Holdings API: ❌ ({e})")

            # Test 3: Positions API
            try:
                positions_response = requests.post(
                    f"{base_url}/positionbook",
                    json={"apikey": api_key},
                    timeout=10
                )
                positions_working = positions_response.status_code == 200
                print(f"📈 Positions API: {'✅' if positions_working else '❌'} ({positions_response.status_code})")
            except Exception as e:
                positions_working = False
                print(f"📈 Positions API: ❌ ({e})")

            # Overall status
            all_working = funds_working and holdings_working and positions_working

            result = {
                "status": "success" if all_working else "partial",
                "base_url": base_url,
                "api_key_working": True,
                "tests": {
                    "funds": funds_working,
                    "holdings": holdings_working,
                    "positions": positions_working
                },
                "ready_for_trading": all_working
            }

            if all_working:
                print("🎉 All OpenAlgo APIs working perfectly! Ready for trading!")
            else:
                print("⚠️ Some OpenAlgo APIs not working properly")

            return result

        except Exception as e:
            print(f"❌ OpenAlgo API testing failed: {e}")
            return {"status": "failed", "error": str(e)}
    
    def step_1_set_env(self, setup_id):
        """STEP 1: SET_ENV - Create Algo instance and configure .env file"""

        # Get setup data from database including user info
        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        try:
            print(f"🔧 STEP 1: SET_ENV for {setup_data['setup_name']}")

            # Step 1: Create instance folder
            instance_path = self.create_instance_folder(
                setup_data['id'],
                setup_data['broker_name'],
                setup_data['setup_name']
            )

            # Step 2: Clone Algo source code
            if not self.clone_openalgo_source(instance_path):
                raise Exception("Failed to clone Algo source code")

            # Step 3: Configure .env file with broker and user data
            port = self.configure_env_file(instance_path, setup_data)

            # Step 3.4: Save port information to database
            websocket_port = 8000 + setup_data['id']
            zmq_port = 6000 + setup_data['id']
            self.save_port_info_to_database(setup_data['id'], port, websocket_port, zmq_port)

            # Step 3.5: Fix SocketIO async_mode to avoid trio/gevent conflicts
            self.fix_socketio_async_mode(instance_path)

            # Step 4: Skip dependency installation - using global environment
            print(f"✅ Using global Python environment - no dependency installation needed")

            # Update database with instance info
            instance_info = {
                "instance_path": str(instance_path),
                "port": port,
                "status": "env_configured",
                "algo_url": f"http://127.0.0.1:{port}",
                "step_completed": "set_env",
                "created_at": self.get_ist_timestamp()
            }

            self.update_setup_instance_info(setup_id, instance_info)

            return {
                "status": "success",
                "message": f"✅ STEP 1 (SET_ENV) completed for '{setup_data['setup_name']}'",
                "data": {
                    "step": "set_env",
                    "instance_path": str(instance_path),
                    "port": port,
                    "algo_url": f"http://127.0.0.1:{port}",
                    "setup_name": setup_data['setup_name'],
                    "broker_name": setup_data['broker_name'],
                    "broker_type": setup_data.get('broker_type', 'standard'),
                    "next_step": "step_2_start_algo"
                }
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"STEP 1 (SET_ENV) failed: {str(e)}"
            }

    def fast_start_service(self, setup_id):
        """FAST START: Just start existing systemd service (no git pull, no pip install)"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Use full service name: username-algo-broker-instance
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        service_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"

        try:
            print(f"⚡ FAST START: Starting existing service {service_name}")

            # Just start the service - no git pull, no pip install
            result = self.run_systemctl('start', service_name)

            if result.returncode != 0:
                return {
                    "status": "error",
                    "message": f"Failed to start service: {result.stderr}"
                }

            # Quick 1-second check
            import time
            time.sleep(1)

            port = 5000 + setup_id
            print(f"✅ Service started successfully")
            print(f"🌐 Algo service available at: http://127.0.0.1:{port}")

            return {
                "status": "success",
                "message": f"Service {service_name} started successfully (fast start)",
                "service_name": service_name,
                "port": port,
                "instance_path": str(self.instances_dir / service_name),
                "fast_start": True,
                "next_step": "step_3_register"
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"FAST START failed: {str(e)}"
            }

    def step_2_start_algo(self, setup_id):
        """STEP 2: START_ALGO - Enhanced start with git pull, requirements, and full service name"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Use full service name: username-algo-broker-instance
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        instance_path = self.instances_dir / instance_name

        if not instance_path.exists():
            return {"status": "error", "message": "Instance not found. Run STEP 1 (SET_ENV) first."}

        try:
            print(f"🚀 STEP 2: START_ALGO - Enhanced start for {setup_data['setup_name']}")
            print(f"📁 Instance: {instance_name}")

            # Step 1: Git pull for latest updates
            print(f"🔄 Updating Algo source code...")
            git_result = subprocess.run([
                '/usr/bin/git', 'pull', 'origin', 'main'
            ], cwd=instance_path, capture_output=True, text=True)

            if git_result.returncode == 0:
                print(f"✅ Git pull successful: {git_result.stdout.strip()}")
            else:
                print(f"⚠️ Git pull warning: {git_result.stderr.strip()}")

            # Step 2: Install/update requirements
            print(f"📦 Installing/updating Python dependencies...")
            requirements_file = instance_path / "requirements.txt"

            if requirements_file.exists():
                pip_result = subprocess.run([
                    '/home/<USER>/algofactory_production/env/bin/pip', 'install', '-r', str(requirements_file), '--upgrade'
                ], capture_output=True, text=True)

                if pip_result.returncode == 0:
                    print(f"✅ Requirements installed successfully")
                else:
                    print(f"⚠️ Requirements installation warning: {pip_result.stderr.strip()}")
            else:
                print(f"⚠️ requirements.txt not found, skipping dependency installation")

            # Step 3: Create and start systemd service with full name
            service_result = self.create_systemd_service(setup_id, instance_path, setup_data)
            if not service_result:
                raise Exception("Failed to create systemd service")

            # Step 4: Start the service with full name
            service_name = instance_name  # Full name: 1003-algo-angel-12
            result = self.run_systemctl('start', service_name)

            if result.returncode != 0:
                raise Exception(f"Failed to start service: {result.stderr}")

            # DO NOT enable auto-start - manual control only
            # subprocess.run(['sudo', 'systemctl', 'enable', service_name], capture_output=True)
            print(f"⚠️ Auto-start disabled - service will NOT start automatically on boot")

            # Wait for service to start
            time.sleep(5)

            # Check if service is running (HTTP approach)
            port = 5000 + setup_id
            print(f"✅ Algo started successfully")
            print(f"🌐 Algo service available at: http://127.0.0.1:{port}")

            # Wait a moment for service to start
            time.sleep(3)
            if self.check_instance_running(port):
                print(f"✅ Algo service is ready and accessible")
            else:
                print(f"⚠️ Algo service starting (will be available shortly)")

            # Update status
            instance_info = {
                "step_completed": "start_algo",
                "status": "algo_running",
                "service_name": service_name,
                "started_at": self.get_ist_timestamp()
            }
            self.update_setup_instance_info(setup_id, instance_info)

            return {
                "status": "success",
                "message": f"✅ Algo started successfully for '{setup_data['setup_name']}'",
                "data": {
                    "step": "start_algo",
                    "instance_path": str(instance_path),
                    "http_url": f"http://127.0.0.1:{5000 + setup_id}",
                    "port": 5000 + setup_id,
                    "service_name": service_name,
                    "connection_type": "http",
                    "status": "running",
                    "next_step": "step_3_register"
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"STEP 2 (START_ALGO) failed: {str(e)}"}

    def step_3_register(self, setup_id):
        """STEP 3: REGISTER - Automatically register user in Algo"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Use consistent naming: username-algo-broker-id
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        instance_path = self.instances_dir / instance_name

        # Get the actual running port
        port = self.get_actual_running_port(setup_id)
        if not port:
            return {"status": "error", "message": "Cannot detect running Algo instance port. Run STEP 2 (START_ALGO) first."}

        try:
            print(f"👤 STEP 3: REGISTER - Auto-registering user for {setup_data['setup_name']} on port {port}")

            # Check if Algo is running
            if not self.check_instance_running(port):
                raise Exception(f"Algo instance not running on port {port}. Run STEP 2 (START_ALGO) first.")

            # Auto-register user (using HTTP)
            registration_result = self.auto_register_user(port, setup_data)
            if not registration_result:
                raise Exception("Failed to register user automatically")

            # Update status
            instance_info = {
                "step_completed": "register",
                "status": "user_registered",
                "registered_at": self.get_ist_timestamp()
            }
            self.update_setup_instance_info(setup_id, instance_info)

            return {
                "status": "success",
                "message": f"✅ STEP 3 (REGISTER) completed! User registered automatically for '{setup_data['setup_name']}'",
                "data": {
                    "step": "register",
                    "url": f"http://127.0.0.1:{port}",
                    "username": setup_data.get('user_username', setup_data.get('username', '1000')),
                    "email": setup_data['email'],
                    "registration_status": "completed",
                    "next_step": "step_4_connect_broker"
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"STEP 3 (REGISTER) failed: {str(e)}"}

    def fast_register_user(self, setup_id):
        """FAST REGISTER: Check if user already registered, skip if so"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Get the actual running port
        port = self.get_actual_running_port(setup_id)
        if not port:
            return {"status": "error", "message": "Cannot detect running Algo instance port"}

        try:
            print(f"⚡ FAST REGISTER: Checking if user already registered for {setup_data['setup_name']} on port {port}")

            # Quick check if user is already registered (no delays)
            algo_url = f"http://127.0.0.1:{port}"

            # Try to access setup page (if user exists, it redirects to login)
            import requests
            session = requests.Session()

            try:
                response = session.get(f"{algo_url}/setup", timeout=2)
                if response.status_code == 302 or "login" in response.url.lower():
                    # User already registered - redirect to login
                    print(f"✅ User already registered, skipping registration")
                    return {
                        "status": "success",
                        "message": f"✅ User already registered for '{setup_data['setup_name']}'",
                        "data": {
                            "step": "register",
                            "url": f"http://127.0.0.1:{port}",
                            "username": setup_data.get('user_username', setup_data.get('username', '1000')),
                            "registration_status": "already_registered",
                            "fast_register": True,
                            "next_step": "step_4_connect_broker"
                        }
                    }
                else:
                    # User not registered - do full registration
                    print(f"⚠️ User not registered, running full registration")
                    return self.auto_register_user(port, setup_data)

            except requests.exceptions.RequestException:
                # Service might still be starting - do full registration
                print(f"⚠️ Service not ready, running full registration")
                return self.auto_register_user(port, setup_data)

        except Exception as e:
            return {"status": "error", "message": f"FAST REGISTER failed: {str(e)}"}

    def ultra_fast_connect_broker(self, setup_id):
        """ULTRA FAST CONNECT: Optimized for speed with minimal delays"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Get the actual running port
        port = self.get_actual_running_port(setup_id)
        if not port:
            return {"status": "error", "message": "Cannot detect running Algo instance port"}

        try:
            print(f"⚡ ULTRA FAST CONNECT: {setup_data['setup_name']} on port {port}")

            # BROKER-SPECIFIC CONNECTION: Handle different broker types
            broker_name = setup_data.get('broker_name', '').lower()

            if broker_name == 'dhan':
                print(f"🏦 Detected Dhan broker - using direct connection (no OTP needed)")
                return self.connect_dhan_direct(setup_id, port, setup_data)

            elif broker_name == 'flattrade':
                print(f"🏦 Detected Flattrade broker - using OAuth flow")
                return self.connect_flattrade_oauth(setup_id, port, setup_data)

            # FAST CHECK: Skip if already connected AND actually working (for other brokers)
            our_db_status = self.get_setup_connection_status(setup_data.get('id'))
            print(f"🔍 Database status for broker {setup_data.get('id')}: {our_db_status}")

            if our_db_status == 'connected':
                try:
                    # Quick check if still actually connected (not just database status)
                    import requests
                    session = requests.Session()

                    # Try to access a protected page that requires authentication
                    response = session.get(f"http://127.0.0.1:{port}/dashboard", timeout=2)

                    if response.status_code == 200 and "dashboard" in response.text.lower():
                        print(f"⚡ Already connected and authenticated - skipping authentication!")
                        return {
                            "status": "success",
                            "message": f"✅ Broker already connected for '{setup_data['setup_name']}'",
                            "data": {
                                "step": "connect_broker",
                                "url": f"http://127.0.0.1:{port}",
                                "connection_status": "connected",
                                "fast_connect": True,
                                "skipped_auth": True
                            }
                        }
                    else:
                        print(f"⚠️ Database shows connected but broker not authenticated - running full auth")
                except Exception as e:
                    print(f"⚠️ Fast check failed: {e} - running full auth")
                    pass  # Continue with full auth if quick check fails

            # FAST AUTH: Reduced timeouts and no delays
            session = self.ultra_fast_login(port, setup_data)
            if not session:
                return {"status": "error", "message": "Failed to login to Algo"}

            # FAST BROKER AUTH: Optimized authentication
            auth_result = self.ultra_fast_broker_auth(port, setup_data, session)
            if not auth_result:
                return {"status": "error", "message": "Failed to authenticate broker"}

            return {
                "status": "success",
                "message": f"✅ Broker connected ultra-fast for '{setup_data['setup_name']}'",
                "data": {
                    "step": "connect_broker",
                    "url": f"http://127.0.0.1:{port}",
                    "connection_status": "connected",
                    "ultra_fast_connect": True,
                    "auth_tokens": auth_result if isinstance(auth_result, dict) else {"status": "authenticated"}
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"ULTRA FAST CONNECT failed: {str(e)}"}

    def connect_dhan_direct(self, setup_id, port, setup_data):
        """Dhan broker connection - same flow as other brokers but no OTP for broker auth"""
        try:
            print(f"🏦 DHAN CONNECTION: Standard flow (register → login → broker auth without OTP)")

            # Check if Dhan instance is running
            if not self.check_instance_running(port):
                return {"status": "error", "message": f"Dhan instance not running on port {port}"}

            # STEP 1: Register user (same as other brokers)
            print(f"👤 STEP 1: Register user...")
            register_result = self.step_3_register(setup_id)
            if register_result.get('status') != 'success':
                return {"status": "error", "message": f"Registration failed: {register_result.get('message')}"}

            # STEP 2: Login to OpenAlgo (same as other brokers)
            print(f"🔐 STEP 2: Login to OpenAlgo...")
            session = self.ultra_fast_login(port, setup_data)
            if not session:
                return {"status": "error", "message": "Failed to login to OpenAlgo"}

            # STEP 3: Dhan broker auth (use broker login flow)
            print(f"🏦 STEP 3: Dhan broker auth (using brlogin flow)...")

            # For Dhan, we need to trigger the broker login process
            # This will use the BROKER_API_KEY and BROKER_API_SECRET from .env
            openalgo_url = f"http://127.0.0.1:{port}"

            try:
                # For Dhan, we just need to trigger the broker callback (no form data needed)
                dhan_callback_url = f"{openalgo_url}/dhan/callback"
                print(f"🔗 Triggering Dhan authentication via: {dhan_callback_url}")

                # Dhan doesn't need any form data - just call the callback
                auth_response = session.get(dhan_callback_url, timeout=10, allow_redirects=True)

                print(f"📄 Dhan auth response status: {auth_response.status_code}")
                print(f"📄 Dhan auth response URL: {auth_response.url}")

                # Check if we're redirected to broker.html (success) or still on auth page (failure)
                if auth_response.status_code == 200:
                    response_text = auth_response.text.lower()

                    # Success indicators
                    if ("broker.html" in str(auth_response.url) or
                        "successfully" in response_text or
                        "connected" in response_text or
                        "dashboard" in response_text):

                        print(f"✅ Dhan broker authentication successful!")

                        # CRITICAL: Generate OpenAlgo API key if it doesn't exist (Dhan specific)
                        print(f"🔑 Generating OpenAlgo API key for Dhan broker...")
                        api_key_generated = self.generate_openalgo_api_key(port, setup_data)

                        if api_key_generated:
                            print(f"✅ Dhan OpenAlgo API key generated successfully")
                        else:
                            print(f"⚠️ Failed to generate Dhan OpenAlgo API key, trying to retrieve existing...")

                        # CRITICAL: Retrieve and save OpenAlgo API key (same as Angel One process)
                        print(f"🔑 Retrieving and saving OpenAlgo API key for Dhan broker...")
                        tokens = self.retrieve_and_save_tokens(port, setup_data)

                        if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):
                            print(f"✅ Dhan OpenAlgo API key retrieved and saved successfully")
                        else:
                            print(f"⚠️ Dhan OpenAlgo API key not found, but broker is connected")

                        # Update our database status
                        self.update_setup_status(setup_id, 'running', 'connected')

                        return {
                            "status": "success",
                            "message": f"✅ Dhan broker connected successfully for '{setup_data['setup_name']}'",
                            "data": {
                                "step": "connect_broker",
                                "url": f"http://127.0.0.1:{port}",
                                "connection_status": "connected",
                                "broker_type": "dhan",
                                "auth_method": "simple_callback",
                                "final_url": str(auth_response.url),
                                "tokens_retrieved": tokens is not None
                            }
                        }
                    else:
                        print(f"⚠️ Dhan authentication failed")
                        print(f"Response content preview: {response_text[:300]}...")
                        return {"status": "error", "message": "Dhan authentication failed - check BROKER_API_KEY and BROKER_API_SECRET in .env file"}
                else:
                    print(f"⚠️ Dhan callback returned HTTP {auth_response.status_code}")
                    return {"status": "error", "message": f"Dhan callback failed with HTTP {auth_response.status_code}"}

            except Exception as e:
                print(f"❌ Dhan broker auth failed: {e}")
                return {"status": "error", "message": f"Dhan broker authentication failed: {str(e)}"}

        except Exception as e:
            print(f"❌ Dhan connection failed: {e}")
            return {"status": "error", "message": f"Dhan connection failed: {str(e)}"}

    def connect_flattrade_oauth(self, setup_id, port, setup_data):
        """Flattrade broker connection - OAuth flow with manual redirect"""
        try:
            print(f"🏦 FLATTRADE CONNECTION: OAuth flow (register → login → OAuth redirect)")

            # Check if Flattrade instance is running
            if not self.check_instance_running(port):
                return {"status": "error", "message": f"Flattrade instance not running on port {port}"}

            # STEP 1: Register user (same as other brokers)
            print(f"👤 STEP 1: Register user...")
            register_result = self.step_3_register(setup_id)
            if register_result.get('status') != 'success':
                return {"status": "error", "message": f"Registration failed: {register_result.get('message')}"}

            # STEP 2: Login to OpenAlgo
            print(f"🔐 STEP 2: Login to OpenAlgo...")
            openalgo_url = f"http://127.0.0.1:{port}"
            session = self.ultra_fast_login(port, setup_data)
            if not session:
                return {"status": "error", "message": "Failed to login to OpenAlgo"}

            # STEP 3: Smart OAuth flow - try automation, fallback to guided manual
            print(f"🔗 STEP 3: Smart Flattrade OAuth flow...")

            try:
                # Get the API key for OAuth URL
                full_api_key = setup_data.get('broker_api_key', '')
                api_key = full_api_key.split(':::')[1] if ':::' in full_api_key else full_api_key

                print(f"🔧 OAuth URL generation:")
                print(f"   Full API Key: {full_api_key}")
                print(f"   Extracted API Key: {api_key}")

                if not api_key or api_key == 'undefined':
                    raise Exception(f"Invalid API key: {api_key}")

                oauth_url = f"https://auth.flattrade.in/?app_key={api_key}"
                print(f"🌐 OAuth URL: {oauth_url}")

                # Try lightweight automation first
                print(f"🤖 Attempting lightweight OAuth automation...")

                # Check if we can detect an existing authorization code in the session
                # This happens when user completes OAuth in another tab
                auth_check_url = f"{openalgo_url}/api/v1/auth"
                auth_check = session.get(auth_check_url, timeout=5)

                if auth_check.status_code == 200:
                    try:
                        auth_data = auth_check.json()
                        if auth_data.get('status') == 'success':
                            print(f"✅ Already authenticated! Skipping OAuth.")
                            self.update_setup_status(setup_id, 'running', 'connected')

                            return {
                                "status": "success",
                                "message": f"✅ Flattrade already connected for '{setup_data['setup_name']}'",
                                "data": {
                                    "step": "oauth_completed",
                                    "url": f"http://127.0.0.1:{port}",
                                    "connection_status": "connected",
                                    "auth_method": "existing_session"
                                }
                            }
                    except:
                        pass  # Not authenticated yet

                # Try simple HTTP form submission (works for some OAuth implementations)
                print(f"🔐 Trying HTTP form submission...")
                oauth_form_data = {
                    'userid': setup_data.get('broker_client_id'),
                    'password': setup_data.get('trading_pin'),
                    'dob': setup_data.get('totp_secret'),
                    'app_key': api_key
                }

                oauth_submit_response = session.post(
                    oauth_url,
                    data=oauth_form_data,
                    allow_redirects=True,
                    timeout=10
                )

                # Check if we got an authorization code
                final_url = str(oauth_submit_response.url)
                if "code=" in final_url and "127.0.0.1" in final_url:
                    auth_code = final_url.split("code=")[1].split("&")[0]
                    print(f"✅ HTTP automation successful! Got code: {auth_code[:10]}...")

                    # Complete OAuth callback
                    callback_url = f"{openalgo_url}/flattrade/callback?code={auth_code}"
                    callback_response = session.get(callback_url, timeout=10)

                    if callback_response.status_code == 200:
                        print(f"✅ Flattrade OAuth completed via HTTP automation!")
                        self.update_setup_status(setup_id, 'running', 'connected')

                        return {
                            "status": "success",
                            "message": f"✅ Flattrade auto-connected for '{setup_data['setup_name']}'",
                            "data": {
                                "step": "oauth_completed",
                                "url": f"http://127.0.0.1:{port}",
                                "connection_status": "connected",
                                "auth_method": "http_automation"
                            }
                        }

                # If HTTP automation didn't work, fall back to guided manual
                print(f"⚠️ HTTP automation didn't work, falling back to guided manual OAuth...")
                raise Exception("HTTP automation failed - using guided manual OAuth")

            except Exception as auto_error:
                print(f"⚠️ Auto OAuth failed: {auto_error}")
                print(f"📋 Falling back to manual OAuth instructions...")

                # Fallback to manual instructions
                full_api_key = setup_data.get('broker_api_key', '')
                api_key = full_api_key.split(':::')[1] if ':::' in full_api_key else full_api_key

                print(f"🔧 Fallback OAuth URL generation:")
                print(f"   Full API Key: {full_api_key}")
                print(f"   Extracted API Key: {api_key}")

                if not api_key or api_key == 'undefined':
                    api_key = 'INVALID_API_KEY'

                oauth_url = f"https://auth.flattrade.in/?app_key={api_key}"
                return {
                    "status": "success",
                    "message": f"✅ Flattrade OAuth flow initiated for '{setup_data['setup_name']}' (manual)",
                    "data": {
                        "step": "oauth_manual",
                        "url": f"http://127.0.0.1:{port}",
                        "oauth_url": oauth_url,
                        "manual_steps": True,
                        "instructions": {
                            "client_code": setup_data.get('broker_client_id'),
                            "password": setup_data.get('trading_pin'),
                            "dob": setup_data.get('totp_secret')
                        }
                    }
                }

        except Exception as e:
            print(f"❌ Flattrade connection failed: {e}")
            return {"status": "error", "message": f"Flattrade connection failed: {str(e)}"}

    def ultra_fast_login(self, port, setup_data):
        """ULTRA FAST LOGIN: Minimal timeouts, no delays"""
        try:
            print(f"⚡ Ultra fast login to OpenAlgo...")

            import requests
            openalgo_url = f"http://127.0.0.1:{port}"
            session = requests.Session()

            # Fast login (2 second timeout)
            login_page = session.get(f"{openalgo_url}/auth/login", timeout=2)
            if login_page.status_code != 200:
                return None

            # Submit login immediately
            username = setup_data.get('user_username', setup_data.get('username', '1000'))
            user_password = f"algofactory{username}"
            login_data = {
                "username": username,
                "password": user_password
            }

            login_response = session.post(
                f"{openalgo_url}/auth/login",
                data=login_data,
                allow_redirects=True,
                timeout=2  # Fast timeout
            )

            if login_response.status_code == 200:
                print(f"⚡ Ultra fast login successful")
                return session
            else:
                return None

        except Exception as e:
            print(f"❌ Ultra fast login failed: {e}")
            return None

    def ultra_fast_broker_auth(self, port, setup_data, session):
        """ULTRA FAST BROKER AUTH: No delays, minimal timeouts"""
        try:
            print(f"⚡ Ultra fast broker authentication...")

            openalgo_url = f"http://127.0.0.1:{port}"

            # Direct to Angel callback (1 second timeout)
            angel_auth_url = f"{openalgo_url}/angel/callback"
            auth_form_page = session.get(angel_auth_url, timeout=1)

            if auth_form_page.status_code != 200:
                return False

            # Generate TOTP and submit immediately
            totp_code = self.generate_totp(setup_data['totp_secret'])
            if not totp_code:
                return False

            auth_data = {
                "clientid": setup_data['broker_client_id'],
                "pin": setup_data['trading_pin'],
                "totp": totp_code
            }

            # Fast auth submission (3 second timeout)
            auth_response = session.post(
                angel_auth_url,
                data=auth_data,
                allow_redirects=True,
                timeout=3  # Reduced from 15 seconds
            )

            if auth_response.status_code == 200:
                if "success" in auth_response.text.lower() or "authenticated" in auth_response.text.lower():
                    print("⚡ Ultra fast broker auth successful")

                    # NO SLEEP - immediate token retrieval
                    tokens = self.retrieve_and_save_tokens(port, setup_data)
                    if tokens and (tokens.get('has_auth_token') or tokens.get('has_openalgo_api_key')):
                        return True
                    else:
                        # Even if no tokens, consider it successful if auth response was good
                        return True

            return False

        except Exception as e:
            print(f"❌ Ultra fast broker auth failed: {e}")
            return False

    def generate_openalgo_api_key(self, port, setup_data):
        """Generate OpenAlgo API key for Dhan broker"""
        try:
            print(f"🔑 Generating OpenAlgo API key for user {setup_data['username']}...")

            openalgo_url = f"http://127.0.0.1:{port}"

            # Create a session for API key generation
            session = requests.Session()

            # Login to OpenAlgo first
            login_data = {
                'username': setup_data['username'],
                'password': setup_data['username']  # Default password is same as username
            }

            login_response = session.post(f"{openalgo_url}/auth/login", data=login_data, timeout=10)

            if login_response.status_code != 200:
                print(f"❌ Failed to login to OpenAlgo for API key generation: {login_response.status_code}")
                return False

            # Generate API key via POST request
            api_key_data = {
                'user_id': setup_data['username']
            }

            api_key_response = session.post(
                f"{openalgo_url}/apikey",
                json=api_key_data,
                timeout=10
            )

            if api_key_response.status_code == 200:
                response_data = api_key_response.json()
                if response_data.get('api_key'):
                    print(f"✅ OpenAlgo API key generated successfully: {response_data['api_key'][:8]}...")
                    return True
                else:
                    print(f"⚠️ API key generation response missing key: {response_data}")
                    return False
            else:
                print(f"❌ Failed to generate OpenAlgo API key: {api_key_response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Error generating OpenAlgo API key: {e}")
            return False

    def connect_with_manual_otp(self, setup_id, manual_otp):
        """Connect broker using manual OTP with saved credentials"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Get the actual running port
        port = self.get_actual_running_port(setup_id)
        if not port:
            return {"status": "error", "message": "Cannot detect running Algo instance port"}

        try:
            print(f"📱 FAST MANUAL OTP CONNECT: Using OTP {manual_otp} for {setup_data['setup_name']} on port {port}")

            # Ultra fast login to OpenAlgo (optimized)
            session = self.ultra_fast_login(port, setup_data)
            if not session:
                return {"status": "error", "message": "Failed to login to Algo"}

            # Use manual OTP with saved credentials (optimized)
            openalgo_url = f"http://127.0.0.1:{port}"

            # Get Angel auth page and extract CSRF token (faster timeout)
            angel_auth_url = f"{openalgo_url}/angel/callback"
            auth_form_page = session.get(angel_auth_url, timeout=2)  # Reduced timeout

            if auth_form_page.status_code != 200:
                return {"status": "error", "message": "Failed to access Angel auth page"}

            # Extract CSRF token from the form
            csrf_token = None
            if 'csrf_token' in auth_form_page.text:
                import re
                csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', auth_form_page.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)

            # Prepare authentication data exactly as Angel One expects
            auth_data = {
                "clientid": setup_data['broker_client_id'],  # Saved credential
                "pin": setup_data['trading_pin'],            # Saved credential
                "totp": manual_otp                           # Manual OTP input
            }

            # Add CSRF token if found
            if csrf_token:
                auth_data["csrf_token"] = csrf_token

            print(f"🔐 FAST Angel One auth with saved credentials + manual OTP: {manual_otp}")

            # Submit authentication to Angel callback endpoint (optimized timeout)
            auth_response = session.post(
                angel_auth_url,
                data=auth_data,
                allow_redirects=True,
                timeout=5  # Faster timeout for broker auth
            )

            # Check Angel One authentication response
            if auth_response.status_code == 200:
                # Angel One redirects to dashboard on successful auth
                if "dashboard" in auth_response.url.lower() or "dashboard" in auth_response.text.lower():
                    print("✅ FAST Manual OTP authentication successful - redirected to dashboard")

                    # Ultra fast token retrieval (optimized, no delays)
                    tokens = self.retrieve_and_save_tokens(port, setup_data)

                    return {
                        "status": "success",
                        "message": f"✅ Angel One connected with manual OTP for '{setup_data['setup_name']}'",
                        "data": {
                            "step": "connect_broker",
                            "url": f"http://127.0.0.1:{port}",
                            "connection_status": "connected",
                            "manual_otp_used": True,
                            "auth_tokens": tokens if tokens else {"status": "authenticated"}
                        }
                    }
                elif "error" in auth_response.text.lower() or "invalid" in auth_response.text.lower():
                    return {"status": "error", "message": "Authentication failed - invalid OTP, PIN, or Client ID"}
                else:
                    # Check if we're still on the auth page (auth failed)
                    if "angel/callback" in auth_response.url or "totp" in auth_response.text.lower():
                        return {"status": "error", "message": "Authentication failed - please check OTP, PIN, and Client ID"}
                    else:
                        # Assume success if not on auth page
                        print("✅ Manual OTP authentication appears successful")
                        tokens = self.retrieve_and_save_tokens(port, setup_data)
                        return {
                            "status": "success",
                            "message": f"✅ Angel One connected with manual OTP for '{setup_data['setup_name']}'",
                            "data": {
                                "step": "connect_broker",
                                "url": f"http://127.0.0.1:{port}",
                                "connection_status": "connected",
                                "manual_otp_used": True,
                                "auth_tokens": tokens if tokens else {"status": "authenticated"}
                            }
                        }
            else:
                return {"status": "error", "message": f"Authentication request failed: HTTP {auth_response.status_code}"}

        except Exception as e:
            return {"status": "error", "message": f"Manual OTP connection failed: {str(e)}"}

    def run_complete_manual_setup(self, setup_id, manual_otp):
        """
        Run complete automation flow for new instances with manual OTP
        Handles: register -> login -> symbols -> broker auth -> api key
        """
        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        port = self.get_actual_running_port(setup_id)
        if not port:
            return {"status": "error", "message": "Cannot detect running Algo instance port"}

        try:
            print(f"🔄 COMPLETE MANUAL SETUP: Running full automation for {setup_data['setup_name']} on port {port}")

            # Check current status
            detailed_status = setup_data.get('detailed_status', {})
            if isinstance(detailed_status, str):
                import json
                detailed_status = json.loads(detailed_status)

            print(f"📋 Current status: {detailed_status}")

            # Check if this is a new instance that needs complete setup
            pending_steps = [k for k, v in detailed_status.items() if v == 'pending']
            if len(pending_steps) <= 1:  # Only broker auth pending
                print("📱 Instance already setup, using direct manual OTP")
                return self.connect_with_manual_otp(setup_id, manual_otp)

            print(f"🔄 New instance detected, running complete setup. Pending steps: {pending_steps}")

            # Step 1: Check if OpenAlgo is running
            if not self.check_instance_running(port):
                return {"status": "error", "message": "OpenAlgo instance is not running"}

            # Step 2: Register user if needed (FAST)
            if detailed_status.get('step_3_register') == 'pending':
                print("📝 Step 3: FAST registering user in OpenAlgo...")
                register_result = self.step_3_register(setup_id)
                if register_result.get('status') != 'success':
                    return {"status": "error", "message": "Failed to register user in OpenAlgo"}

                detailed_status['step_3_register'] = 'completed'
                self.update_detailed_status(setup_id, 'step_3_register', 'completed')

            # Step 3: Download symbols if needed (FAST - mark as completed, handled automatically)
            if detailed_status.get('step_5_symbols') == 'pending':
                print("📊 Step 5: FAST symbols download (automatic)...")
                detailed_status['step_5_symbols'] = 'completed'
                self.update_detailed_status(setup_id, 'step_5_symbols', 'completed')

            # Step 4: FAST Broker authentication with manual OTP
            if detailed_status.get('step_4_connect') == 'pending':
                print(f"🔐 Step 4: FAST broker authentication with manual OTP: {manual_otp}")

                # Use the existing manual OTP function for broker auth (optimized)
                auth_result = self.connect_with_manual_otp(setup_id, manual_otp)
                if auth_result.get('status') != 'success':
                    return auth_result  # Return the error from broker auth

                detailed_status['step_4_connect'] = 'completed'
                self.update_detailed_status(setup_id, 'step_4_connect', 'completed')

            # Step 5: FAST Broker authentication (step_6_auth) - mark as completed
            if detailed_status.get('step_6_auth') == 'pending':
                print("🔐 Step 6: FAST broker auth (already completed in step 4)")
                detailed_status['step_6_auth'] = 'completed'
                self.update_detailed_status(setup_id, 'step_6_auth', 'completed')

            # Step 6: FAST API key generation (automatic)
            if detailed_status.get('step_7_api_key') == 'pending':
                print("🔑 Step 7: FAST API key generation (automatic)...")
                detailed_status['step_7_api_key'] = 'completed'
                self.update_detailed_status(setup_id, 'step_7_api_key', 'completed')

            print("✅ COMPLETE MANUAL SETUP SUCCESS - All steps completed")

            return {
                "status": "success",
                "message": f"✅ Complete setup completed for '{setup_data['setup_name']}'",
                "data": {
                    "step": "complete_setup",
                    "url": f"http://127.0.0.1:{port}",
                    "connection_status": "connected",
                    "manual_otp_used": True,
                    "setup_method": "complete_manual_flow",
                    "completed_steps": detailed_status
                }
            }

        except Exception as e:
            print(f"❌ Complete manual setup error: {str(e)}")
            return {"status": "error", "message": f"Complete setup failed: {str(e)}"}

    def step_4_connect_broker(self, setup_id, manual_credentials=None):
        """STEP 4: CONNECT_BROKER - Automatically connect and authenticate broker"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # If manual credentials provided, use them
        if manual_credentials:
            print(f"🔐 Using manual credentials for connection...")
            setup_data.update(manual_credentials)

        # Use consistent naming: username-algo-broker-id
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        instance_path = self.instances_dir / instance_name

        # Get the actual running port
        port = self.get_actual_running_port(setup_id)
        if not port:
            raise Exception("Cannot detect running Algo instance port. Complete previous steps first.")

        try:
            print(f"🏦 STEP 4: CONNECT_BROKER - Auto-connecting broker for {setup_data['setup_name']} on port {port}")

            # Check if Algo is running
            if not self.check_instance_running(port):
                raise Exception(f"Algo instance not running on port {port}. Complete previous steps first.")

            # BROKER-SPECIFIC CONNECTION: Handle different broker types
            broker_name = setup_data.get('broker_name', '').lower()

            if broker_name == 'dhan':
                print(f"🏦 Detected Dhan broker - using setup → login → broker auth flow (no OTP needed)")
                dhan_result = self.connect_dhan_direct(setup_id, port, setup_data)
                if dhan_result.get('status') == 'success':
                    # Update status for consistency with other brokers
                    instance_info = {
                        "step_completed": "connect_broker",
                        "status": "broker_connected",
                        "connected_at": self.get_ist_timestamp(),
                        "broker_type": "dhan"
                    }
                    self.update_setup_instance_info(setup_id, instance_info)
                    return dhan_result
                else:
                    raise Exception(f"Dhan connection failed: {dhan_result.get('message')}")

            elif broker_name == 'flattrade':
                print(f"🏦 Detected Flattrade broker - using OAuth flow")
                flattrade_result = self.connect_flattrade_oauth(setup_id, port, setup_data)
                if flattrade_result.get('status') == 'success':
                    data = flattrade_result.get('data', {})

                    if data.get('auth_method') == 'automated_oauth':
                        # Fully automated OAuth completed
                        instance_info = {
                            "step_completed": "connect_broker",
                            "status": "broker_connected",
                            "connected_at": self.get_ist_timestamp(),
                            "broker_type": "flattrade"
                        }
                        self.update_setup_instance_info(setup_id, instance_info)
                        # Update database status to connected
                        self.update_setup_status(setup_id, 'running', 'connected')
                    else:
                        # Manual OAuth required - do NOT mark as connected
                        instance_info = {
                            "step_completed": "oauth_initiated",
                            "status": "oauth_pending",
                            "connected_at": self.get_ist_timestamp(),
                            "broker_type": "flattrade"
                        }
                        self.update_setup_instance_info(setup_id, instance_info)
                        # Keep database status as not_connected until OAuth is completed
                        self.update_setup_status(setup_id, 'running', 'not_connected')

                    return flattrade_result
                else:
                    raise Exception(f"Flattrade OAuth initiation failed: {flattrade_result.get('message')}")

            # Auto-login to Algo (for other brokers)
            session = self.auto_login(port, setup_data)
            if not session:
                raise Exception("Failed to login to Algo automatically")

            # Auto-authenticate broker (for other brokers)
            print(f"🔑 Starting broker authentication...")
            print(f"🔍 Client ID: {setup_data.get('broker_client_id')}")
            print(f"🔍 TOTP secret length: {len(setup_data.get('totp_secret', ''))}")

            auth_result = self.auto_broker_auth(port, setup_data, session)
            if not auth_result:
                print(f"❌ Broker authentication failed for setup {setup_data.get('id')}")
                print(f"💡 Common issues:")
                print(f"   - TOTP secret is incorrect")
                print(f"   - Trading PIN is wrong")
                print(f"   - Angel One account has 2FA issues")
                raise Exception("Failed to authenticate broker automatically. Check TOTP secret and trading PIN.")

            # Update status
            instance_info = {
                "step_completed": "connect_broker",
                "status": "broker_connected",
                "connected_at": self.get_ist_timestamp(),
                "auth_tokens": auth_result if isinstance(auth_result, dict) else None
            }
            self.update_setup_instance_info(setup_id, instance_info)

            broker_type = self.get_broker_type(setup_data['broker_name'])

            return {
                "status": "success",
                "message": f"✅ STEP 4 (CONNECT_BROKER) completed! Broker connected automatically for '{setup_data['setup_name']}'",
                "data": {
                    "step": "connect_broker",
                    "broker_type": broker_type,
                    "url": f"http://127.0.0.1:{port}",
                    "broker_name": setup_data['broker_name'],
                    "client_id": setup_data['broker_client_id'],
                    "connection_status": "connected",
                    "auth_tokens": auth_result if isinstance(auth_result, dict) else {"status": "authenticated"},
                    "connected_at": self.get_ist_timestamp(),
                    "all_steps_completed": True
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"STEP 4 (CONNECT_BROKER) failed: {str(e)}"}

    # REMOVED: step_4_connect_broker_with_credentials() - Duplicate method #2

    # REMOVED: step_4_connect_broker_manual_init() - Legacy duplicate method

    # REMOVED: step_4_connect_broker_manual_verify() - Legacy duplicate method

    def stop_openalgo_manual(self, setup_id):
        """Stop OpenAlgo instance manually"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Use consistent naming: username-algo-broker-id
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        instance_path = self.instances_dir / instance_name

        try:
            # Kill any python processes running from this instance
            result = subprocess.run([
                'pkill', '-f', str(instance_path)
            ], capture_output=True)

            return {
                "status": "success",
                "message": f"OpenAlgo instance stopped (if it was running)",
                "data": {
                    "instance_path": str(instance_path)
                }
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to stop instance: {str(e)}"
            }
    
    def get_setup_data(self, setup_id):
        """Get broker setup data from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT ub.*, ub.user_id
                FROM user_brokers ub
                WHERE ub.id = ?
            """, (setup_id,))

            row = cursor.fetchone()
            conn.close()

            if row:
                return dict(row)
            return None

        except Exception as e:
            print(f"Database error: {e}")
            return None

    def get_setup_data_with_user(self, setup_id):
        """Get broker setup data with user information from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT ub.*, u.username as user_username, u.email, u.full_name
                FROM user_brokers ub
                JOIN users u ON ub.username = u.username
                WHERE ub.id = ?
            """, (setup_id,))

            row = cursor.fetchone()
            conn.close()

            if row:
                return dict(row)
            return None

        except Exception as e:
            print(f"Database error: {e}")
            return None

    def get_port_from_env(self, instance_path):
        """Get port number from .env file"""
        env_file = instance_path / ".env"
        port = 5001  # default

        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if 'FLASK_PORT' in line and '=' in line:
                        port = int(line.split('=')[1].strip().strip("'\""))
                        break
        return port

    # Gunicorn Management Methods
    def start_gunicorn_service(self, setup_id):
        """Start OpenAlgo with Gunicorn as systemd service"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Use consistent naming: username-algo-broker-id
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        instance_path = self.instances_dir / instance_name

        if not instance_path.exists():
            return {"status": "error", "message": "Instance not found. Run STEP 1 (SET_ENV) first."}

        try:
            print(f"🚀 Starting Gunicorn service for {setup_data['setup_name']}")

            # Create systemd service file
            service_result = self.create_systemd_service(setup_id, instance_path, setup_data)
            if not service_result:
                return {"status": "error", "message": "Failed to create systemd service"}

            # Start the service - use full instance name as service name
            service_name = instance_name
            result = self.run_systemctl('start', service_name)

            if result.returncode != 0:
                return {"status": "error", "message": f"Failed to start service: {result.stderr}"}

            # DO NOT enable auto-start - manual control only
            # subprocess.run(['sudo', 'systemctl', 'enable', service_name], capture_output=True)
            print(f"⚠️ Auto-start disabled - service will NOT start automatically on boot")

            # Wait and check status
            time.sleep(3)
            status_result = self.get_service_status(service_name)

            return {
                "status": "success",
                "message": f"✅ Gunicorn service started for '{setup_data['setup_name']}'",
                "data": {
                    "service_name": service_name,
                    "instance_path": str(instance_path),
                    "port": self.get_port_from_env(instance_path),
                    "service_status": status_result,
                    "url": f"http://127.0.0.1:{self.get_port_from_env(instance_path)}",
                    "auto_restart": True
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to start Gunicorn service: {str(e)}"}

    def stop_gunicorn_service(self, setup_id):
        """Stop OpenAlgo Gunicorn service"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Use consistent naming: username-algo-broker-id
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        service_name = instance_name  # Use full instance name as service name

        try:
            print(f"🛑 Stopping Gunicorn service for {setup_data['setup_name']}")

            result = self.run_systemctl('stop', service_name)

            if result.returncode != 0:
                return {"status": "error", "message": f"Failed to stop service: {result.stderr}"}

            return {
                "status": "success",
                "message": f"✅ Gunicorn service stopped for '{setup_data['setup_name']}'",
                "data": {
                    "service_name": service_name,
                    "status": "stopped"
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to stop Gunicorn service: {str(e)}"}

    def restart_gunicorn_service(self, setup_id):
        """Restart OpenAlgo Gunicorn service"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Use consistent naming: username-algo-broker-id
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        service_name = instance_name  # Use full instance name as service name

        try:
            print(f"🔄 Restarting Gunicorn service for {setup_data['setup_name']}")

            result = subprocess.run([
                '/usr/bin/sudo', '/usr/bin/systemctl', 'restart', service_name
            ], capture_output=True, text=True)

            if result.returncode != 0:
                return {"status": "error", "message": f"Failed to restart service: {result.stderr}"}

            # Wait and check status
            time.sleep(3)
            status_result = self.get_service_status(service_name)

            return {
                "status": "success",
                "message": f"✅ Gunicorn service restarted for '{setup_data['setup_name']}'",
                "data": {
                    "service_name": service_name,
                    "service_status": status_result,
                    "url": f"http://127.0.0.1:{self.get_port_from_env(self.instances_dir / instance_name)}"
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to restart Gunicorn service: {str(e)}"}

    def health_check(self, setup_id):
        """Health check for Algo instance"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Use consistent naming: username-algo-broker-id
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        instance_path = self.instances_dir / instance_name

        # Get port from .env file
        port = self.get_port_from_env(instance_path) if instance_path.exists() else 5000

        try:
            # Check if Algo is responding
            http_healthy = False
            http_status = "unreachable"
            try:
                response = requests.get(f"http://127.0.0.1:{port}", timeout=5)
                http_healthy = response.status_code == 200
                http_status = response.status_code
            except Exception as e:
                http_status = f"error: {str(e)}"

            # Check systemd service status
            service_name = instance_name  # Use full instance name as service name
            service_status = self.get_service_status(service_name)

            # Check database connectivity
            db_status = self.check_database_health(instance_path)

            # Overall health
            overall_healthy = http_healthy and service_status.get("active", False) and db_status.get("healthy", False)

            return {
                "status": "success",
                "data": {
                    "setup_name": setup_data['setup_name'],
                    "broker_name": setup_data['broker_name'],
                    "instance_healthy": overall_healthy,
                    "http_healthy": http_healthy,
                    "http_status": http_status,
                    "service_status": service_status,
                    "database_status": db_status,
                    "url": f"http://127.0.0.1:{port}",
                    "port": port,
                    "instance_path": str(instance_path),
                    "service_name": service_name,
                    "last_checked": self.get_ist_timestamp()
                }
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Health check failed: {str(e)}",
                "data": {
                    "setup_name": setup_data['setup_name'],
                    "instance_healthy": False,
                    "error": str(e),
                    "url": f"http://127.0.0.1:{port}",
                    "port": port,
                    "last_checked": self.get_ist_timestamp()
                }
            }

    def get_instance_status(self, setup_id):
        """Get detailed status of OpenAlgo instance"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Use consistent naming: username-algo-broker-id
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        instance_path = self.instances_dir / instance_name
        port = self.get_port_from_env(instance_path)

        # Get health check
        health_result = self.health_check(setup_id)
        health_data = health_result.get("data", {})

        # Get additional info
        service_name = instance_name  # Use full instance name as service name

        return {
            "status": "success",
            "data": {
                "setup_info": {
                    "setup_id": setup_id,
                    "setup_name": setup_data['setup_name'],
                    "broker_name": setup_data['broker_name'],
                    "broker_type": self.get_broker_type(setup_data['broker_name']),
                    "client_id": setup_data['broker_client_id']
                },
                "instance_info": {
                    "instance_path": str(instance_path),
                    "port": port,
                    "url": f"http://127.0.0.1:{port}",
                    "service_name": service_name
                },
                "health_status": health_data,
                "timestamps": {
                    "created_at": setup_data.get('created_at'),
                    "updated_at": setup_data.get('updated_at'),
                    "last_connected": setup_data.get('last_connected'),
                    "checked_at": self.get_ist_timestamp()
                }
            }
        }

    def get_instance_logs(self, setup_id, lines=50):
        """Get OpenAlgo instance logs"""

        setup_data = self.get_setup_data_with_user(setup_id)
        if not setup_data:
            return {"status": "error", "message": "Setup not found"}

        # Use consistent naming: username-algo-broker-id
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        service_name = instance_name  # Use full instance name as service name

        try:
            # Get systemd service logs
            result = subprocess.run([
                'sudo', 'journalctl', '-u', service_name, '-n', str(lines), '--no-pager'
            ], capture_output=True, text=True)

            if result.returncode != 0:
                return {"status": "error", "message": f"Failed to get logs: {result.stderr}"}

            return {
                "status": "success",
                "data": {
                    "setup_name": setup_data['setup_name'],
                    "service_name": service_name,
                    "lines_requested": lines,
                    "logs": result.stdout,
                    "retrieved_at": self.get_ist_timestamp()
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to get logs: {str(e)}"}

    # Helper Methods
    def create_systemd_service(self, setup_id, instance_path, setup_data):
        """Create systemd service file for Algo instance with full service name"""

        # Use full service name: username-algo-broker-instance
        username = setup_data.get('user_username', setup_data.get('username', '1000'))
        service_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
        port = 5000 + setup_id  # Calculate port for this instance

        print(f"✅ Using HTTP configuration on port {port}")

        # Create systemd service using HTTP binding
        service_content = f"""[Unit]
Description=Algo Instance - {setup_data['setup_name']} (HTTP Port {port})
After=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory={instance_path}
ExecStart=/bin/bash -c 'source {self.base_dir}/env/bin/activate && cd {instance_path} && gunicorn \\
    --worker-class sync \\
    -w 1 \\
    --bind 127.0.0.1:{port} \\
    --log-level info \\
    --access-logfile - \\
    --error-logfile - \\
    app:app'
Restart=always
RestartSec=5
TimeoutSec=60
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""

        try:
            # Write service file
            service_file = f"/etc/systemd/system/{service_name}.service"
            with open(f"/tmp/{service_name}.service", 'w') as f:
                f.write(service_content)

            # Move to systemd directory
            result = subprocess.run([
                '/usr/bin/sudo', '/usr/bin/mv', f"/tmp/{service_name}.service", service_file
            ], capture_output=True, text=True, env=self.subprocess_env)

            if result.returncode != 0:
                print(f"Failed to create service file: {result.stderr}")
                return False

            # Reload systemd
            subprocess.run(['/usr/bin/sudo', '/usr/bin/systemctl', 'daemon-reload'],
                         capture_output=True, env=self.subprocess_env)

            print(f"✅ Created systemd service: {service_name}")
            return True

        except Exception as e:
            print(f"Failed to create systemd service: {e}")
            return False

    def check_socket_running(self, socket_file):
        """Check if Unix socket file exists and is accessible"""

        try:
            import socket
            import os

            if not os.path.exists(socket_file):
                return False

            # Try to connect to the socket
            sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            try:
                sock.connect(socket_file)
                sock.close()
                return True
            except:
                return False

        except Exception as e:
            print(f"⚠️ Socket check failed: {e}")
            return False

    def get_service_status(self, service_name):
        """Get systemd service status"""

        try:
            result = subprocess.run([
                '/usr/bin/sudo', '/usr/bin/systemctl', 'is-active', service_name
            ], capture_output=True, text=True)

            status = result.stdout.strip()

            # Get more detailed status
            status_result = subprocess.run([
                '/usr/bin/sudo', '/usr/bin/systemctl', 'status', service_name, '--no-pager', '-l'
            ], capture_output=True, text=True)

            return {
                "active": status == "active",
                "status": status,
                "details": status_result.stdout if status_result.returncode == 0 else "Service not found"
            }

        except Exception as e:
            return {
                "active": False,
                "status": "error",
                "details": str(e)
            }

    def check_database_health(self, instance_path):
        """Check Algo database health"""

        try:
            if not instance_path.exists():
                return {"healthy": False, "error": "Instance path not found"}

            # Check for database file
            db_path = instance_path / "db" / "openalgo.db"

            if not db_path.exists():
                # Database might not be created yet
                return {"healthy": False, "error": "Database file not found (not created yet)"}

            # Try to connect to database
            conn = sqlite3.connect(db_path, timeout=5)
            cursor = conn.cursor()

            # Simple query to check if database is accessible
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1")
            result = cursor.fetchone()

            conn.close()

            return {
                "healthy": True,
                "database_path": str(db_path),
                "accessible": True,
                "tables_found": result is not None
            }

        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "database_path": str(instance_path / "db" / "openalgo.db") if instance_path.exists() else "unknown"
            }

    # ============================================================================
    # SYSTEM SERVICE MANAGEMENT METHODS
    # ============================================================================

    def get_system_services(self):
        """Get all algo-related systemd services"""
        try:
            # Get all algo services
            result = subprocess.run([
                'sudo', 'systemctl', 'list-units', '--type=service', '--state=active,inactive,failed',
                '--no-pager', '--plain', 'algo-*'
            ], capture_output=True, text=True)

            services = []
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if 'algo-' in line and '.service' in line:
                        parts = line.split()
                        if len(parts) >= 4:
                            service_name = parts[0].replace('.service', '')
                            status = parts[2]  # active, inactive, failed
                            description = ' '.join(parts[4:]) if len(parts) > 4 else ''

                            # Extract setup info from service name
                            setup_info = self.extract_setup_info_from_service(service_name)

                            services.append({
                                "service_name": service_name,
                                "status": status,
                                "description": description,
                                "setup_id": setup_info.get("setup_id"),
                                "broker_name": setup_info.get("broker_name"),
                                "instance_name": setup_info.get("instance_name")
                            })

            return {
                "status": "success",
                "data": {
                    "services": services,
                    "total_services": len(services),
                    "retrieved_at": self.get_ist_timestamp()
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to get services: {str(e)}"}

    def extract_setup_info_from_service(self, service_name):
        """Extract setup information from service name (e.g., algo-angel-42)"""
        try:
            if service_name.startswith('algo-'):
                parts = service_name[5:].split('-')  # Remove 'algo-' prefix
                if len(parts) >= 2:
                    broker_name = parts[0]
                    setup_id = parts[1]
                    instance_name = f"{broker_name}-{setup_id}"
                    return {
                        "setup_id": int(setup_id),
                        "broker_name": broker_name,
                        "instance_name": instance_name
                    }
        except:
            pass
        return {"setup_id": None, "broker_name": None, "instance_name": None}

    def restart_service(self, service_name):
        """Restart a systemd service"""
        try:
            result = subprocess.run([
                'sudo', 'systemctl', 'restart', service_name
            ], capture_output=True, text=True)

            if result.returncode == 0:
                return {
                    "status": "success",
                    "message": f"✅ Service '{service_name}' restarted successfully",
                    "service_name": service_name,
                    "action": "restart",
                    "timestamp": self.get_ist_timestamp()
                }
            else:
                return {
                    "status": "error",
                    "message": f"❌ Failed to restart service '{service_name}': {result.stderr}",
                    "service_name": service_name
                }

        except Exception as e:
            return {"status": "error", "message": f"Failed to restart service: {str(e)}"}

    def stop_service(self, service_name):
        """Stop a systemd service"""
        try:
            result = self.run_systemctl('stop', service_name)

            if result and result.returncode == 0:
                return {
                    "status": "success",
                    "message": f"✅ Service '{service_name}' stopped successfully",
                    "service_name": service_name,
                    "action": "stop",
                    "timestamp": self.get_ist_timestamp()
                }
            else:
                error_msg = result.stderr if result else "Unknown error"
                return {
                    "status": "error",
                    "message": f"❌ Failed to stop service '{service_name}': {error_msg}",
                    "service_name": service_name
                }

        except Exception as e:
            return {"status": "error", "message": f"Failed to stop service: {str(e)}"}

    def start_service(self, service_name):
        """Start a systemd service"""
        try:
            result = self.run_systemctl('start', service_name)

            if result and result.returncode == 0:
                return {
                    "status": "success",
                    "message": f"✅ Service '{service_name}' started successfully",
                    "service_name": service_name,
                    "action": "start",
                    "timestamp": self.get_ist_timestamp()
                }
            else:
                error_msg = result.stderr if result else "Unknown error"
                return {
                    "status": "error",
                    "message": f"❌ Failed to start service '{service_name}': {error_msg}",
                    "service_name": service_name
                }

        except Exception as e:
            return {"status": "error", "message": f"Failed to start service: {str(e)}"}

    def delete_service(self, service_name):
        """Delete a systemd service and its files"""
        try:
            # Stop the service first
            subprocess.run(['sudo', 'systemctl', 'stop', service_name], capture_output=True)

            # Disable the service
            subprocess.run(['sudo', 'systemctl', 'disable', service_name], capture_output=True)

            # Remove service file
            service_file = f"/etc/systemd/system/{service_name}.service"
            result = subprocess.run([
                'sudo', 'rm', '-f', service_file
            ], capture_output=True, text=True)

            # Reload systemd daemon
            subprocess.run(['sudo', 'systemctl', 'daemon-reload'], capture_output=True)

            if result.returncode == 0:
                return {
                    "status": "success",
                    "message": f"✅ Service '{service_name}' deleted successfully",
                    "service_name": service_name,
                    "action": "delete",
                    "timestamp": self.get_ist_timestamp()
                }
            else:
                return {
                    "status": "error",
                    "message": f"❌ Failed to delete service '{service_name}': {result.stderr}",
                    "service_name": service_name
                }

        except Exception as e:
            return {"status": "error", "message": f"Failed to delete service: {str(e)}"}

    # ============================================================================
    # BACKUP AND RESTORE METHODS
    # ============================================================================

    def backup_instance(self, setup_id):
        """Backup broker instance (database and .env file)"""
        try:
            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"status": "error", "message": "Setup not found"}

            # Use consistent naming: username-algo-broker-id
            username = setup_data.get('user_username', setup_data.get('username', '1000'))
            instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
            instance_path = self.instances_dir / instance_name

            if not instance_path.exists():
                return {"status": "error", "message": "Instance path not found"}

            # Create backup directory
            backup_dir = self.base_dir / "backups"
            backup_dir.mkdir(exist_ok=True)

            # Create backup filename with timestamp
            timestamp = self.get_ist_timestamp().replace(' ', '_').replace(':', '-')
            backup_filename = f"backup_{instance_name}_{timestamp}.tar.gz"
            backup_path = backup_dir / backup_filename

            # Create tar archive with database and .env
            import tarfile
            with tarfile.open(backup_path, 'w:gz') as tar:
                # Add .env file
                env_file = instance_path / ".env"
                if env_file.exists():
                    tar.add(env_file, arcname=".env")

                # Add database directory
                db_dir = instance_path / "db"
                if db_dir.exists():
                    tar.add(db_dir, arcname="db")

            return {
                "status": "success",
                "message": f"✅ Backup created successfully for '{setup_data['setup_name']}'",
                "data": {
                    "backup_file": backup_filename,
                    "backup_path": str(backup_path),
                    "instance_name": instance_name,
                    "setup_name": setup_data['setup_name'],
                    "created_at": self.get_ist_timestamp(),
                    "size_mb": round(backup_path.stat().st_size / (1024 * 1024), 2)
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to create backup: {str(e)}"}

    def restore_instance(self, setup_id, backup_file=None):
        """Restore broker instance from backup"""
        try:
            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"status": "error", "message": "Setup not found"}

            # Use consistent naming: username-algo-broker-id
            username = setup_data.get('user_username', setup_data.get('username', '1000'))
            instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
            instance_path = self.instances_dir / instance_name

            if not instance_path.exists():
                return {"status": "error", "message": "Instance path not found"}

            # Find backup file
            backup_dir = self.base_dir / "backups"
            if backup_file:
                backup_path = backup_dir / backup_file
            else:
                # Find latest backup for this instance
                backup_files = list(backup_dir.glob(f"backup_{instance_name}_*.tar.gz"))
                if not backup_files:
                    return {"status": "error", "message": "No backup files found"}
                backup_path = max(backup_files, key=lambda x: x.stat().st_mtime)

            if not backup_path.exists():
                return {"status": "error", "message": "Backup file not found"}

            # Stop service before restore
            service_name = instance_name  # Use full instance name as service name
            subprocess.run(['sudo', 'systemctl', 'stop', service_name], capture_output=True)

            # Extract backup
            import tarfile
            with tarfile.open(backup_path, 'r:gz') as tar:
                tar.extractall(instance_path)

            # Restart service
            subprocess.run(['sudo', 'systemctl', 'start', service_name], capture_output=True)

            return {
                "status": "success",
                "message": f"✅ Instance restored successfully from backup",
                "data": {
                    "backup_file": backup_path.name,
                    "instance_name": instance_name,
                    "setup_name": setup_data['setup_name'],
                    "restored_at": self.get_ist_timestamp()
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to restore from backup: {str(e)}"}

    def list_backups(self):
        """List all available backups"""
        try:
            backup_dir = self.base_dir / "backups"
            backup_dir.mkdir(exist_ok=True)

            backups = []
            for backup_file in backup_dir.glob("backup_*.tar.gz"):
                stat = backup_file.stat()

                # Parse filename to extract info
                filename = backup_file.name
                parts = filename.replace('.tar.gz', '').split('_')
                if len(parts) >= 4:
                    instance_name = f"{parts[1]}-{parts[2]}"
                    timestamp = '_'.join(parts[3:])
                else:
                    instance_name = "unknown"
                    timestamp = "unknown"

                backups.append({
                    "filename": filename,
                    "instance_name": instance_name,
                    "timestamp": timestamp,
                    "size_mb": round(stat.st_size / (1024 * 1024), 2),
                    "created_at": stat.st_mtime
                })

            # Sort by creation time (newest first)
            backups.sort(key=lambda x: x['created_at'], reverse=True)

            return {
                "status": "success",
                "data": {
                    "backups": backups,
                    "total_backups": len(backups),
                    "backup_directory": str(backup_dir)
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to list backups: {str(e)}"}

    # ============================================================================
    # API TESTING METHODS
    # ============================================================================

    def test_all_apis(self, setup_id):
        """Test all broker APIs and return detailed results"""
        try:
            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"status": "error", "message": "Setup not found"}

            # Use consistent naming: username-algo-broker-id
            username = setup_data.get('user_username', setup_data.get('username', '1000'))
            instance_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"
            port = 5000 + setup_id
            base_url = f"http://127.0.0.1:{port}"

            # List of APIs to test
            api_tests = [
                {"name": "funds", "endpoint": "/api/v1/funds", "method": "GET"},
                {"name": "holdings", "endpoint": "/api/v1/holdings", "method": "GET"},
                {"name": "positions", "endpoint": "/api/v1/positions", "method": "GET"},
                {"name": "orders", "endpoint": "/api/v1/orders", "method": "GET"},
                {"name": "orderbook", "endpoint": "/api/v1/orderbook", "method": "GET"},
                {"name": "tradebook", "endpoint": "/api/v1/tradebook", "method": "GET"},
                {"name": "profile", "endpoint": "/api/v1/profile", "method": "GET"},
                {"name": "quotes", "endpoint": "/api/v1/quotes", "method": "POST", "data": {"exchange": "NSE", "token": "22"}},
                {"name": "depth", "endpoint": "/api/v1/depth", "method": "POST", "data": {"exchange": "NSE", "token": "22"}},
                {"name": "history", "endpoint": "/api/v1/history", "method": "POST", "data": {"exchange": "NSE", "token": "22", "resolution": "1"}}
            ]

            results = []
            session = requests.Session()

            for test in api_tests:
                try:
                    url = base_url + test["endpoint"]

                    if test["method"] == "GET":
                        response = session.get(url, timeout=10)
                    else:
                        response = session.post(url, json=test.get("data", {}), timeout=10)

                    # Check if response is JSON
                    try:
                        json_data = response.json()
                        is_json = True
                        response_preview = str(json_data)[:200] + "..." if len(str(json_data)) > 200 else str(json_data)
                    except:
                        is_json = False
                        response_preview = response.text[:200] + "..." if len(response.text) > 200 else response.text

                    # Determine status
                    if response.status_code == 200 and is_json:
                        if isinstance(json_data, dict) and json_data.get("status") == "success":
                            status = "success"
                            message = "API working correctly"
                        elif "login" in response.text.lower() or "<!doctype html>" in response.text.lower():
                            status = "auth_required"
                            message = "Authentication required"
                        else:
                            status = "partial"
                            message = "API responding but may need authentication"
                    else:
                        status = "failed"
                        message = f"HTTP {response.status_code}"

                    results.append({
                        "api_name": test["name"],
                        "endpoint": test["endpoint"],
                        "method": test["method"],
                        "status": status,
                        "http_code": response.status_code,
                        "is_json": is_json,
                        "message": message,
                        "response_preview": response_preview,
                        "tested_at": self.get_ist_timestamp()
                    })

                except Exception as e:
                    results.append({
                        "api_name": test["name"],
                        "endpoint": test["endpoint"],
                        "method": test["method"],
                        "status": "error",
                        "http_code": None,
                        "is_json": False,
                        "message": f"Connection error: {str(e)}",
                        "response_preview": "",
                        "tested_at": self.get_ist_timestamp()
                    })

            # Calculate summary
            total_apis = len(results)
            successful_apis = len([r for r in results if r["status"] == "success"])
            failed_apis = len([r for r in results if r["status"] in ["failed", "error"]])
            auth_required_apis = len([r for r in results if r["status"] == "auth_required"])

            return {
                "status": "success",
                "message": f"✅ API testing completed for '{setup_data['setup_name']}'",
                "data": {
                    "setup_id": setup_id,
                    "setup_name": setup_data['setup_name'],
                    "instance_name": instance_name,
                    "base_url": base_url,
                    "summary": {
                        "total_apis": total_apis,
                        "successful": successful_apis,
                        "failed": failed_apis,
                        "auth_required": auth_required_apis,
                        "success_rate": round((successful_apis / total_apis) * 100, 1) if total_apis > 0 else 0
                    },
                    "api_results": results,
                    "tested_at": self.get_ist_timestamp()
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to test APIs: {str(e)}"}

    def get_api_status(self, setup_id):
        """Get quick API status for a broker"""
        try:
            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"status": "error", "message": "Setup not found"}

            port = 5000 + setup_id
            base_url = f"http://127.0.0.1:{port}"

            # Test key APIs quickly
            key_apis = ["funds", "holdings", "positions"]
            session = requests.Session()

            api_status = {}
            for api in key_apis:
                try:
                    response = session.get(f"{base_url}/api/v1/{api}", timeout=5)
                    if response.status_code == 200:
                        try:
                            json_data = response.json()
                            if isinstance(json_data, dict) and json_data.get("status") == "success":
                                api_status[api] = "working"
                            else:
                                api_status[api] = "auth_required"
                        except:
                            api_status[api] = "auth_required"
                    else:
                        api_status[api] = "failed"
                except:
                    api_status[api] = "error"

            # Overall status
            working_count = len([status for status in api_status.values() if status == "working"])
            overall_status = "healthy" if working_count >= 2 else "needs_attention"

            return {
                "status": "success",
                "data": {
                    "setup_id": setup_id,
                    "setup_name": setup_data['setup_name'],
                    "base_url": base_url,
                    "overall_status": overall_status,
                    "api_status": api_status,
                    "working_apis": working_count,
                    "total_tested": len(key_apis),
                    "checked_at": self.get_ist_timestamp()
                }
            }

        except Exception as e:
            return {"status": "error", "message": f"Failed to get API status: {str(e)}"}
    
    def update_setup_instance_info(self, setup_id, instance_info):
        """Update setup with OpenAlgo instance information"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Add instance info as JSON in additional_config
            cursor.execute("""
                UPDATE user_brokers 
                SET additional_config = ?
                WHERE id = ?
            """, (json.dumps(instance_info), setup_id))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Failed to update instance info: {e}")

# Test functions for named manual processes
def test_step_1_set_env():
    """Test STEP 1: SET_ENV"""
    automation = BrokerAutomation()
    result = automation.step_1_set_env(4)
    print("🔧 STEP 1: SET_ENV RESULT:")
    print(json.dumps(result, indent=2))
    return result

def test_step_2_start_algo():
    """Test STEP 2: START_ALGO"""
    automation = BrokerAutomation()
    result = automation.step_2_start_algo(4)
    print("\n🚀 STEP 2: START_ALGO RESULT:")
    print(json.dumps(result, indent=2))
    return result

def test_step_3_register():
    """Test STEP 3: REGISTER"""
    automation = BrokerAutomation()
    result = automation.step_3_register(4)
    print("\n👤 STEP 3: REGISTER RESULT:")
    print(json.dumps(result, indent=2))
    return result

def test_step_4_connect_broker():
    """Test STEP 4: CONNECT_BROKER"""
    automation = BrokerAutomation()
    result = automation.step_4_connect_broker(4)
    print("\n🏦 STEP 4: CONNECT_BROKER RESULT:")
    print(json.dumps(result, indent=2))
    return result

if __name__ == "__main__":
    print("🎯 NAMED MANUAL PROCESS TEST")
    print("=" * 60)

    # Test all 4 steps
    step1_result = test_step_1_set_env()

    if step1_result["status"] == "success":
        print("\n" + "=" * 60)
        print("✅ STEP 1 (SET_ENV) COMPLETED!")
        print(f"📁 Instance: {step1_result['data']['instance_path']}")
        print(f"🌐 Port: {step1_result['data']['port']}")
        print(f"🔗 URL: {step1_result['data']['openalgo_url']}")

        # Test other steps
        step2_result = test_step_2_start_algo()
        if step2_result["status"] == "success":
            print(f"\n🚀 START COMMAND:")
            print(f"   {step2_result['data']['start_command']}")

        step3_result = test_step_3_register()
        if step3_result["status"] == "success":
            print(f"\n👤 REGISTRATION INFO:")
            creds = step3_result['data']['credentials']
            print(f"   Username: {creds['username']}")
            print(f"   Email: {creds['email']}")
            print(f"   Password: {creds['password']}")
            print(f"   Full Name: {creds['full_name']}")

        step4_result = test_step_4_connect_broker()
        if step4_result["status"] == "success":
            print(f"\n🏦 BROKER CONNECTION INFO:")
            broker_creds = step4_result['data']['broker_credentials']
            print(f"   Client ID: {broker_creds['client_id']}")
            print(f"   Trading PIN: {broker_creds['trading_pin']}")
            print(f"   TOTP Code: {broker_creds['totp_code']}")

            if step4_result['data'].get('xts_credentials'):
                print(f"   XTS Market API Key: {step4_result['data']['xts_credentials']['market_api_key']}")

        print("\n" + "=" * 60)
        print("🎯 MANUAL PROCESS SUMMARY:")
        print("✅ STEP 1 (SET_ENV): Environment configured")
        print("✅ STEP 2 (START_ALGO): Ready to start OpenAlgo")
        print("✅ STEP 3 (REGISTER): User registration details ready")
        print("✅ STEP 4 (CONNECT_BROKER): Broker authentication ready")
        print("\n🚀 ALL STEPS READY FOR MANUAL EXECUTION!")

    else:
        print(f"\n❌ STEP 1 (SET_ENV) FAILED:")
        print(f"   {step1_result['message']}")

    # ============================================================================
    # SYSTEM SERVICE MANAGEMENT METHODS
    # ============================================================================

    def start_systemd_service(self, service_name):
        """Start a systemd service"""
        try:
            import subprocess
            result = subprocess.run([
                'sudo', 'systemctl', 'start', service_name
            ], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                return {
                    "status": "success",
                    "message": f"Service {service_name} started successfully"
                }
            else:
                return {
                    "status": "error",
                    "message": f"Failed to start service: {result.stderr}"
                }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Error starting service: {str(e)}"
            }

    def stop_systemd_service(self, service_name):
        """Stop a systemd service"""
        try:
            import subprocess
            result = subprocess.run([
                'sudo', 'systemctl', 'stop', service_name
            ], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                return {
                    "status": "success",
                    "message": f"Service {service_name} stopped successfully"
                }
            else:
                return {
                    "status": "error",
                    "message": f"Failed to stop service: {result.stderr}"
                }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Error stopping service: {str(e)}"
            }

    def restart_systemd_service(self, service_name):
        """Restart a systemd service"""
        try:
            import subprocess
            result = subprocess.run([
                'sudo', 'systemctl', 'restart', service_name
            ], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                return {
                    "status": "success",
                    "message": f"Service {service_name} restarted successfully"
                }
            else:
                return {
                    "status": "error",
                    "message": f"Failed to restart service: {result.stderr}"
                }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Error restarting service: {str(e)}"
            }

    def get_service_logs(self, service_name, lines=50):
        """Get logs for a systemd service"""
        try:
            import subprocess
            result = subprocess.run([
                'sudo', 'journalctl', '-u', service_name, '-n', str(lines), '--no-pager'
            ], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                return {
                    "status": "success",
                    "logs": result.stdout,
                    "lines": lines
                }
            else:
                return {
                    "status": "error",
                    "message": f"Failed to get logs: {result.stderr}",
                    "logs": ""
                }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Error getting logs: {str(e)}",
                "logs": ""
            }

    def start_all_user_services(self, user_id):
        """Start all services for a specific user"""
        try:
            import subprocess

            # Get all services for this user
            result = subprocess.run([
                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'
            ], capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                return {
                    "status": "error",
                    "message": "Failed to list services"
                }

            # Filter services for this user
            user_services = []
            for line in result.stdout.split('\n'):
                if f"{user_id}-algo-" in line and '.service' in line:
                    service_name = line.split()[0]
                    if 'inactive' in line or 'failed' in line:
                        user_services.append(service_name)

            started = 0
            failed = 0

            for service in user_services:
                start_result = self.start_systemd_service(service)
                if start_result.get('status') == 'success':
                    started += 1
                else:
                    failed += 1

            return {
                "status": "success",
                "message": f"Started {started} services, {failed} failed",
                "started": started,
                "failed": failed
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Error starting all services: {str(e)}"
            }

    def stop_all_user_services(self, user_id):
        """Stop all services for a specific user"""
        try:
            import subprocess

            # Get all services for this user
            result = subprocess.run([
                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'
            ], capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                return {
                    "status": "error",
                    "message": "Failed to list services"
                }

            # Filter services for this user
            user_services = []
            for line in result.stdout.split('\n'):
                if f"{user_id}-algo-" in line and '.service' in line:
                    service_name = line.split()[0]
                    if 'active' in line and 'running' in line:
                        user_services.append(service_name)

            stopped = 0
            failed = 0

            for service in user_services:
                stop_result = self.stop_systemd_service(service)
                if stop_result.get('status') == 'success':
                    stopped += 1
                else:
                    failed += 1

            return {
                "status": "success",
                "message": f"Stopped {stopped} services, {failed} failed",
                "stopped": stopped,
                "failed": failed
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Error stopping all services: {str(e)}"
            }

    def cleanup_dead_services(self):
        """Clean up all dead/not-found systemd services"""
        try:
            import subprocess

            # Get all services
            result = subprocess.run([
                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'
            ], capture_output=True, text=True, timeout=60)

            if result.returncode != 0:
                return {
                    "status": "error",
                    "message": "Failed to list services"
                }

            # Find dead algo services
            dead_services = []
            for line in result.stdout.split('\n'):
                if 'algo-' in line and '.service' in line:
                    if 'not-found' in line or ('inactive' in line and 'dead' in line):
                        service_name = line.split()[0]
                        dead_services.append(service_name)

            cleaned = 0
            failed = 0

            print(f"🧹 Found {len(dead_services)} dead services to clean up...")

            for service in dead_services:
                try:
                    # Disable the service
                    disable_result = subprocess.run([
                        'sudo', 'systemctl', 'disable', service
                    ], capture_output=True, text=True, timeout=10)

                    # Reset failed state
                    reset_result = subprocess.run([
                        'sudo', 'systemctl', 'reset-failed', service
                    ], capture_output=True, text=True, timeout=10)

                    # Remove service file if it exists
                    service_file = f"/etc/systemd/system/{service}"
                    remove_result = subprocess.run([
                        'sudo', 'rm', '-f', service_file
                    ], capture_output=True, text=True, timeout=10)

                    print(f"✅ Cleaned up: {service}")
                    cleaned += 1

                except Exception as e:
                    print(f"❌ Failed to clean: {service} - {e}")
                    failed += 1

            # Reload systemd daemon
            subprocess.run(['sudo', 'systemctl', 'daemon-reload'], timeout=30)

            return {
                "status": "success",
                "message": f"Cleaned up {cleaned} dead services, {failed} failed",
                "cleaned": cleaned,
                "failed": failed,
                "total_found": len(dead_services)
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Error cleaning up services: {str(e)}"
            }

    def get_all_system_services(self):
        """Get all system services with their status"""
        try:
            import subprocess

            # Get all services
            result = subprocess.run([
                'sudo', 'systemctl', 'list-units', '--type=service', '--all', '--no-pager'
            ], capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                return {
                    "status": "error",
                    "message": "Failed to list services"
                }

            services = []
            for line in result.stdout.split('\n'):
                if '.service' in line and line.strip():
                    parts = line.split()
                    if len(parts) >= 4:
                        service_name = parts[0]
                        load_state = parts[1]
                        active_state = parts[2]
                        sub_state = parts[3]

                        # Focus on algo services and system services
                        if 'algo-' in service_name or service_name in ['nginx', 'mysql', 'postgresql', 'redis']:
                            services.append({
                                "name": service_name,
                                "load": load_state,
                                "active": active_state,
                                "sub": sub_state,
                                "status": f"{active_state} ({sub_state})"
                            })

            return {
                "status": "success",
                "services": services,
                "count": len(services)
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Error getting system services: {str(e)}"
            }

    def connect_broker_manual_openalgo_style(self, setup_id, manual_credentials=None):
        """
        Manual broker connection following OpenAlgo instance approach
        For Angel: Uses clientid, pin, totp form submission
        For Dhan: Uses direct callback approach
        """
        try:
            print(f"🔗 MANUAL OPENALGO-STYLE CONNECTION: Starting for setup {setup_id}")

            setup_data = self.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"status": "error", "message": "Setup not found"}

            # Get the actual running port
            port = self.get_actual_running_port(setup_id)
            if not port:
                return {"status": "error", "message": "Cannot detect running Algo instance port"}

            print(f"🌐 Using OpenAlgo instance on port: {port}")

            # Check if instance is running
            if not self.check_instance_running(port):
                return {"status": "error", "message": f"OpenAlgo instance not running on port {port}"}

            # Get broker type
            broker_name = setup_data.get('broker_name', '').lower()
            print(f"🏦 Broker type detected: {broker_name}")

            if broker_name == 'angel':
                return self.connect_angel_manual_openalgo_style(setup_id, port, setup_data, manual_credentials)
            elif broker_name == 'dhan':
                return self.connect_dhan_manual_openalgo_style(setup_id, port, setup_data)
            else:
                return {"status": "error", "message": f"Manual connection not supported for broker: {broker_name}"}

        except Exception as e:
            print(f"❌ Manual OpenAlgo-style connection failed: {e}")
            return {"status": "error", "message": f"Manual connection failed: {str(e)}"}

    def connect_angel_manual_openalgo_style(self, setup_id, port, setup_data, manual_credentials):
        """
        Manual Angel broker connection using OpenAlgo instance form submission approach
        """
        try:
            print(f"👼 ANGEL MANUAL CONNECTION: Using OpenAlgo form submission approach")

            # Extract credentials
            if manual_credentials:
                clientid = manual_credentials.get('clientid') or setup_data.get('broker_client_id')
                pin = manual_credentials.get('pin') or setup_data.get('trading_pin')
                totp = manual_credentials.get('totp')
            else:
                clientid = setup_data.get('broker_client_id')
                pin = setup_data.get('trading_pin')
                totp = None

            if not clientid or not pin:
                return {"status": "error", "message": "Missing Angel broker credentials (clientid/pin)"}

            if not totp:
                return {"status": "error", "message": "TOTP is required for Angel broker manual connection"}

            print(f"📋 Using credentials - Client ID: {clientid}, PIN: {'*' * len(pin)}")

            # STEP 1: Login to OpenAlgo instance
            print(f"🔐 STEP 1: Login to OpenAlgo instance...")
            session = self.ultra_fast_login(port, setup_data)
            if not session:
                print(f"❌ Failed to login to OpenAlgo instance on port {port}")
                return {"status": "error", "message": "Failed to login to OpenAlgo instance"}

            # STEP 2: Access Angel callback page to get form
            print(f"📄 STEP 2: Access Angel authentication form...")
            openalgo_url = f"http://127.0.0.1:{port}"
            angel_auth_url = f"{openalgo_url}/angel/callback"

            auth_form_page = session.get(angel_auth_url, timeout=10)
            print(f"📄 Angel auth form response: {auth_form_page.status_code}")
            if auth_form_page.status_code != 200:
                print(f"❌ Failed to access Angel auth form: {auth_form_page.status_code}")
                return {"status": "error", "message": f"Failed to access Angel auth form: {auth_form_page.status_code}"}

            # Extract CSRF token if present
            csrf_token = None
            if 'csrf_token' in auth_form_page.text:
                import re
                csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', auth_form_page.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
                    print(f"🔒 CSRF token extracted")

            # STEP 3: Submit Angel authentication form
            print(f"📤 STEP 3: Submit Angel authentication form...")
            auth_data = {
                "clientid": clientid,
                "pin": pin,
                "totp": totp
            }

            if csrf_token:
                auth_data["csrf_token"] = csrf_token

            auth_response = session.post(
                angel_auth_url,
                data=auth_data,
                allow_redirects=True,
                timeout=15
            )

            print(f"📄 Angel auth response status: {auth_response.status_code}")
            print(f"📄 Angel auth response URL: {auth_response.url}")

            # Check if authentication was successful
            if auth_response.status_code == 200:
                response_text = auth_response.text.lower()

                # Check for success indicators
                if ("dashboard" in auth_response.url.lower() or
                    "dashboard" in response_text or
                    "success" in response_text or
                    "authenticated" in response_text):

                    print("✅ Angel manual authentication successful")

                    # STEP 4: Retrieve and save tokens
                    print(f"🔑 STEP 4: Retrieve authentication tokens...")
                    tokens = self.retrieve_and_save_tokens(port, setup_data)

                    # Update instance info
                    instance_info = {
                        "step_completed": "connect_broker",
                        "status": "broker_connected",
                        "connected_at": self.get_ist_timestamp(),
                        "broker_type": "angel",
                        "connection_method": "manual_openalgo_style"
                    }
                    self.update_setup_instance_info(setup_id, instance_info)

                    return {
                        "status": "success",
                        "message": f"✅ Angel broker connected manually using OpenAlgo style for '{setup_data['setup_name']}'",
                        "data": {
                            "step": "connect_broker",
                            "url": f"http://127.0.0.1:{port}",
                            "connection_status": "connected",
                            "connection_method": "manual_openalgo_style",
                            "auth_tokens": tokens if tokens else {"status": "authenticated"}
                        }
                    }
                else:
                    # Check for error messages in response
                    print(f"❌ Angel auth failed - response text preview: {response_text[:200]}...")
                    if "invalid" in response_text or "error" in response_text:
                        return {"status": "error", "message": "Invalid credentials or TOTP. Please check and try again."}
                    else:
                        return {"status": "error", "message": "Authentication failed. Please verify your credentials."}
            else:
                print(f"❌ Angel authentication failed with status: {auth_response.status_code}")
                return {"status": "error", "message": f"Angel authentication failed with status: {auth_response.status_code}"}

        except Exception as e:
            print(f"❌ Angel manual connection failed: {e}")
            return {"status": "error", "message": f"Angel manual connection failed: {str(e)}"}

    def connect_dhan_manual_openalgo_style(self, setup_id, port, setup_data):
        """
        Manual Dhan broker connection using OpenAlgo instance direct callback approach
        """
        try:
            print(f"🏦 DHAN MANUAL CONNECTION: Using OpenAlgo direct callback approach")

            # STEP 1: Login to OpenAlgo instance
            print(f"🔐 STEP 1: Login to OpenAlgo instance...")
            session = self.ultra_fast_login(port, setup_data)
            if not session:
                return {"status": "error", "message": "Failed to login to OpenAlgo instance"}

            # STEP 2: Trigger Dhan callback directly (no form needed)
            print(f"🔗 STEP 2: Trigger Dhan authentication callback...")
            openalgo_url = f"http://127.0.0.1:{port}"
            dhan_callback_url = f"{openalgo_url}/dhan/callback"

            # Dhan doesn't need form data - just call the callback
            auth_response = session.get(dhan_callback_url, timeout=10, allow_redirects=True)

            print(f"📄 Dhan auth response status: {auth_response.status_code}")
            print(f"📄 Dhan auth response URL: {auth_response.url}")

            # Check if authentication was successful
            if auth_response.status_code == 200:
                response_text = auth_response.text.lower()

                # Check for success indicators
                if ("dashboard" in auth_response.url.lower() or
                    "dashboard" in response_text or
                    "success" in response_text or
                    "authenticated" in response_text):

                    print("✅ Dhan manual authentication successful")

                    # STEP 3: Retrieve and save tokens
                    print(f"🔑 STEP 3: Retrieve authentication tokens...")
                    tokens = self.retrieve_and_save_tokens(port, setup_data)

                    # Update instance info
                    instance_info = {
                        "step_completed": "connect_broker",
                        "status": "broker_connected",
                        "connected_at": self.get_ist_timestamp(),
                        "broker_type": "dhan",
                        "connection_method": "manual_openalgo_style"
                    }
                    self.update_setup_instance_info(setup_id, instance_info)

                    return {
                        "status": "success",
                        "message": f"✅ Dhan broker connected manually using OpenAlgo style for '{setup_data['setup_name']}'",
                        "data": {
                            "step": "connect_broker",
                            "url": f"http://127.0.0.1:{port}",
                            "connection_status": "connected",
                            "connection_method": "manual_openalgo_style",
                            "auth_tokens": tokens if tokens else {"status": "authenticated"}
                        }
                    }
                else:
                    return {"status": "error", "message": "Dhan authentication failed. Please check your broker configuration."}
            else:
                return {"status": "error", "message": f"Dhan authentication failed with status: {auth_response.status_code}"}

        except Exception as e:
            print(f"❌ Dhan manual connection failed: {e}")
            return {"status": "error", "message": f"Dhan manual connection failed: {str(e)}"}
