"""
Broker Service for AlgoFactory API
Business logic for broker-related operations
"""

import sys
import logging
from typing import List, Optional, Dict, Any

# Setup logging
logger = logging.getLogger(__name__)

class BrokerService:
    """Service class for broker operations"""
    
    def __init__(self):
        """Initialize broker service"""
        # Import broker automation
        sys.path.append('/home/<USER>/algofactory/scripts/automation')
        from broker_automation import BrokerAutomation
        self.broker_automation = BrokerAutomation()
        logger.info("✅ Broker service initialized with automation")
    
    def get_all_broker_setups(self) -> List[Dict[str, Any]]:
        """Get all broker setups from database"""
        try:
            logger.info("📋 Getting all broker setups...")
            setups = self.broker_automation.get_all_broker_setups()

            logger.info(f"✅ Retrieved {len(setups)} broker setups")
            return setups

        except Exception as e:
            logger.error(f"❌ Error getting broker setups: {e}")
            raise Exception(f"Failed to get broker setups: {str(e)}")
    
    def create_broker_setup(self, setup_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new broker setup using the multi-tenant API approach"""
        try:
            logger.info(f"🆕 Creating new broker setup for user: {setup_data.get('username')}")

            # Import required modules
            import sqlite3
            import json
            from datetime import datetime, timezone, timedelta

            # Database path
            db_path = "/home/<USER>/algofactory/database/algofactory.db"

            # Get IST timestamp
            ist = timezone(timedelta(hours=5, minutes=30))
            created_at_ist = datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S')

            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                # Get setup data
                username = setup_data.get('username')
                broker_name = setup_data.get('broker_name')
                admin_id = setup_data.get('admin_id', 'Admin_1')
                creator_name = setup_data.get('created_by', 'superadmin')

                # Validate username format
                if not username or not username.isdigit():
                    return {
                        "success": False,
                        "message": f"Invalid username format: {username}. Should be numeric like '1001'"
                    }

                # Check if user exists
                cursor.execute("SELECT username FROM users WHERE username = ?", (username,))
                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": f"User {username} not found"
                    }

                # Insert broker setup into user_brokers table first to get the ID
                cursor.execute("""
                    INSERT INTO user_brokers (
                        username, broker_name, setup_name, instance_number,
                        instance_name, broker_client_id, broker_api_key,
                        broker_api_secret, broker_api_key_market, trading_pin,
                        totp_secret, setup_status, connection_status, additional_config
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'not_connected', ?)
                """, (
                    username,
                    broker_name,
                    setup_data.get('setup_name'),
                    1,  # temporary instance number
                    'temp',  # temporary instance name
                    setup_data.get('broker_client_id'),
                    setup_data.get('broker_api_key'),
                    setup_data.get('broker_api_secret'),
                    setup_data.get('broker_api_key_market', ''),
                    setup_data.get('trading_pin'),
                    setup_data.get('totp_secret'),
                    json.dumps({
                        "creator": creator_name,
                        "created_at_ist": created_at_ist,
                        "created_for": username
                    })
                ))

                setup_id = cursor.lastrowid

                # Now update with correct instance name using the actual setup_id
                correct_instance_name = f"{username}-algo-{broker_name}-{setup_id}"
                cursor.execute("""
                    UPDATE user_brokers
                    SET instance_name = ?, instance_number = ?
                    WHERE id = ?
                """, (correct_instance_name, setup_id, setup_id))

                # Update with final instance info using setup_id
                final_instance_name = f"{username}-algo-{broker_name}-{setup_id}"

                cursor.execute("""
                    UPDATE user_brokers
                    SET instance_number = ?, instance_name = ?
                    WHERE id = ?
                """, (setup_id, final_instance_name, setup_id))

                conn.commit()

                logger.info(f"✅ Broker setup created successfully with ID: {setup_id}")

                # Try to run automation
                try:
                    logger.info(f"🚀 Starting broker automation for setup_id: {setup_id}")

                    # STEP 1: SET_ENV - Create instance folder, clone OpenAlgo, configure .env
                    automation_result = self.broker_automation.step_1_set_env(setup_id)

                    if automation_result['status'] == 'success':
                        # Update status to automated
                        cursor.execute("""
                            UPDATE user_brokers
                            SET setup_status = 'env_configured', detailed_status = ?
                            WHERE id = ?
                        """, (json.dumps(automation_result), setup_id))
                        conn.commit()

                        return {
                            "success": True,
                            "message": "Broker setup created and environment configured successfully",
                            "setup_id": setup_id,
                            "automation_status": "env_configured",
                            "instance_path": automation_result.get('data', {}).get('instance_path'),
                            "algo_url": automation_result.get('data', {}).get('algo_url') or f"http://127.0.0.1:{5000 + setup_id}",
                            "data": {
                                "username": username,
                                "broker_name": broker_name,
                                "setup_name": setup_data.get('setup_name'),
                                "instance_name": final_instance_name,
                                "admin_id": admin_id
                            }
                        }
                    else:
                        # Update status to failed
                        cursor.execute("""
                            UPDATE user_brokers
                            SET setup_status = 'failed', detailed_status = ?
                            WHERE id = ?
                        """, (json.dumps(automation_result), setup_id))
                        conn.commit()

                        return {
                            "success": True,
                            "message": "Broker setup created but automation failed",
                            "setup_id": setup_id,
                            "automation_status": "failed",
                            "automation_error": automation_result.get('message'),
                            "data": {
                                "username": username,
                                "broker_name": broker_name,
                                "setup_name": setup_data.get('setup_name'),
                                "instance_name": final_instance_name,
                                "admin_id": admin_id
                            }
                        }

                except Exception as automation_error:
                    logger.error(f"❌ Automation failed: {automation_error}")

                    # Update status to error
                    cursor.execute("""
                        UPDATE user_brokers
                        SET setup_status = 'error', detailed_status = ?
                        WHERE id = ?
                    """, (json.dumps({"error": str(automation_error)}), setup_id))
                    conn.commit()

                    return {
                        "success": True,
                        "message": "Broker setup created but automation system failed",
                        "setup_id": setup_id,
                        "automation_status": "error",
                        "automation_error": str(automation_error),
                        "data": {
                            "username": username,
                            "broker_name": broker_name,
                            "setup_name": setup_data.get('setup_name'),
                            "instance_name": final_instance_name,
                            "admin_id": admin_id
                        }
                    }

        except Exception as e:
            logger.error(f"❌ Error creating broker setup: {e}")
            return {
                "success": False,
                "message": f"Failed to create broker setup: {str(e)}"
            }

    def create_broker_setup_from_saved(self, saved_broker_id: int, user_id: str, admin_id: str) -> Dict[str, Any]:
        """Create a new broker setup from saved broker configuration"""
        try:
            logger.info(f"🆕 Creating broker setup from saved configuration ID: {saved_broker_id}")
            logger.info(f"🔍 Debug: user_id={user_id}, admin_id={admin_id}, saved_broker_id={saved_broker_id}")

            # Import required modules
            import sqlite3
            from datetime import datetime, timezone, timedelta

            # Database path
            db_path = "/home/<USER>/algofactory/database/algofactory.db"

            # Get IST timestamp
            ist = timezone(timedelta(hours=5, minutes=30))
            created_at_ist = datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S')

            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                # Get saved broker configuration
                cursor.execute("""
                    SELECT user_id, admin_id, broker_name, broker_type, trading_setup_name,
                           client_id, api_key, api_secret, trading_pin, totp_secret
                    FROM saved_broker_details
                    WHERE id = ?
                """, (saved_broker_id,))

                saved_broker = cursor.fetchone()
                if not saved_broker:
                    return {
                        "success": False,
                        "message": f"Saved broker configuration {saved_broker_id} not found"
                    }

                # Extract saved broker data
                saved_user_id = saved_broker[0]
                saved_admin_id = saved_broker[1]
                broker_name = saved_broker[2]
                setup_name = saved_broker[4] or f"{broker_name}_setup"
                client_id = saved_broker[5]
                api_key = saved_broker[6]
                api_secret = saved_broker[7]
                trading_pin = saved_broker[8]
                totp_secret = saved_broker[9]

                # Use provided user_id and admin_id, or fall back to saved ones
                target_user_id = user_id if user_id else saved_user_id
                target_admin_id = admin_id if admin_id else saved_admin_id

                logger.info(f"📋 Using saved broker config: {setup_name} for user {target_user_id}")
                logger.info(f"🔍 Debug: saved_user_id={saved_user_id}, provided_user_id={user_id}, final_target_user_id={target_user_id}")

                # Validate username format
                if not target_user_id or not str(target_user_id).isdigit():
                    return {
                        "success": False,
                        "message": f"Invalid username format: {target_user_id}. Should be numeric like '1001'"
                    }

                # Check if user exists
                cursor.execute("SELECT username FROM users WHERE username = ?", (str(target_user_id),))
                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": f"User {target_user_id} not found"
                    }

                # Get next instance number for this user and broker
                cursor.execute("""
                    SELECT COALESCE(MAX(instance_number), 0) + 1
                    FROM user_brokers
                    WHERE username = ? AND broker_name = ?
                """, (str(target_user_id), broker_name))
                instance_number = cursor.fetchone()[0]

                # Insert broker setup into user_brokers table first with temporary instance name
                cursor.execute("""
                    INSERT INTO user_brokers (
                        username, broker_name, setup_name, instance_number,
                        instance_name, broker_client_id, broker_api_key,
                        broker_api_secret, trading_pin, totp_secret,
                        setup_status, connection_status, http_port, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'stopped', 'not_connected', ?, ?)
                """, (
                    str(target_user_id),
                    broker_name,
                    setup_name,
                    instance_number,
                    f"temp-{target_user_id}-{broker_name}",  # Temporary name
                    client_id,
                    api_key,
                    api_secret,
                    trading_pin,
                    totp_secret,
                    5000 + int(target_user_id),  # Port based on user ID
                    created_at_ist
                ))

                setup_id = cursor.lastrowid

                # Now update with correct instance name using the actual setup_id
                correct_instance_name = f"{target_user_id}-algo-{broker_name}-{setup_id}"
                cursor.execute("""
                    UPDATE user_brokers
                    SET instance_name = ?
                    WHERE id = ?
                """, (correct_instance_name, setup_id))

                conn.commit()

                logger.info(f"✅ Broker setup created successfully with ID: {setup_id}")

                # Now run SET_ENV step to create the instance folder
                logger.info(f"🔧 Running SET_ENV step for setup ID: {setup_id}")
                env_result = self.broker_automation.step_1_set_env(setup_id)

                if env_result.get('status') == 'success':
                    logger.info(f"✅ SET_ENV completed successfully for setup ID: {setup_id}")
                    final_status = "env_configured"

                    # Update status in database
                    cursor.execute("UPDATE user_brokers SET setup_status = ? WHERE id = ?",
                                 (final_status, setup_id))
                    conn.commit()
                else:
                    logger.warning(f"⚠️ SET_ENV failed for setup ID: {setup_id}: {env_result.get('message')}")
                    final_status = "setup_failed"

                return {
                    "success": True,
                    "message": f"Broker setup '{setup_name}' created and configured successfully",
                    "data": {
                        "setup_id": setup_id,
                        "username": target_user_id,
                        "broker_name": broker_name,
                        "setup_name": setup_name,
                        "instance_name": correct_instance_name,
                        "instance_number": instance_number,
                        "status": final_status,
                        "connection_status": "not_connected",
                        "saved_broker_id": saved_broker_id,
                        "env_setup": env_result.get('status') == 'success'
                    }
                }

        except Exception as e:
            logger.error(f"❌ Error creating broker setup from saved config: {e}")
            return {
                "success": False,
                "message": f"Failed to create broker setup: {str(e)}"
            }

    def get_broker_setup_by_id(self, setup_id: int) -> Optional[Dict[str, Any]]:
        """Get a specific broker setup by ID"""
        try:
            logger.info(f"🔍 Getting broker setup: {setup_id}")
            
            setup = self.broker_automation.get_setup_by_id(setup_id)
            
            if setup:
                logger.info(f"✅ Broker setup found: {setup_id}")
                return setup
            else:
                logger.warning(f"⚠️ Broker setup not found: {setup_id}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error getting broker setup {setup_id}: {e}")
            raise Exception(f"Failed to get broker setup: {str(e)}")
    
    # REMOVED: connect_broker_manual() - Old method, use run_complete_manual_setup() instead

    def start_broker(self, setup_id: int) -> Dict[str, Any]:
        """Start broker instance with proper status tracking"""
        try:
            logger.info(f"▶️ Starting broker for setup: {setup_id}")

            # Update status to starting
            self.broker_automation.update_setup_status(setup_id, 'starting', 'not_connected')

            # Step 2: Start Algo
            result = self.broker_automation.step_2_start_algo(setup_id)

            if result.get('status') == 'success':
                logger.info(f"✅ Broker started successfully for setup: {setup_id}")

                # Update status to running
                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')

                return {
                    "success": True,
                    "message": "Broker started successfully",
                    "data": result,
                    "setup_status": "running",
                    "connection_status": "not_connected"
                }
            else:
                logger.error(f"❌ Failed to start broker for setup: {setup_id}")

                # Update status to failed
                self.broker_automation.update_setup_status(setup_id, 'failed', 'not_connected')

                return {
                    "success": False,
                    "message": result.get('message', 'Failed to start broker'),
                    "setup_status": "failed",
                    "connection_status": "not_connected"
                }

        except Exception as e:
            logger.error(f"❌ Error starting broker for setup {setup_id}: {e}")

            # Update status to error
            self.broker_automation.update_setup_status(setup_id, 'error', 'not_connected')

            return {
                "success": False,
                "message": f"Failed to start broker: {str(e)}",
                "setup_status": "error",
                "connection_status": "not_connected"
            }

    def disconnect_broker(self, setup_id: int) -> Dict[str, Any]:
        """Disconnect broker and logout from OpenAlgo"""
        try:
            logger.info(f"🔌 Disconnecting broker and logging out for setup: {setup_id}")

            # Get setup data
            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {
                    "success": False,
                    "message": "Setup not found"
                }

            # Real logout from OpenAlgo with proper auth token revocation
            http_port = setup_data.get('http_port')
            base_url = f"http://127.0.0.1:{http_port}"

            try:
                import requests
                import sqlite3

                # Step 1: Call logout endpoints
                logout_response = requests.post(f"{base_url}/logout", timeout=10)
                logger.info(f"🔌 Logout response: {logout_response.status_code}")

                # Step 2: Also try broker logout if available
                try:
                    broker_logout = requests.post(f"{base_url}/auth/logout", timeout=5)
                    logger.info(f"🏦 Broker logout response: {broker_logout.status_code}")
                except:
                    pass  # Broker logout might not exist

                # Step 3: CRITICAL - Directly revoke auth tokens in OpenAlgo database
                instance_name = f"{setup_data['username']}-algo-{setup_data['broker_name']}-{setup_id}"
                openalgo_db_path = f"/home/<USER>/algofactory/algo/instances/{instance_name}/db/openalgo.db"

                try:
                    with sqlite3.connect(openalgo_db_path) as conn:
                        cursor = conn.cursor()

                        # Set is_revoked = 1 for all auth tokens
                        cursor.execute("UPDATE auth SET is_revoked = 1")

                        # Clear auth tokens for extra security
                        cursor.execute("UPDATE auth SET auth = '', feed_token = ''")

                        conn.commit()
                        logger.info(f"✅ Auth tokens revoked in OpenAlgo database")

                        # Verify revocation
                        cursor.execute("SELECT name, is_revoked FROM auth")
                        auth_status = cursor.fetchall()
                        logger.info(f"🔍 Auth status after revocation: {auth_status}")

                except Exception as db_error:
                    logger.error(f"❌ Failed to revoke auth tokens in database: {db_error}")

            except Exception as logout_error:
                logger.warning(f"⚠️ Logout API call failed (continuing): {logout_error}")

            # Update status to disconnected (keep algo running)
            self.broker_automation.update_setup_status(setup_id, 'running', 'disconnected')

            return {
                "success": True,
                "message": "✅ Broker disconnected and logged out successfully. Algo service still running.",
                "setup_status": "running",
                "connection_status": "disconnected",
                "logout_performed": True,
                "data": {
                    "action": "logout",
                    "algo_status": "running",
                    "broker_status": "disconnected",
                    "auth_tokens_revoked": True
                }
            }

        except Exception as e:
            logger.error(f"❌ Error disconnecting broker for setup {setup_id}: {e}")

            return {
                "success": False,
                "message": f"Failed to disconnect broker: {str(e)}"
            }

    def stop_broker(self, setup_id: int) -> Dict[str, Any]:
        """Stop and disable broker instance"""
        try:
            logger.info(f"⏹️ Stopping and disabling broker for setup: {setup_id}")

            # Update status to stopping
            self.broker_automation.update_setup_status(setup_id, 'stopping', 'not_connected')

            result = self.broker_automation.stop_algo(setup_id)

            if result.get('status') == 'success':
                logger.info(f"✅ Broker stopped and disabled successfully for setup: {setup_id}")

                # Update status to stopped
                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')

                return {
                    "success": True,
                    "message": "Broker stopped and disabled successfully",
                    "data": result,
                    "setup_status": "stopped",
                    "connection_status": "not_connected"
                }
            else:
                logger.error(f"❌ Failed to stop broker for setup: {setup_id}")

                # Update status to error
                self.broker_automation.update_setup_status(setup_id, 'error', 'not_connected')

                return {
                    "success": False,
                    "message": result.get('message', 'Failed to stop broker'),
                    "setup_status": "error",
                    "connection_status": "not_connected"
                }

        except Exception as e:
            logger.error(f"❌ Error stopping broker for setup {setup_id}: {e}")

            # Update status to error
            self.broker_automation.update_setup_status(setup_id, 'error', 'not_connected')

            return {
                "success": False,
                "message": f"Failed to stop broker: {str(e)}",
                "setup_status": "error",
                "connection_status": "not_connected"
            }

    # REMOVED: connect_broker_auto() - Old version, keeping the newer ULTRA FAST version below

    def get_broker_logs(self, setup_id: int) -> Dict[str, Any]:
        """Get broker logs"""
        try:
            logger.info(f"📄 Getting logs for setup: {setup_id}")

            result = self.broker_automation.get_logs(setup_id)

            if result.get('status') == 'success':
                logger.info(f"✅ Logs retrieved successfully for setup: {setup_id}")
                return {
                    "success": True,
                    "message": "Logs retrieved successfully",
                    "logs": result.get('logs', 'No logs available')
                }
            else:
                logger.error(f"❌ Failed to get logs for setup: {setup_id}")
                return {
                    "success": False,
                    "message": result.get('message', 'Failed to get logs')
                }

        except Exception as e:
            logger.error(f"❌ Error getting logs for setup {setup_id}: {e}")
            return {
                "success": False,
                "message": f"Failed to get logs: {str(e)}"
            }

    def delete_broker_setup(self, setup_id: int) -> Dict[str, Any]:
        """Fast delete broker setup completely"""
        try:
            logger.info(f"🗑️ Fast deleting broker setup: {setup_id}")

            # Get setup data to find service name and paths
            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {
                    "success": False,
                    "message": "Setup not found"
                }

            service_name = setup_data.get('instance_name', f"algo-{setup_data.get('username')}-{setup_data.get('broker_name')}-{setup_id}")

            # Step 1: Stop and disable service
            import subprocess
            try:
                subprocess.run(['sudo', 'systemctl', 'stop', service_name], capture_output=True, text=True, timeout=10)
                subprocess.run(['sudo', 'systemctl', 'disable', service_name], capture_output=True, text=True, timeout=10)

                # Remove service file
                service_file = f"/etc/systemd/system/{service_name}.service"
                subprocess.run(['sudo', 'rm', '-f', service_file], capture_output=True, text=True, timeout=10)

                # Reload systemd
                subprocess.run(['sudo', 'systemctl', 'daemon-reload'], capture_output=True, text=True, timeout=10)
                subprocess.run(['sudo', 'systemctl', 'reset-failed'], capture_output=True, text=True, timeout=10)

            except Exception as service_error:
                logger.warning(f"Service cleanup failed (continuing): {service_error}")

            # Step 2: Remove instance folder
            try:
                instance_path = f"/home/<USER>/algofactory/algo/instances/{service_name}"
                subprocess.run(['sudo', 'rm', '-rf', instance_path], capture_output=True, text=True, timeout=10)
            except Exception as folder_error:
                logger.warning(f"Folder cleanup failed (continuing): {folder_error}")

            # Step 3: Remove from database
            import sqlite3
            db_path = "/home/<USER>/algofactory/database/algofactory.db"

            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM user_brokers WHERE id = ?", (setup_id,))
                conn.commit()

            logger.info(f"✅ Broker setup deleted successfully: {setup_id}")
            return {
                "success": True,
                "message": f"Broker setup {service_name} deleted successfully",
                "service_name": service_name,
                "setup_id": setup_id,
                "details": {
                    "service_stopped": True,
                    "service_file_removed": True,
                    "folder_removed": True,
                    "database_removed": True
                }
            }

        except Exception as e:
            logger.error(f"❌ Error deleting broker setup {setup_id}: {e}")
            return {
                "success": True,  # Still return success if most cleanup worked
                "message": f"Broker setup deleted with some warnings: {str(e)}",
                "setup_id": setup_id,
                "warning": str(e)
            }

    def start_broker_automation(self, setup_id: int) -> Dict[str, Any]:
        """Start full broker automation"""
        try:
            logger.info(f"🚀 Starting broker automation for setup: {setup_id}")
            
            result = self.broker_automation.run_full_automation(setup_id)
            
            if result.get('success'):
                logger.info(f"✅ Broker automation completed successfully: {setup_id}")
            else:
                logger.error(f"❌ Broker automation failed: {result.get('message')}")
            
            return result
                
        except Exception as e:
            logger.error(f"❌ Error starting broker automation {setup_id}: {e}")
            return {
                "success": False,
                "message": f"Failed to start broker automation: {str(e)}"
            }
    
    def get_supported_brokers(self) -> List[Dict[str, Any]]:
        """Get list of supported brokers"""
        try:
            logger.info("📋 Getting supported brokers...")
            
            brokers = [
                {
                    "name": "angel",
                    "display_name": "Angel One",
                    "status": "active",
                    "description": "Angel One broker integration"
                },
                {
                    "name": "dhan",
                    "display_name": "Dhan",
                    "status": "active",
                    "description": "Dhan broker integration"
                },
                {
                    "name": "zerodha",
                    "display_name": "Zerodha",
                    "status": "coming_soon",
                    "description": "Zerodha broker integration (coming soon)"
                },
                {
                    "name": "upstox",
                    "display_name": "Upstox",
                    "status": "coming_soon",
                    "description": "Upstox broker integration (coming soon)"
                }
            ]
            
            logger.info(f"✅ Retrieved {len(brokers)} supported brokers")
            return brokers
            
        except Exception as e:
            logger.error(f"❌ Error getting supported brokers: {e}")
            raise Exception(f"Failed to get supported brokers: {str(e)}")
    
    def delete_broker_setup(self, setup_id: int) -> Dict[str, Any]:
        """Delete a broker setup"""
        try:
            logger.info(f"🗑️ Deleting broker setup: {setup_id}")
            
            result = self.broker_automation.delete_setup(setup_id)

            # Fix: broker_automation returns 'status' not 'success'
            if result.get('status') == 'success':
                logger.info(f"✅ Broker setup deleted successfully: {setup_id}")
                return {
                    "success": True,
                    "message": result.get('message', f"Broker setup {setup_id} deleted successfully")
                }
            else:
                logger.error(f"❌ Failed to delete broker setup: {result.get('message')}")
                return {
                    "success": False,
                    "message": result.get('message', 'Failed to delete broker setup')
                }
                
        except Exception as e:
            logger.error(f"❌ Error deleting broker setup {setup_id}: {e}")
            return {
                "success": False,
                "message": f"Failed to delete broker setup: {str(e)}"
            }

    def fast_start_broker(self, setup_id: int) -> Dict[str, Any]:
        """Fast start broker using simple systemd commands"""
        try:
            logger.info(f"⚡ Fast starting broker for setup: {setup_id}")

            # Get setup data to find service name
            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {
                    "success": False,
                    "message": "Setup not found"
                }

            service_name = setup_data.get('instance_name', f"{setup_data.get('username')}-algo-{setup_data.get('broker_name')}-{setup_id}")

            # Check if systemd service exists, if not create it
            import subprocess
            import os

            service_file = f"/etc/systemd/system/{service_name}.service"
            if not os.path.exists(service_file):
                logger.info(f"🔧 Service file not found, creating: {service_name}")

                # Create systemd service
                instance_path = f"/home/<USER>/algofactory/algo/instances/{service_name}"
                service_result = self.broker_automation.create_systemd_service(setup_id, instance_path, setup_data)

                if not service_result:
                    return {
                        "success": False,
                        "message": "Failed to create systemd service"
                    }

            # Use simple systemd start
            result = self.broker_automation.start_service(service_name)

            if result.get('status') == 'success':
                # Update status in database
                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')

                return {
                    "success": True,
                    "message": "Broker started successfully",
                    "service_name": service_name,
                    "setup_status": "running",
                    "connection_status": "not_connected"
                }
            else:
                return {
                    "success": False,
                    "message": result.get('message', 'Failed to start service')
                }

        except Exception as e:
            logger.error(f"❌ Error fast starting broker {setup_id}: {e}")
            return {
                "success": False,
                "message": f"Failed to start broker: {str(e)}"
            }

    def fast_stop_broker(self, setup_id: int) -> Dict[str, Any]:
        """Fast stop broker using simple systemd commands"""
        try:
            logger.info(f"⚡ Fast stopping broker for setup: {setup_id}")

            # Get setup data to find service name
            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {
                    "success": False,
                    "message": "Setup not found"
                }

            service_name = setup_data.get('instance_name', f"algo-{setup_data.get('username')}-{setup_data.get('broker_name')}-{setup_id}")

            # Use simple systemd stop
            result = self.broker_automation.stop_service(service_name)

            if result.get('status') == 'success':
                # Update status in database
                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')

                return {
                    "success": True,
                    "message": "Broker stopped successfully",
                    "service_name": service_name,
                    "setup_status": "stopped",
                    "connection_status": "not_connected"
                }
            else:
                return {
                    "success": False,
                    "message": result.get('message', 'Failed to stop service')
                }

        except Exception as e:
            logger.error(f"❌ Error fast stopping broker {setup_id}: {e}")
            return {
                "success": False,
                "message": f"Failed to stop broker: {str(e)}"
            }

    def sync_broker_status(self, setup_id: int) -> Dict[str, Any]:
        """Sync broker status with actual systemd service status"""
        try:
            logger.info(f"🔄 Syncing broker status for setup: {setup_id}")

            # Get setup data
            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"success": False, "message": "Setup not found"}

            service_name = setup_data.get('instance_name', f"{setup_data.get('username')}-algo-{setup_data.get('broker_name')}-{setup_id}")

            # Check actual systemd service status
            import subprocess
            try:
                result = subprocess.run(['sudo', 'systemctl', 'is-active', service_name],
                                      capture_output=True, text=True, timeout=5)

                is_active = result.stdout.strip() == 'active'

                # Update database with real status (preserve existing connection_status)
                current_connection_status = self.broker_automation.get_setup_connection_status(setup_id)
                if is_active:
                    # Only update setup_status, preserve connection_status
                    self.broker_automation.update_setup_status(setup_id, 'running', current_connection_status or 'not_connected')
                    actual_status = 'running'
                else:
                    # If service is stopped, connection must be not_connected
                    self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')
                    actual_status = 'stopped'

                return {
                    "success": True,
                    "message": "Status synced successfully",
                    "service_name": service_name,
                    "actual_status": actual_status,
                    "is_active": is_active
                }

            except subprocess.TimeoutExpired:
                return {"success": False, "message": "Timeout checking service status"}

        except Exception as e:
            logger.error(f"❌ Error syncing broker status {setup_id}: {e}")
            return {"success": False, "message": f"Failed to sync status: {str(e)}"}

    def sync_broker_status(self, setup_id: int) -> Dict[str, Any]:
        """Sync broker status with actual systemd service status"""
        try:
            logger.info(f"🔄 Syncing broker status for setup: {setup_id}")

            # Get setup data
            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {
                    "success": False,
                    "message": "Setup not found"
                }

            service_name = setup_data.get('instance_name', f"{setup_data.get('username')}-algo-{setup_data.get('broker_name')}-{setup_id}")

            # Check actual systemd service status
            import subprocess
            try:
                result = subprocess.run([
                    'sudo', 'systemctl', 'is-active', service_name
                ], capture_output=True, text=True, timeout=5)

                is_active = result.stdout.strip() == 'active'

                # Update database with real status (preserve existing connection_status)
                current_connection_status = self.broker_automation.get_setup_connection_status(setup_id)
                if is_active:
                    # Only update setup_status, preserve connection_status
                    self.broker_automation.update_setup_status(setup_id, 'running', current_connection_status or 'not_connected')
                    actual_status = 'running'
                else:
                    # If service is stopped, connection must be not_connected
                    self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')
                    actual_status = 'stopped'

                return {
                    "success": True,
                    "message": "Status synced successfully",
                    "service_name": service_name,
                    "actual_status": actual_status,
                    "is_active": is_active
                }

            except subprocess.TimeoutExpired:
                return {
                    "success": False,
                    "message": "Timeout checking service status"
                }

        except Exception as e:
            logger.error(f"❌ Error syncing broker status {setup_id}: {e}")
            return {
                "success": False,
                "message": f"Failed to sync status: {str(e)}"
            }

    def start_broker(self, setup_id: int) -> Dict[str, Any]:
        """Fast start broker instance - just start systemd service"""
        try:
            logger.info(f"🚀 Fast starting broker for setup: {setup_id}")

            # Get setup data to determine service name
            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {"success": False, "message": "Setup not found"}

            # Calculate service name
            username = setup_data.get('user_username', setup_data.get('username', '1000'))
            service_name = f"{username}-algo-{setup_data['broker_name']}-{setup_data['id']}"

            logger.info(f"🔄 Starting service: {service_name}")

            # Check if systemd service exists first
            check_result = self.broker_automation.run_systemctl('status', service_name)

            if "could not be found" in check_result.stderr.lower() or "not found" in check_result.stderr.lower():
                # Service doesn't exist - need to create it first
                logger.info(f"🔧 Service not found, creating systemd service: {service_name}")

                # Get instance path
                instance_path = f"/home/<USER>/algofactory/algo/instances/{service_name}"

                # Create systemd service
                service_created = self.broker_automation.create_systemd_service(setup_id, instance_path, setup_data)

                if not service_created:
                    result = {
                        "status": "error",
                        "message": "Failed to create systemd service"
                    }
                else:
                    logger.info(f"✅ Systemd service created successfully: {service_name}")

                    # Now start the service
                    start_result = self.broker_automation.run_systemctl('start', service_name)

                    if start_result.returncode == 0:
                        port = 5000 + setup_id
                        result = {
                            "status": "success",
                            "message": f"Service {service_name} created and started successfully",
                            "service_name": service_name,
                            "port": port,
                            "service_created": True
                        }
                    else:
                        result = {
                            "status": "error",
                            "message": f"Service created but failed to start: {start_result.stderr}"
                        }
            else:
                # Service exists - just start it (ULTRA-FAST!)
                logger.info(f"⚡ ULTRA-FAST START: Service exists, just starting: {service_name}")
                start_result = self.broker_automation.run_systemctl('start', service_name)

                if start_result.returncode == 0:
                    port = 5000 + setup_id
                    result = {
                        "status": "success",
                        "message": f"Service {service_name} started successfully (ultra-fast)",
                        "service_name": service_name,
                        "port": port,
                        "ultra_fast_start": True
                    }
                else:
                    result = {
                        "status": "error",
                        "message": f"Failed to start service: {start_result.stderr}"
                    }

            if result.get('status') == 'success':
                logger.info(f"✅ Broker started successfully for setup: {setup_id}")

                # Update status to running
                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')

                # START BUTTON ONLY STARTS SERVICE - NO AUTO REGISTER
                # User can manually register later if needed

                return {
                    "success": True,
                    "message": result.get('message', 'Broker started successfully'),
                    "data": result
                }
            else:
                # Update status back to stopped on failure
                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')
                return {
                    "success": False,
                    "message": result.get('message', 'Failed to start broker')
                }

        except Exception as e:
            logger.error(f"❌ Error starting broker {setup_id}: {e}")
            # Update status back to stopped on error
            self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')
            return {
                "success": False,
                "message": f"Failed to start broker: {str(e)}"
            }

    def stop_broker(self, setup_id: int) -> Dict[str, Any]:
        """Stop broker instance with proper status tracking"""
        try:
            logger.info(f"🛑 Stopping broker for setup: {setup_id}")

            # Update status to stopping
            self.broker_automation.update_setup_status(setup_id, 'stopping', 'not_connected')

            result = self.broker_automation.stop_algo(setup_id)

            if result.get('status') == 'success':
                logger.info(f"✅ Broker stopped successfully for setup: {setup_id}")

                # Update status to stopped
                self.broker_automation.update_setup_status(setup_id, 'stopped', 'not_connected')

                return {
                    "success": True,
                    "message": result.get('message', 'Broker stopped successfully'),
                    "data": result
                }
            else:
                # Update status back to running on failure
                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')
                return {
                    "success": False,
                    "message": result.get('message', 'Failed to stop broker')
                }

        except Exception as e:
            logger.error(f"❌ Error stopping broker {setup_id}: {e}")
            # Update status back to running on error
            self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')
            return {
                "success": False,
                "message": f"Failed to stop broker: {str(e)}"
            }

    def connect_broker_auto(self, setup_id: int) -> Dict[str, Any]:
        """ULTRA FAST auto connect broker - optimized for speed"""
        try:
            logger.info(f"⚡ Ultra fast auto connecting broker for setup: {setup_id}")

            # Update status to connecting
            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')

            # Use ultra fast connect function
            result = self.broker_automation.ultra_fast_connect_broker(setup_id)

            if result.get('status') == 'success':
                logger.info(f"✅ Broker connected ultra-fast for setup: {setup_id}")

                # Update status to connected
                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')

                return {
                    "success": True,
                    "message": result.get('message', 'Broker connected ultra-fast'),
                    "data": result
                }
            else:
                # Update status back to not_connected on failure
                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')
                return {
                    "success": False,
                    "message": result.get('message', 'Failed to connect broker')
                }

        except Exception as e:
            logger.error(f"❌ Error auto connecting broker {setup_id}: {e}")
            # Update status back to not_connected on error
            self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')
            return {
                "success": False,
                "message": f"Failed to connect broker: {str(e)}"
            }

    def auto_connect_broker_with_automation(self, setup_id: int) -> Dict[str, Any]:
        """Auto connect broker using the automation system (step_4_connect_broker)"""
        try:
            logger.info(f"🤖 Auto connecting broker with automation for setup: {setup_id}")

            # Update status to connecting
            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')

            # Use the automation system's step_4_connect_broker method
            result = self.broker_automation.step_4_connect_broker(setup_id)

            if result.get('status') == 'success':
                logger.info(f"✅ Broker auto-connected with automation for setup: {setup_id}")

                # Update status to connected
                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')

                return {
                    "success": True,
                    "message": result.get('message', 'Broker connected successfully'),
                    "data": result.get('data', {})
                }
            else:
                logger.error(f"❌ Failed to auto-connect broker with automation: {result.get('message')}")

                # Update status back to not connected
                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')

                return {
                    "success": False,
                    "message": result.get('message', 'Failed to connect broker with automation')
                }

        except Exception as e:
            logger.error(f"❌ Error in auto broker connection with automation: {e}")

            # Update status back to not connected on error
            self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')

            return {
                "success": False,
                "message": f"Failed to auto-connect broker with automation: {str(e)}"
            }

    def get_broker_oauth_url(self, setup_id: int) -> Dict[str, Any]:
        """Get OAuth URL and credentials for manual broker connection"""
        try:
            logger.info(f"🔗 Getting OAuth URL for setup: {setup_id}")

            # Get setup data
            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {
                    "success": False,
                    "message": "Setup not found"
                }

            broker_name = setup_data.get('broker_name', '').lower()

            if broker_name == 'flattrade':
                # Generate Flattrade OAuth URL
                api_key = setup_data.get('broker_api_key', '').split(':::')[1] if ':::' in setup_data.get('broker_api_key', '') else setup_data.get('broker_api_key', '')
                oauth_url = f"https://auth.flattrade.in/?app_key={api_key}"

                return {
                    "success": True,
                    "message": "OAuth URL generated successfully",
                    "data": {
                        "oauth_url": oauth_url,
                        "broker_name": broker_name,
                        "credentials": {
                            "client_code": setup_data.get('broker_client_id'),
                            "password": setup_data.get('trading_pin'),
                            "dob": setup_data.get('totp_secret')
                        }
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"OAuth URL not supported for broker: {broker_name}"
                }

        except Exception as e:
            logger.error(f"❌ Error getting OAuth URL: {e}")
            return {
                "success": False,
                "message": f"Failed to get OAuth URL: {str(e)}"
            }

    # REMOVED: connect_broker_manual() - Duplicate method #2, use run_complete_manual_setup() instead

    def connect_broker_manual_otp(self, setup_id: int, manual_otp: str) -> Dict[str, Any]:
        """Manual broker connection with OTP only (uses saved credentials)"""
        try:
            logger.info(f"🔗 Manual OTP connect for setup: {setup_id}")

            # Update status to connecting
            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')

            # Get saved setup data (includes all credentials)
            setup_data = self.broker_automation.get_setup_data_with_user(setup_id)
            if not setup_data:
                return {
                    "success": False,
                    "message": "Setup not found",
                    "setup_status": "running",
                    "connection_status": "error"
                }

            # Validate that we have saved credentials
            required_fields = ['broker_client_id', 'broker_api_key', 'broker_api_secret', 'trading_pin']
            missing_fields = [field for field in required_fields if not setup_data.get(field)]

            if missing_fields:
                return {
                    "success": False,
                    "message": f"Missing saved credentials: {', '.join(missing_fields)}. Please update broker setup.",
                    "setup_status": "running",
                    "connection_status": "error"
                }

            # Use manual OTP with saved credentials
            result = self.broker_automation.connect_with_manual_otp(setup_id, manual_otp)

            if result.get('status') == 'success':
                logger.info(f"✅ Manual OTP connection successful for setup: {setup_id}")

                # Update status to connected
                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')

                return {
                    "success": True,
                    "message": result.get('message', 'Broker connected with manual OTP'),
                    "data": result
                }
            else:
                logger.error(f"❌ Manual OTP connection failed for setup: {setup_id}")

                # Update status to error
                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')

                return {
                    "success": False,
                    "message": result.get('message', 'Manual OTP connection failed'),
                    "setup_status": "running",
                    "connection_status": "not_connected"
                }

        except Exception as e:
            logger.error(f"❌ Error in manual OTP connection for setup {setup_id}: {e}")

            # Update status to error
            try:
                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')
            except:
                pass

            return {
                "success": False,
                "message": f"Failed to connect with manual OTP: {str(e)}",
                "setup_status": "running",
                "connection_status": "not_connected"
            }

    def run_complete_manual_setup(self, setup_id: int, manual_otp: str) -> Dict[str, Any]:
        """Run complete manual setup for new instances (register + login + broker auth)"""
        try:
            logger.info(f"🔄 Complete manual setup for setup: {setup_id}")

            # Update status to connecting
            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')

            # Run complete manual setup flow
            result = self.broker_automation.run_complete_manual_setup(setup_id, manual_otp)

            if result.get('status') == 'success':
                # Update final status
                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')

                return {
                    "success": True,
                    "message": result.get('message', 'Complete setup successful'),
                    "data": result.get('data', {})
                }
            else:
                # Update status back to not_connected on failure
                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')

                return {
                    "success": False,
                    "message": result.get('message', 'Complete setup failed')
                }

        except Exception as e:
            logger.error(f"❌ Complete manual setup error: {e}")

            # Update status back to not_connected on error
            try:
                self.broker_automation.update_setup_status(setup_id, 'running', 'not_connected')
            except:
                pass

            return {
                "success": False,
                "message": f"Failed to complete manual setup: {str(e)}"
            }

    def connect_broker_manual_openalgo_style(self, setup_id: int, manual_credentials: Dict[str, Any] = None) -> Dict[str, Any]:
        """Manual broker connection using OpenAlgo instance approach"""
        try:
            logger.info(f"🔗 Manual OpenAlgo-style broker connection for setup: {setup_id}")
            logger.info(f"📋 Manual credentials provided: {manual_credentials is not None}")

            # Update status to connecting
            self.broker_automation.update_setup_status(setup_id, 'running', 'connecting')

            # Use the new manual connection method
            result = self.broker_automation.connect_broker_manual_openalgo_style(setup_id, manual_credentials)
            logger.info(f"📡 Broker automation result: {result}")

            if result.get('status') == 'success':
                # Update status to connected
                self.broker_automation.update_setup_status(setup_id, 'running', 'connected')

                return {
                    "success": True,
                    "message": result.get('message', 'Broker connected successfully using OpenAlgo style'),
                    "data": result.get('data', {}),
                    "setup_status": "running",
                    "connection_status": "connected"
                }
            else:
                # Update status to error
                self.broker_automation.update_setup_status(setup_id, 'running', 'error')

                return {
                    "success": False,
                    "message": result.get('message', 'Failed to connect broker'),
                    "setup_status": "running",
                    "connection_status": "error"
                }

        except Exception as e:
            logger.error(f"❌ Error manual OpenAlgo-style connecting broker {setup_id}: {e}")
            # Update status to error
            self.broker_automation.update_setup_status(setup_id, 'running', 'error')

            return {
                "success": False,
                "message": f"Failed to connect broker: {str(e)}",
                "setup_status": "running",
                "connection_status": "error"
            }


