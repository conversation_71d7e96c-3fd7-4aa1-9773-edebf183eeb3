/**
 * User Details Page - Clean and Simple
 * Only essential functionality for user-details page
 */

console.log('🚀 User Details JS loaded');

class UserDetailsManager {
    constructor() {
        this.userId = null;
        this.adminId = null;
        this.userData = null;
        this.brokers = [];
        this.savedBrokers = [];
        this.supportedBrokers = [];
        this.showSavedBrokers = false;

        // Broker configuration templates for dynamic forms
        this.BROKER_CONFIGS = {
            angel: {
                name: 'Angel One',
                fields: [
                    { name: 'client_id', label: 'Client ID', type: 'text', required: true, placeholder: 'Enter Angel One Client ID' },
                    { name: 'api_key', label: 'API Key', type: 'text', required: true, placeholder: 'Enter Angel One API Key' },
                    { name: 'api_secret', label: 'API Secret', type: 'password', required: true, placeholder: 'Enter Angel One API Secret' },
                    { name: 'trading_pin', label: 'Trading PIN', type: 'password', required: true, placeholder: 'Enter 4-digit Trading PIN' },
                    { name: 'totp_secret', label: 'TOTP Secret', type: 'password', required: true, placeholder: 'Enter TOTP Secret Key' }
                ],
                info: 'Angel One requires Client ID, API Key, API Secret, Trading PIN, and TOTP Secret for automated trading.',
                color: 'blue'
            },
            dhan: {
                name: 'Dhan',
                fields: [
                    { name: 'api_key', label: 'API Key (Client ID)', type: 'text', required: true, placeholder: 'Enter Dhan API Key' },
                    { name: 'api_secret', label: 'API Secret (Access Token)', type: 'password', required: true, placeholder: 'Enter Dhan API Secret/JWT Token' }
                ],
                info: 'Dhan requires only API Key and API Secret. No OTP or PIN needed for automated trading.',
                color: 'teal'
            },
            flattrade: {
                name: 'Flattrade',
                fields: [
                    { name: 'client_id', label: 'Client Code', type: 'text', required: true, placeholder: 'Enter Flattrade Client Code (e.g., FT045353)' },
                    { name: 'api_key', label: 'API Key', type: 'text', required: true, placeholder: 'Enter Flattrade API Key (Client:::Key format)' },
                    { name: 'api_secret', label: 'API Secret', type: 'password', required: true, placeholder: 'Enter Flattrade API Secret' },
                    { name: 'trading_pin', label: 'Password', type: 'password', required: true, placeholder: 'Enter Flattrade Password' },
                    { name: 'totp_secret', label: 'Date of Birth', type: 'text', required: true, placeholder: 'Enter DOB (DDMMYYYY format, e.g., 27051982)' }
                ],
                info: 'Flattrade uses OAuth authentication. Password and DOB are used for OAuth login, not TOTP.',
                color: 'purple',
                authType: 'oauth'
            }
        };
    }

    /**
     * Initialize the page
     */
    async init() {
        console.log('🎯 Initializing user details...');

        // CLEAR ALL CACHED DATA FIRST
        this.userData = null;
        this.brokers = [];

        // Get URL parameters (check both 'id' and 'userId' for compatibility)
        const urlParams = new URLSearchParams(window.location.search);
        const newUserId = urlParams.get('userId') || urlParams.get('id');
        const newAdminId = urlParams.get('admin');
        const brokerId = urlParams.get('broker'); // Check if specific broker dashboard should be opened

        console.log('📋 NEW User ID:', newUserId);
        console.log('📋 NEW Admin ID:', newAdminId);
        console.log('📋 OLD User ID:', this.userId);
        if (brokerId) {
            console.log('📊 Auto-open broker dashboard for ID:', brokerId);
        }

        // Update IDs
        this.userId = newUserId;
        this.adminId = newAdminId;
        this.autoBrokerId = brokerId; // Store for later use

        if (!this.userId) {
            console.error('❌ No user ID provided');
            return;
        }

        // CLEAR DISPLAY FIRST to prevent showing old data
        this.clearDisplay();

        // Show loading state
        this.showLoadingState();

        // Load fresh data
        await this.loadUserData();
        await this.loadBrokerData();
        await this.loadSavedBrokers();
        await this.loadSupportedBrokers();

        // Hide loading spinners
        this.hideLoadingSpinners();

        // Auto-open broker dashboard if broker ID is provided
        if (this.autoBrokerId) {
            setTimeout(() => {
                const broker = this.brokers.find(b => (b.id || b.setup_id) == this.autoBrokerId);
                if (broker) {
                    console.log('🎯 Auto-opening dashboard for broker:', broker.broker_name);
                    this.viewBrokerDashboard(this.autoBrokerId);
                } else {
                    console.warn('⚠️ Broker not found for auto-open:', this.autoBrokerId);
                }
            }, 1000); // Wait for data to load and render
        }

        console.log('✅ User details initialized for user:', this.userId);
    }

    /**
     * Load user data
     */
    async loadUserData() {
        try {
            console.log('📡 Loading user data with authentication...');

            // Get authentication token
            const token = localStorage.getItem('authToken');
            if (!token) {
                console.error('❌ No authentication token found');
                window.location.href = '/superadmin/login/';
                return;
            }

            // Get auth token from localStorage or session
            const authToken = localStorage.getItem('authToken') || sessionStorage.getItem('authToken') || 'superadmin_token_12345';

            const response = await fetch(`/api/users/${this.userId}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Auth-Token': authToken
                }
            });
            const result = await response.json();

            if (response.ok && result.success && result.data) {
                this.userData = result.data;
                this.updateUserInfo();
                console.log('✅ User data loaded with proper authorization');
            } else if (response.status === 403) {
                console.error('❌ Access denied - insufficient permissions');
                this.showAccessDenied();
            } else if (response.status === 401) {
                console.error('❌ Authentication failed');
                window.location.href = '/superadmin/login/';
            } else {
                console.warn('⚠️ User API failed, using defaults');
                this.setDefaultUserInfo();
            }
        } catch (error) {
            console.error('❌ Error loading user data:', error);
            this.setDefaultUserInfo();
        }
    }

    /**
     * Load broker data (setup brokers from user_brokers table)
     */
    async loadBrokerData() {
        try {
            console.log('📡 Loading setup broker data for user:', this.userId);

            // Get authentication token
            const token = localStorage.getItem('authToken');
            if (!token) {
                console.error('❌ No authentication token found for broker data');
                return;
            }

            // Load setup brokers (actual running instances) with authentication
            let response = await fetch(`/api/broker-details?user_id=${this.userId}&admin_id=${this.adminId}`, {
                headers: {
                    'X-Auth-Token': token,
                    'Content-Type': 'application/json'
                }
            });
            let result = await response.json();

            // If no data with current admin_id, try Admin_1 (fallback for existing data)
            if (response.ok && result.success && result.data.length === 0) {
                console.log('🔄 No setup brokers with current admin, trying Admin_1...');
                response = await fetch(`/api/broker-details?user_id=${this.userId}&admin_id=Admin_1`);
                result = await response.json();
            }

            if (response.ok && result.success) {
                this.brokers = result.data || [];
                this.updateBrokerTable();
                console.log('✅ Setup broker data loaded:', this.brokers.length, 'setup brokers');
            } else {
                console.warn('⚠️ Setup broker API failed');
                this.showNoBrokers();
            }
        } catch (error) {
            console.error('❌ Error loading setup broker data:', error);
            this.showNoBrokers();
        }
    }

    /**
     * Load supported brokers from API
     */
    async loadSupportedBrokers() {
        try {
            console.log('📡 Loading supported brokers...');

            const response = await fetch('/api/brokers/supported');
            const result = await response.json();

            if (response.ok && result.success) {
                this.supportedBrokers = result.data || [];
                console.log('✅ Supported brokers loaded:', this.supportedBrokers.length, 'brokers');
            } else {
                console.warn('⚠️ Failed to load supported brokers');
                this.supportedBrokers = [];
            }
        } catch (error) {
            console.error('❌ Error loading supported brokers:', error);
            this.supportedBrokers = [];
        }
    }

    /**
     * Load saved broker configurations (from saved_broker_details table)
     */
    async loadSavedBrokers() {
        try {
            console.log('📡 Loading saved broker configurations for user:', this.userId);

            const token = localStorage.getItem('authToken');
            if (!token) {
                console.error('❌ No authentication token found for saved brokers');
                return;
            }

            const response = await fetch(`/api/saved-brokers?user_id=${this.userId}&admin_id=${this.adminId}`, {
                headers: {
                    'X-Auth-Token': token,
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();

            if (response.ok && result.success) {
                this.savedBrokers = result.data || [];
                this.updateSavedBrokersSection();
                console.log('✅ Saved broker configurations loaded:', this.savedBrokers.length, 'saved brokers');
            } else {
                console.warn('⚠️ Saved brokers API failed:', result.message);
                this.savedBrokers = [];
            }
        } catch (error) {
            console.error('❌ Error loading saved broker configurations:', error);
            this.savedBrokers = [];
        }
    }

    /**
     * Update saved brokers section in the UI
     */
    updateSavedBrokersSection() {
        // Update the statistics section to show saved brokers count
        const savedBrokersElement = document.querySelector('[data-stat="saved-brokers"]');
        if (savedBrokersElement) {
            savedBrokersElement.textContent = this.savedBrokers.length;
        }

        // Always update the broker table to include saved brokers
        this.updateBrokerTable();
    }



    /**
     * Get appropriate actions for each broker type and status
     */
    getBrokerActions(broker) {
        const actions = [];

        if (broker.type === 'setup') {
            if (broker.status_category === 'running') {
                // Running setup broker - can view, stop, disconnect
                actions.push(`
                    <button onclick="userDetailsManager.viewBrokerDashboard(${broker.id})"
                            class="px-2 py-1 text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-400 rounded"
                            title="View broker dashboard">
                        <i class="fas fa-chart-line"></i>
                    </button>
                `);
                actions.push(`
                    <button onclick="userDetailsManager.stopBroker(${broker.id})"
                            class="px-2 py-1 text-xs bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400 rounded"
                            title="Stop broker">
                        <i class="fas fa-stop"></i>
                    </button>
                `);
                actions.push(`
                    <button onclick="userDetailsManager.disconnectBroker(${broker.id})"
                            class="px-2 py-1 text-xs bg-orange-100 text-orange-700 hover:bg-orange-200 dark:bg-orange-900/20 dark:text-orange-400 rounded"
                            title="Disconnect broker">
                        <i class="fas fa-unlink"></i>
                    </button>
                `);
            } else {
                // Stopped setup broker - can edit, start, delete
                actions.push(`
                    <button onclick="userDetailsManager.viewBrokerDashboard(${broker.id})"
                            class="px-2 py-1 text-xs bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 rounded"
                            title="View broker dashboard">
                        <i class="fas fa-chart-line"></i>
                    </button>
                `);
                actions.push(`
                    <button onclick="userDetailsManager.startBroker(${broker.id})"
                            class="px-2 py-1 text-xs bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 rounded"
                            title="Start broker">
                        <i class="fas fa-play"></i>
                    </button>
                `);
                actions.push(`
                    <button onclick="userDetailsManager.deleteBroker(${broker.id})"
                            class="px-2 py-1 text-xs bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400 rounded"
                            title="Delete broker">
                        <i class="fas fa-trash"></i>
                    </button>
                `);
            }
        } else if (broker.status_category === 'used') {
            // Used saved broker - limited actions
            actions.push(`
                <button onclick="userDetailsManager.viewSavedBroker(${broker.id})"
                        class="px-2 py-1 text-xs bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-900/20 dark:text-gray-400 rounded"
                        title="View details">
                    <i class="fas fa-eye"></i>
                </button>
            `);
            actions.push(`
                <button onclick="userDetailsManager.copyBroker(${broker.id})"
                        class="px-2 py-1 text-xs bg-purple-100 text-purple-700 hover:bg-purple-200 dark:bg-purple-900/20 dark:text-purple-400 rounded"
                        title="Copy configuration">
                    <i class="fas fa-copy"></i>
                </button>
            `);
        } else {
            // Available saved broker or template - full actions
            actions.push(`
                <button onclick="userDetailsManager.editSavedBroker(${broker.id})"
                        class="px-2 py-1 text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-400 rounded"
                        title="Edit configuration">
                    <i class="fas fa-edit"></i>
                </button>
            `);
            actions.push(`
                <button onclick="userDetailsManager.copyBroker(${broker.id})"
                        class="px-2 py-1 text-xs bg-purple-100 text-purple-700 hover:bg-purple-200 dark:bg-purple-900/20 dark:text-purple-400 rounded"
                        title="Copy configuration">
                    <i class="fas fa-copy"></i>
                </button>
            `);
            actions.push(`
                <button onclick="userDetailsManager.setupFromSaved(${broker.id})"
                        class="px-2 py-1 text-xs bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 rounded"
                        title="Create setup from this configuration">
                    <i class="fas fa-play"></i>
                </button>
            `);
            actions.push(`
                <button onclick="userDetailsManager.deleteSavedBroker(${broker.id})"
                        class="px-2 py-1 text-xs bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400 rounded"
                        title="Delete configuration">
                    <i class="fas fa-trash"></i>
                </button>
            `);
        }

        return actions.join('');
    }

    /**
     * Update user information on page
     */
    updateUserInfo() {
        console.log('🔄 Updating user info with data:', this.userData);

        // Update main user display
        const userName = document.getElementById('userName');
        const userId = document.getElementById('userId');
        const userSubtitle = document.getElementById('userSubtitle');

        if (userName) {
            userName.textContent = this.userData.full_name || `User ${this.userData.username}`;
            console.log('✅ Updated userName to:', userName.textContent);
        }

        if (userId) {
            userId.textContent = this.userData.email || `user${this.userData.username}@algofactory.in`;
            console.log('✅ Updated userId to:', userId.textContent);
        }

        if (userSubtitle) {
            userSubtitle.textContent = `Admin: ${this.userData.admin_id || 'No Admin'} | ID: ${this.userData.user_number || 'N/A'}`;
            console.log('✅ Updated userSubtitle to:', userSubtitle.textContent);
        }

        // Update breadcrumb navigation
        const userIdBreadcrumb = document.getElementById('userIdBreadcrumb');
        const adminBreadcrumb = document.getElementById('adminBreadcrumb');

        if (userIdBreadcrumb) {
            userIdBreadcrumb.textContent = this.userData.username;
            console.log('✅ Updated breadcrumb user ID to:', this.userData.username);
        }

        if (adminBreadcrumb) {
            adminBreadcrumb.textContent = this.userData.admin_id || 'No Admin';
            adminBreadcrumb.href = `/superadmin/admin-details/?id=${this.userData.admin_id}`;
            console.log('✅ Updated breadcrumb admin to:', this.userData.admin_id, 'with link:', adminBreadcrumb.href);
        }

        // Update admin badge
        const userAdmin = document.getElementById('userAdmin');
        if (userAdmin) {
            userAdmin.textContent = this.userData.admin_id || 'No Admin';
            console.log('✅ Updated admin badge to:', this.userData.admin_id);
        }

        // Update contact information
        const userEmail = document.getElementById('userEmail');
        if (userEmail) {
            userEmail.textContent = this.userData.email || `user${this.userData.username}@algofactory.in`;
            console.log('✅ Updated contact email to:', userEmail.textContent);
        }

        // Update page title
        document.title = `${this.userData.full_name || this.userData.username} - User Details - AlgoFactory`;

        // Update stats if available
        this.updateUserStats();
    }

    updateUserStats() {
        const totalBrokers = document.getElementById('totalBrokers');
        const totalFunds = document.getElementById('totalFunds');
        const totalPnl = document.getElementById('totalPnl');
        const totalPositions = document.getElementById('totalPositions');

        if (totalBrokers) totalBrokers.textContent = this.userData.total_brokers || 0;
        if (totalFunds) totalFunds.textContent = `₹${(this.userData.total_funds || 0).toLocaleString()}`;
        if (totalPnl) {
            const pnl = this.userData.total_pnl || 0;
            totalPnl.textContent = `₹${pnl.toLocaleString()}`;
            totalPnl.className = pnl >= 0 ? 'text-2xl font-bold text-green-600 dark:text-green-400' : 'text-2xl font-bold text-red-600 dark:text-red-400';
        }
        if (totalPositions) totalPositions.textContent = this.userData.total_positions || 0;

        // 🚀 ADD REAL DATA INDICATOR
        this.addRealDataIndicator();
    }

    addRealDataIndicator() {
        // Remove any existing indicators
        const existingIndicator = document.getElementById('realDataIndicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Create real data indicator
        const isRealData = this.userData.real_data === true;
        const dataSource = this.userData.data_source || 'unknown';

        const indicator = document.createElement('div');
        indicator.id = 'realDataIndicator';
        indicator.className = `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
            isRealData
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-yellow-100 text-yellow-800 border border-yellow-200'
        }`;

        indicator.innerHTML = `
            <span class="w-2 h-2 rounded-full mr-2 ${isRealData ? 'bg-green-500' : 'bg-yellow-500'}"></span>
            ${isRealData ? '🚀 REAL DATA' : '⚠️ FAKE DATA'}
            <span class="ml-2 text-xs opacity-75">(${dataSource})</span>
        `;

        // Add to the stats section
        const statsContainer = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-4.gap-6');
        if (statsContainer) {
            // Create a container for the indicator
            const indicatorContainer = document.createElement('div');
            indicatorContainer.className = 'col-span-full flex justify-center mb-4';
            indicatorContainer.appendChild(indicator);

            // Insert at the beginning of stats container
            statsContainer.insertBefore(indicatorContainer, statsContainer.firstChild);
        }

        // Also add to console for debugging
        console.log(`📊 Data Source: ${dataSource} | Real Data: ${isRealData}`);
        if (isRealData) {
            console.log('✅ Displaying REAL broker data from Angel One API');
        } else {
            console.log('⚠️ Displaying FAKE/FALLBACK data');
        }
    }

    /**
     * Set default user info if API fails
     */
    setDefaultUserInfo() {
        const userName = document.getElementById('userName');
        const userId = document.getElementById('userId');

        if (userName) userName.textContent = `User ${this.userId}`;
        if (userId) userId.textContent = `user${this.userId}@algofactory.in`;
    }

    clearDisplay() {
        console.log('🧹 Clearing display for fresh data...');

        // Clear user info
        const userName = document.getElementById('userName');
        const userId = document.getElementById('userId');
        const userSubtitle = document.getElementById('userSubtitle');

        if (userName) userName.textContent = 'Loading...';
        if (userId) userId.textContent = 'Loading user data...';
        if (userSubtitle) userSubtitle.textContent = 'Please wait...';

        // Clear breadcrumb
        const userIdBreadcrumb = document.getElementById('userIdBreadcrumb');
        const adminBreadcrumb = document.getElementById('adminBreadcrumb');

        if (userIdBreadcrumb) userIdBreadcrumb.textContent = 'Loading...';
        if (adminBreadcrumb) adminBreadcrumb.textContent = 'Loading...';

        // Clear admin badge
        const userAdmin = document.getElementById('userAdmin');
        if (userAdmin) userAdmin.textContent = 'Loading...';

        // Clear contact info
        const userEmail = document.getElementById('userEmail');
        if (userEmail) userEmail.textContent = 'Loading...';

        // Clear stats
        const totalBrokers = document.getElementById('totalBrokers');
        const totalFunds = document.getElementById('totalFunds');
        const totalPnl = document.getElementById('totalPnl');
        const totalPositions = document.getElementById('totalPositions');

        if (totalBrokers) totalBrokers.textContent = '0';
        if (totalFunds) totalFunds.textContent = '₹0';
        if (totalPnl) totalPnl.textContent = '₹0';
        if (totalPositions) totalPositions.textContent = '0';

        // Clear broker table
        const brokerTableBody = document.getElementById('brokerTableBody');
        if (brokerTableBody) {
            brokerTableBody.innerHTML = '<tr><td colspan="6" class="text-center py-4">Loading brokers...</td></tr>';
        }

        // Remove any access denied messages
        const accessDeniedDivs = document.querySelectorAll('.bg-red-50');
        accessDeniedDivs.forEach(div => div.remove());
    }

    showLoadingState() {
        console.log('⏳ Showing loading state...');

        // Show loading spinners if they exist
        const loadingElements = document.querySelectorAll('.fa-spinner');
        loadingElements.forEach(element => {
            element.style.display = 'inline-block';
        });
    }

    showAccessDenied() {
        const userName = document.getElementById('userName');
        const userId = document.getElementById('userId');
        const userSubtitle = document.getElementById('userSubtitle');

        if (userName) userName.textContent = 'Access Denied';
        if (userId) userId.textContent = 'You do not have permission to view this user';
        if (userSubtitle) userSubtitle.textContent = 'Insufficient permissions to access this user data';

        // Hide broker sections
        const brokerSections = document.querySelectorAll('.broker-section, .stats-card');
        brokerSections.forEach(section => section.style.display = 'none');

        // Show access denied message
        const mainContent = document.querySelector('.page-content');
        if (mainContent) {
            const accessDeniedDiv = document.createElement('div');
            accessDeniedDiv.className = 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center';
            accessDeniedDiv.innerHTML = `
                <i class="fas fa-lock text-red-500 text-4xl mb-4"></i>
                <h3 class="text-lg font-semibold text-red-800 dark:text-red-400 mb-2">Access Denied</h3>
                <p class="text-red-600 dark:text-red-300">You do not have permission to view this user's details.</p>
                <button onclick="history.back()" class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                    Go Back
                </button>
            `;
            mainContent.appendChild(accessDeniedDiv);
        }
    }

    /**
     * Hide all loading spinners
     */
    hideLoadingSpinners() {
        // Hide loading spinners and replace with data
        const loadingElements = document.querySelectorAll('[class*="loading"], .fa-spinner');
        loadingElements.forEach(element => {
            if (element.textContent === 'Loading...') {
                element.textContent = 'N/A';
            }
            element.classList.remove('fa-spin');
            element.style.display = 'none';
        });

        // Update specific elements with real data
        this.updateContactInfo();
        this.updateStatistics();
    }

    /**
     * Update contact information
     */
    updateContactInfo() {
        const email = this.userData?.email || `user${this.userId}@algofactory.in`;
        const phone = this.userData?.phone || 'N/A';
        const lastLogin = this.userData?.last_login ? `${new Date(this.userData.last_login).toLocaleDateString()} ${new Date(this.userData.last_login).toLocaleTimeString()}` : 'Never';

        // Find and update contact elements
        const contactElements = document.querySelectorAll('[id*="contact"], [class*="contact"]');
        contactElements.forEach(element => {
            if (element.textContent === 'Loading...') {
                if (element.id.includes('email') || element.textContent.includes('@')) {
                    element.textContent = email;
                } else if (element.id.includes('phone')) {
                    element.textContent = phone;
                } else if (element.id.includes('login')) {
                    element.textContent = lastLogin;
                }
            }
        });
    }

    /**
     * Update statistics with REAL data from user API
     */
    updateStatistics() {
        // Use REAL data from user API response, not local broker array
        const totalBrokers = this.userData?.total_brokers || 0;
        const activeBrokers = this.userData?.connected_brokers || 0;
        const totalFunds = this.userData?.total_funds || 0;
        const totalPnl = this.userData?.total_pnl || 0;

        // Update statistics elements by data attribute (more reliable)
        const totalBrokersElement = document.querySelector('[data-stat="total-brokers"]');
        if (totalBrokersElement) {
            totalBrokersElement.textContent = totalBrokers;
        }

        const activeBrokersElement = document.querySelector('[data-stat="active-brokers"]');
        if (activeBrokersElement) {
            activeBrokersElement.textContent = activeBrokers;
        }

        // Update financial statistics if elements exist
        const totalFundsElement = document.querySelector('[data-stat="total-funds"]');
        if (totalFundsElement) {
            totalFundsElement.textContent = `₹${totalFunds.toLocaleString()}`;
        }

        const totalPnlElement = document.querySelector('[data-stat="total-pnl"]');
        if (totalPnlElement) {
            const pnlClass = totalPnl >= 0 ? 'text-green-600' : 'text-red-600';
            totalPnlElement.textContent = `₹${totalPnl.toLocaleString()}`;
            totalPnlElement.className = totalPnlElement.className.replace(/text-(green|red)-600/g, '') + ` ${pnlClass}`;
        }

        console.log('✅ Statistics updated:', { totalBrokers, activeBrokers, totalFunds, totalPnl });
    }

    /**
     * Update broker table with ONLY setup brokers (running instances)
     * Saved brokers are managed separately through Add/Edit/Setup buttons
     */
    updateBrokerTable() {
        const tableBody = document.getElementById('brokersTableBody');
        if (!tableBody) {
            console.warn('⚠️ Broker table body not found');
            return;
        }

        // Show ONLY setup brokers (running instances) in the table
        const brokersToShow = [...this.brokers];

        if (brokersToShow.length === 0) {
            this.showNoBrokers();
            return;
        }

        const rows = brokersToShow.map(broker => {
            console.log('🔍 Rendering setup broker:', broker);

            // Handle different data structures from API
            const brokerName = broker.broker_name || 'Unknown';
            const clientId = broker.broker_client_id || broker.client_id || 'N/A';
            const setupName = broker.setup_name || broker.trading_setup_name || 'N/A';
            // Calculate status - avoid intermediate states during progress
            let status;
            if (broker.progress_operation) {
                status = broker.progress_operation; // Show 'starting' or 'connecting' during progress
            } else {
                status = broker.status || `${broker.setup_status}_${broker.connection_status}` || 'Unknown';
            }
            const instanceName = broker.instance_name || 'N/A';
            const createdAt = broker.created_at ? new Date(broker.created_at).toLocaleDateString() : 'N/A';

            // Use REAL financial data from API (no fake data generation)
            const funds = broker.funds || 0;
            const pnl = broker.pnl || 0;
            const positions = broker.positions || 0;
            const brokerType = 'Setup Instance'; // Only setup brokers in table

            return `
                <tr class="hover:bg-gray-50 dark:hover:bg-slate-700">
                    <td class="px-3 py-3 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-chart-line text-blue-600 dark:text-blue-400 text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900 dark:text-slate-100">${brokerName}</div>
                                <div class="text-xs text-gray-500 dark:text-slate-400">${setupName}</div>
                                <div class="text-xs text-gray-400 dark:text-slate-500">${instanceName}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-3 py-3 whitespace-nowrap text-center">
                        ${broker.progress_operation ?
                            this.getProgressDisplay(broker) :
                            `<span class="px-2 py-1 text-xs rounded-full ${this.getStatusColor(status)}">
                                ${this.getStatusDisplay(status)}
                            </span>`
                        }
                    </td>
                    <td class="px-3 py-3 whitespace-nowrap text-right text-sm">
                        <span class="font-medium text-gray-900 dark:text-slate-100">₹${funds.toLocaleString()}</span>
                    </td>
                    <td class="px-3 py-3 whitespace-nowrap text-right text-sm">
                        <span class="font-medium ${pnl >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">
                            ₹${pnl.toLocaleString()}
                        </span>
                    </td>
                    <td class="px-3 py-3 whitespace-nowrap text-center text-sm">
                        <span class="font-medium text-gray-900 dark:text-slate-100">${positions}</span>
                    </td>
                    <td class="px-3 py-3 whitespace-nowrap text-center">
                        <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            ${brokerType}
                        </span>
                    </td>
                    <td class="px-3 py-3 whitespace-nowrap text-center text-sm text-gray-500 dark:text-slate-400">
                        ${createdAt}
                    </td>
                    <td class="px-3 py-3 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-1">
                            ${this.getBrokerActionButtons(broker)}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        tableBody.innerHTML = rows;
        console.log('✅ Comprehensive broker table updated with', brokersToShow.length, 'brokers');
    }

    /**
     * Show no brokers message
     */
    showNoBrokers() {
        const tableBody = document.getElementById('brokersTableBody');
        if (!tableBody) return;

        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="px-6 py-8 text-center text-gray-500 dark:text-slate-400">
                    <i class="fas fa-chart-line text-3xl mb-3"></i>
                    <p>No broker connections found</p>
                    <button onclick="userDetailsManager.addBroker()"
                            class="mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm">
                        <i class="fas fa-plus mr-2"></i>Add First Broker
                    </button>
                </td>
            </tr>
        `;
    }

    /**
     * Get enhanced status display with progress indicators
     */
    getStatusDisplay(status) {
        switch (status) {
            // ✅ Connected states
            case 'running_connected':
                return '🟢 Connected';

            // 🟡 Running but not connected
            case 'running_not_connected':
                return '🟡 Running';
            case 'running_disconnected':
                return '🟡 Logged Out';

            // 🔵 Setup/Configuration states
            case 'env_configured':
                return '🔵 Configured';
            case 'configured':
                return '🔵 Ready';

            // 🟣 Loading states with animation
            case 'starting':
                return '🟣 Starting...';
            case 'connecting':
                return '🟣 Connecting...';
            case 'loading':
                return '🟣 Loading...';

            // 🔴 Error states
            case 'running_register_failed':
                return '🔴 Auth Failed';
            case 'failed':
                return '🔴 Failed';
            case 'error':
                return '🔴 Error';

            // ⚫ Stopped states
            case 'stopped':
                return '⚫ Stopped';
            case 'inactive':
                return '⚫ Inactive';

            // Default
            default:
                return status.replace('_', ' ').toUpperCase();
        }
    }

    /**
     * Show progress indicator for long operations
     */
    showProgressIndicator(brokerId, operation, steps) {
        const broker = this.brokers.find(b => b.id === brokerId || b.setup_id === brokerId);
        if (!broker) return;

        // Set progress state
        broker.progress_operation = operation;
        broker.progress_steps = steps;
        broker.progress_current = 0;
        broker.progress_message = steps[0];

        this.updateBrokerTable();
    }

    /**
     * Update progress indicator
     */
    updateProgress(brokerId, stepIndex, message) {
        const broker = this.brokers.find(b => b.id === brokerId || b.setup_id === brokerId);
        if (!broker || !broker.progress_operation) return;

        broker.progress_current = stepIndex;
        broker.progress_message = message || broker.progress_steps[stepIndex];

        this.updateBrokerTable();
    }

    /**
     * Clear progress indicator
     */
    clearProgress(brokerId) {
        const broker = this.brokers.find(b => b.id === brokerId || b.setup_id === brokerId);
        if (!broker) return;

        delete broker.progress_operation;
        delete broker.progress_steps;
        delete broker.progress_current;
        delete broker.progress_message;

        this.updateBrokerTable();
    }

    /**
     * Get progress display for status column
     */
    getProgressDisplay(broker) {
        if (!broker.progress_operation) return null;

        const current = broker.progress_current || 0;
        const total = broker.progress_steps ? broker.progress_steps.length : 4;
        const percentage = Math.round((current / total) * 100);
        const message = broker.progress_message || 'Processing...';

        return `
            <div class="flex flex-col items-center space-y-1">
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
                    <span class="text-xs font-medium">${message}</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                    <div class="bg-purple-600 h-1.5 rounded-full transition-all duration-300" style="width: ${percentage}%"></div>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">${current}/${total} (${percentage}%)</span>
            </div>
        `;
    }

    /**
     * Open comprehensive Broker Manager - handles all broker operations
     */
    async openBrokerManager() {
        try {
            console.log('🔧 Opening comprehensive Broker Manager...');

            // Load fresh data
            await this.loadSavedBrokers();

            const savedBrokersList = this.savedBrokers && this.savedBrokers.length > 0
                ? this.savedBrokers.map(broker => {
                    const brokerName = broker.broker_name || 'Unknown';
                    const setupName = broker.trading_setup_name || 'Unnamed';
                    const clientId = broker.client_id || 'N/A';
                    const createdAt = broker.created_at ? new Date(broker.created_at).toLocaleDateString() : 'N/A';
                    const isUsed = broker.used_by_setup ? true : false;

                    return `
                        <div class="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center gap-2">
                                        <h4 class="font-medium text-gray-900 dark:text-slate-100">${brokerName}</h4>
                                        ${isUsed ? '<span class="px-2 py-1 text-xs bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400 rounded">In Use</span>' : ''}
                                    </div>
                                    <p class="text-sm text-gray-600 dark:text-slate-400">${setupName}</p>
                                    <p class="text-xs text-gray-500 dark:text-slate-500">Client ID: ${clientId}</p>
                                    <p class="text-xs text-gray-500 dark:text-slate-500">Created: ${createdAt}</p>
                                    ${isUsed ? `<p class="text-xs text-orange-600 dark:text-orange-400">Used by: ${broker.used_by_setup}</p>` : ''}
                                </div>
                                <div class="flex gap-1 flex-wrap">
                                    <button onclick="userDetailsManager.editSavedBrokerConfig(${broker.id})"
                                            class="px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded text-xs">
                                        <i class="fas fa-edit mr-1"></i>Edit
                                    </button>
                                    ${!isUsed ? `
                                        <button onclick="userDetailsManager.setupFromSaved(${broker.id})"
                                                class="px-2 py-1 bg-green-100 hover:bg-green-200 text-green-700 rounded text-xs">
                                            <i class="fas fa-play mr-1"></i>Setup
                                        </button>
                                        <button onclick="userDetailsManager.deleteSavedBroker(${broker.id})"
                                                class="px-2 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded text-xs">
                                            <i class="fas fa-trash mr-1"></i>Delete
                                        </button>
                                    ` : `
                                        <button onclick="userDetailsManager.viewUsedBroker(${broker.id})"
                                                class="px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded text-xs">
                                            <i class="fas fa-eye mr-1"></i>View
                                        </button>
                                    `}
                                </div>
                            </div>
                        </div>
                    `;
                }).join('')
                : '<div class="text-center py-8 text-gray-500 dark:text-slate-400"><i class="fas fa-database text-3xl mb-3"></i><p>No saved broker configurations found</p></div>';

            const modalContent = `
                <div class="space-y-6">
                    <!-- Header -->
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-slate-100">Broker Manager</h3>
                            <p class="text-sm text-gray-600 dark:text-slate-400">Complete broker management - Add, Edit, Setup, and Manage all brokers</p>
                        </div>
                        <button onclick="userDetailsManager.addNewBrokerConfig()"
                                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm">
                            <i class="fas fa-plus mr-2"></i>Add New Broker
                        </button>
                    </div>

                    <!-- Saved Configurations Content -->
                    <div class="space-y-3 max-h-96 overflow-y-auto">
                        ${savedBrokersList}
                    </div>

                    <!-- Footer -->
                    <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-slate-600">
                        <div class="text-sm text-gray-500 dark:text-slate-400">
                            💡 Setup brokers appear in the main table with funds & P&L data
                        </div>
                        <button onclick="userDetailsManager.closeModal()"
                                class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">
                            Close
                        </button>
                    </div>
                </div>
            `;

            this.showModal('Broker Manager', modalContent, 'max-w-4xl');
        } catch (error) {
            console.error('❌ Error opening Broker Manager:', error);
            this.showError('Failed to open Broker Manager');
        }
    }



    /**
     * Add new broker configuration
     */
    addNewBrokerConfig() {
        this.closeModal();
        this.addBroker();
    }

    /**
     * View used broker details
     */
    viewUsedBroker(brokerId) {
        const broker = this.savedBrokers.find(b => b.id === brokerId);
        if (!broker) return;

        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Broker Configuration Details</h4>
                <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-orange-500 mr-2"></i>
                        <span class="text-sm text-orange-800 dark:text-orange-300">
                            This configuration is currently being used by: <strong>${broker.used_by_setup}</strong>
                        </span>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300">Broker</label>
                        <p class="text-sm text-gray-900 dark:text-slate-100">${broker.broker_name}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300">Setup Name</label>
                        <p class="text-sm text-gray-900 dark:text-slate-100">${broker.trading_setup_name || 'N/A'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300">Client ID</label>
                        <p class="text-sm text-gray-900 dark:text-slate-100">${broker.client_id || 'N/A'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300">Created</label>
                        <p class="text-sm text-gray-900 dark:text-slate-100">${broker.created_at ? new Date(broker.created_at).toLocaleDateString() : 'N/A'}</p>
                    </div>
                </div>
                <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Close</button>
                </div>
            </div>
        `;

        this.showModal('Broker Configuration Details', modalContent);
    }

    /**
     * Setup broker from saved configuration
     */
    async setupFromSaved(savedBrokerId) {
        try {
            console.log(`🚀 Setting up broker from saved configuration ${savedBrokerId}`);
            console.log('🔍 Setup function called successfully!');

            // Find the saved broker
            const savedBroker = this.savedBrokers.find(b => b.id === savedBrokerId);
            if (!savedBroker) {
                this.showError('Saved broker configuration not found');
                return;
            }

            // Show app-level confirmation modal
            this.showSetupConfirmationModal(savedBroker);
        } catch (error) {
            console.error('❌ Error setting up broker:', error);
            this.showError('Failed to setup broker: ' + error.message);
        }
    }

    /**
     * Show setup confirmation modal
     */
    showSetupConfirmationModal(savedBroker) {
        const modalContent = `
            <div class="space-y-4">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-play text-green-600 dark:text-green-400 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-slate-100">Setup Broker Instance</h4>
                        <p class="text-sm text-gray-600 dark:text-slate-400">Create a running instance from saved configuration</p>
                    </div>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700 dark:text-slate-300">Broker:</span>
                            <span class="text-sm text-gray-900 dark:text-slate-100">${savedBroker.broker_name}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700 dark:text-slate-300">Setup Name:</span>
                            <span class="text-sm text-gray-900 dark:text-slate-100">${savedBroker.trading_setup_name || 'Unnamed'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700 dark:text-slate-300">Client ID:</span>
                            <span class="text-sm text-gray-900 dark:text-slate-100">${savedBroker.client_id || 'N/A'}</span>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-info-circle text-yellow-500 mt-0.5 text-sm"></i>
                        <div class="text-xs text-yellow-700 dark:text-yellow-300">
                            <p class="font-medium mb-1">What happens next:</p>
                            <p>• A running broker instance will be created</p>
                            <p>• It will appear in the main table with financial data</p>
                            <p>• The saved configuration will be marked as "In Use"</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.confirmSetupBroker(${savedBroker.id})" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg">
                        <i class="fas fa-play mr-2"></i>Setup Broker
                    </button>
                </div>
            </div>
        `;

        this.showModal('Setup Broker Instance', modalContent);
    }

    /**
     * Confirm and execute broker setup
     */
    async confirmSetupBroker(savedBrokerId) {
        try {
            // Find the saved broker
            const savedBroker = this.savedBrokers.find(b => b.id === savedBrokerId);
            if (!savedBroker) {
                this.showError('Saved broker configuration not found');
                return;
            }

            this.closeModal();

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Create setup broker from saved configuration using new unified API
            const setupData = {
                user_id: this.userId,
                admin_id: this.adminId,
                saved_broker_id: savedBrokerId
            };

            console.log('🚀 Calling broker-setup API with data:', setupData);

            // Add audit log before API call
            await this.logAuditAction('BROKER_SETUP_ATTEMPT', {
                user_id: this.userId,
                admin_id: this.adminId,
                saved_broker_id: savedBrokerId,
                broker_name: savedBroker.broker_name,
                setup_name: savedBroker.trading_setup_name,
                action: 'Setup broker from saved configuration',
                timestamp: new Date().toISOString()
            });

            const response = await fetch('/api/broker-setup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Auth-Token': token
                },
                body: JSON.stringify(setupData)
            });

            const result = await response.json();
            console.log('📋 Broker setup API response:', result);

            if (response.ok && result.success) {
                // Log successful setup
                await this.logAuditAction('BROKER_SETUP_SUCCESS', {
                    user_id: this.userId,
                    admin_id: this.adminId,
                    saved_broker_id: savedBrokerId,
                    broker_name: savedBroker.broker_name,
                    setup_name: savedBroker.trading_setup_name,
                    instance_name: result.data?.instance_name || 'unknown',
                    setup_id: result.data?.setup_id || 'unknown',
                    action: 'Broker setup completed successfully',
                    timestamp: new Date().toISOString()
                });

                this.showSuccess(`✅ Broker setup created successfully! Instance: ${result.data?.instance_name || 'New Instance'}`);

                // Mark saved broker as used
                await this.markSavedBrokerAsUsed(savedBrokerId, result.data?.instance_name);

                // Auto-refresh data to show new setup broker in table
                await this.loadBrokerData();
                await this.loadSavedBrokers();

                // Show notification about state change
                setTimeout(() => {
                    this.showNotification(`🚀 Broker "${savedBroker.trading_setup_name}" is now running and visible in the table with financial data!`, 'success');
                }, 1000);
            } else {
                // Log failed setup
                await this.logAuditAction('BROKER_SETUP_FAILED', {
                    user_id: this.userId,
                    admin_id: this.adminId,
                    saved_broker_id: savedBrokerId,
                    broker_name: savedBroker.broker_name,
                    setup_name: savedBroker.trading_setup_name,
                    error: result.message || 'Unknown error',
                    action: 'Broker setup failed',
                    timestamp: new Date().toISOString()
                });

                throw new Error(result.message || 'Failed to setup broker');
            }
        } catch (error) {
            console.error('❌ Error setting up broker:', error);

            // Log error in audit
            await this.logAuditAction('BROKER_SETUP_ERROR', {
                user_id: this.userId,
                admin_id: this.adminId,
                saved_broker_id: savedBrokerId,
                error: error.message,
                action: 'Broker setup encountered error',
                timestamp: new Date().toISOString()
            });

            this.showError('Failed to setup broker: ' + error.message);
        }
    }

    /**
     * Log audit action for tracking who does what and when
     */
    async logAuditAction(action_type, details) {
        try {
            const token = localStorage.getItem('authToken');
            if (!token) return; // Skip audit if no token

            const auditData = {
                action_type: action_type,
                user_id: this.userId,
                admin_id: this.adminId,
                performed_by: localStorage.getItem('username') || 'unknown',
                performed_by_role: localStorage.getItem('userRole') || 'unknown',
                details: details,
                timestamp: new Date().toISOString(),
                ip_address: 'web_interface',
                user_agent: navigator.userAgent
            };

            console.log('📝 Logging audit action:', action_type, details);

            const response = await fetch('/api/audit-log', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Auth-Token': token
                },
                body: JSON.stringify(auditData)
            });

            if (!response.ok) {
                console.warn('⚠️ Failed to log audit action:', action_type);
            }
        } catch (error) {
            console.warn('⚠️ Error logging audit action:', error);
            // Don't throw error for audit logging failures
        }
    }

    /**
     * Mark saved broker as used by a setup instance
     */
    async markSavedBrokerAsUsed(savedBrokerId, instanceName) {
        try {
            const token = localStorage.getItem('authToken');

            const response = await fetch(`/api/saved-brokers/${savedBrokerId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Auth-Token': token
                },
                body: JSON.stringify({
                    used_by_setup: instanceName
                })
            });

            if (response.ok) {
                console.log('✅ Saved broker marked as used');
            }
        } catch (error) {
            console.warn('⚠️ Could not mark saved broker as used:', error);
        }
    }

    /**
     * Refresh broker data
     */
    async refreshBrokerData() {
        console.log('🔄 Refreshing broker data...');
        this.showNotification('Refreshing broker data...', 'info');

        await this.loadBrokerData();
        await this.loadSavedBrokers();

        this.showSuccess('Broker data refreshed successfully!');
    }

    /**
     * Get status color classes with enhanced visual feedback
     */
    getStatusColor(status) {
        switch (status) {
            // ✅ Connected states (Green)
            case 'running_connected':
                return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border border-green-200 dark:border-green-800';

            // 🟡 Running but not connected (Orange/Amber)
            case 'running_not_connected':
            case 'running_disconnected':
                return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400 border border-amber-200 dark:border-amber-800';

            // 🔵 Setup/Configuration states (Blue)
            case 'env_configured':
            case 'configured':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200 dark:border-blue-800';

            // 🟣 Starting/Loading states (Purple with animation)
            case 'starting':
            case 'connecting':
            case 'loading':
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 border border-purple-200 dark:border-purple-800 animate-pulse';

            // 🔴 Error states (Red)
            case 'running_register_failed':
            case 'failed':
            case 'error':
                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200 dark:border-red-800';

            // ⚫ Stopped states (Gray)
            case 'stopped':
            case 'inactive':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 border border-gray-200 dark:border-gray-800';

            // 🟡 Default/Unknown states (Yellow)
            default:
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800';
        }
    }

    /**
     * Get broker action buttons based on status
     */
    getBrokerActionButtons(broker) {
        const setupId = broker.setup_id || broker.id;
        const setupStatus = broker.setup_status || 'unknown';
        const connectionStatus = broker.connection_status || 'unknown';

        console.log('🔘 Generating buttons for broker:', broker.broker_name,
                   'Setup:', setupStatus, 'Connection:', connectionStatus, 'ID:', setupId);

        let buttons = [];

        // Check if broker has loading state
        const loadingState = broker.loading_state;
        const loadingMessage = broker.loading_message;

        // Start/Stop button based on setup_status
        if (loadingState === 'starting' || loadingState === 'stopping') {
            // Show loading state
            buttons.push(`
                <button disabled class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs cursor-not-allowed"
                        title="${loadingMessage}">
                    <i class="fas fa-spinner fa-spin mr-1"></i>${loadingMessage}
                </button>
            `);
        } else if (setupStatus === 'env_configured' || setupStatus === 'not_setup' || setupStatus === 'stopped') {
            // Show Start button for brokers that are configured but not running
            buttons.push(`
                <button onclick="userDetailsManager.startBroker(${setupId})"
                        class="px-2 py-1 bg-green-100 hover:bg-green-200 text-green-700 rounded text-xs"
                        title="Start Broker Service">
                    <i class="fas fa-play mr-1"></i>Start
                </button>
            `);
        } else {
            // Show Stop button for any other status (running, started, ready, etc.)
            buttons.push(`
                <button onclick="userDetailsManager.stopBroker(${setupId})"
                        class="px-2 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded text-xs"
                        title="Stop Broker Service">
                    <i class="fas fa-stop mr-1"></i>Stop
                </button>
            `);
        }

        // Connect/Disconnect button based on connection_status
        if (loadingState === 'connecting' || loadingState === 'disconnecting') {
            // Show loading state for connecting/disconnecting
            buttons.push(`
                <button disabled class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs cursor-not-allowed"
                        title="${loadingMessage}">
                    <i class="fas fa-spinner fa-spin mr-1"></i>${loadingMessage}
                </button>
            `);
        } else if (connectionStatus === 'connected') {
            // Show View and Disconnect buttons for connected brokers
            buttons.push(`
                <button onclick="userDetailsManager.viewBrokerDashboard(${setupId})"
                        class="px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded text-xs"
                        title="View Broker Dashboard">
                    <i class="fas fa-chart-line mr-1"></i>View
                </button>
            `);
            buttons.push(`
                <button onclick="userDetailsManager.disconnectBroker(${setupId})"
                        class="px-2 py-1 bg-orange-100 hover:bg-orange-200 text-orange-700 rounded text-xs"
                        title="Disconnect from Broker">
                    <i class="fas fa-unlink mr-1"></i>Disconnect
                </button>
            `);
        } else if (connectionStatus === 'not_connected' || connectionStatus === 'disconnected' || connectionStatus === 'register_failed') {
            // Show Connect button for any broker that's not connected
            // Allow connect if broker has been started (not just 'running' status)
            if (setupStatus === 'running' || setupStatus === 'env_configured' || setupStatus === 'started' || setupStatus === 'ready') {
                buttons.push(`
                    <button onclick="userDetailsManager.showConnectOptions(${setupId})"
                            class="px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded text-xs"
                            title="Connect to Broker">
                        <i class="fas fa-link mr-1"></i>Connect
                    </button>
                `);
            }
        }

        // Edit button (for individual broker instances)
        buttons.push(`
            <button onclick="userDetailsManager.editBrokerEnv(${setupId})"
                    class="px-2 py-1 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded text-xs"
                    title="Edit Broker Configuration">
                <i class="fas fa-edit mr-1"></i>Edit
            </button>
        `);

        // Delete button
        buttons.push(`
            <button onclick="userDetailsManager.deleteBroker(${setupId})"
                    class="px-2 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded text-xs"
                    title="Delete Broker Instance">
                <i class="fas fa-trash mr-1"></i>Delete
            </button>
        `);

        return buttons.join('');
    }

    /**
     * Edit Saved Brokers - Edit broker configurations saved in database
     */
    async editSavedBrokers() {
        try {
            console.log('📝 Loading saved broker configurations from database...');

            // Get saved broker configurations from saved-brokers endpoint - try both admin IDs
            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            let response = await fetch(`/api/saved-brokers?user_id=${this.userId}&admin_id=${this.adminId}`, {
                headers: {
                    'X-Auth-Token': token,
                    'Content-Type': 'application/json'
                }
            });
            let result = await response.json();

            // If no data with current admin_id, try Admin_1 (fallback for existing data)
            if (response.ok && result.success && result.data.length === 0) {
                console.log('🔄 No saved brokers with current admin, trying Admin_1...');
                response = await fetch(`/api/saved-brokers?user_id=${this.userId}&admin_id=Admin_1`, {
                    headers: {
                        'X-Auth-Token': token,
                        'Content-Type': 'application/json'
                    }
                });
                result = await response.json();
            }

            if (response.ok && result.success) {
                const savedBrokers = result.data || [];
                if (savedBrokers.length === 0) {
                    this.showError('No saved broker configurations found. Please add a broker first using "Add Broker" button.');
                    return;
                }

                console.log('📋 Found saved brokers:', savedBrokers);
                this.showEditSavedBrokersModal(savedBrokers);
            } else {
                this.showError('Failed to load saved broker configurations');
            }
        } catch (error) {
            console.error('❌ Error loading saved brokers:', error);
            this.showError('Error loading saved broker configurations: ' + error.message);
        }
    }

    /**
     * Show edit saved brokers modal - for database broker configurations
     */
    showEditSavedBrokersModal(savedBrokers) {
        console.log('📝 Showing edit saved brokers modal with data:', savedBrokers);

        const brokersList = savedBrokers.map(broker => {
            const brokerName = broker.broker_name || 'Unknown';
            const setupName = broker.trading_setup_name || 'N/A';
            const clientId = broker.client_id || 'N/A';
            const apiKey = (broker.broker_api_key || broker.api_key) ? (broker.broker_api_key || broker.api_key).substring(0, 8) + '...' : 'N/A';
            const createdAt = broker.created_at ? `${new Date(broker.created_at).toLocaleDateString()} ${new Date(broker.created_at).toLocaleTimeString()}` : 'N/A';
            const brokerId = broker.id;

            return `
                <div class="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900 dark:text-slate-100">${brokerName}</h4>
                            <p class="text-sm text-gray-600 dark:text-slate-400">${setupName}</p>
                            <p class="text-xs text-gray-500 dark:text-slate-500">Client ID: ${clientId}</p>
                            <p class="text-xs text-gray-500 dark:text-slate-500">API Key: ${apiKey}</p>
                            <p class="text-xs text-gray-500 dark:text-slate-500">Created: ${createdAt}</p>
                        </div>
                        <div class="flex gap-2">
                            <button onclick="userDetailsManager.editSavedBrokerConfig(${brokerId})"
                                    class="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded text-xs">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </button>
                            <button onclick="userDetailsManager.deleteSavedBroker(${brokerId})"
                                    class="px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded text-xs">
                                <i class="fas fa-trash mr-1"></i>Delete
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Edit Saved Broker Configurations</h4>
                <p class="text-sm text-gray-600 dark:text-slate-400">Edit broker configurations saved in database (added via "Add Broker")</p>
                <div class="space-y-3 max-h-96 overflow-y-auto">
                    ${brokersList}
                </div>
                <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Close</button>
                </div>
            </div>
        `;

        this.showModal('Edit Saved Broker Configurations', modalContent);
    }

    /**
     * Edit saved broker configuration from database
     */
    async editSavedBrokerConfig(brokerId) {
        try {
            console.log('📝 Editing saved broker config for ID:', brokerId);
            console.log('🔍 Function called successfully!');

            // Get the broker from the saved brokers list with proper authorization
            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            const response = await fetch(`/api/saved-brokers?user_id=${this.userId}&admin_id=${this.adminId}`, {
                headers: {
                    'X-Auth-Token': token,
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error('Failed to get saved broker details');
            }

            const savedBrokers = result.data || [];
            const broker = savedBrokers.find(b => b.id === brokerId);

            if (!broker) {
                throw new Error('Saved broker configuration not found');
            }

            console.log('📋 Saved broker data:', broker);

            // Show edit saved broker modal
            this.showEditSavedBrokerModal(broker);

        } catch (error) {
            console.error('❌ Error loading saved broker:', error);
            this.showError('Failed to load saved broker configuration: ' + error.message);
        }
    }

    /**
     * Show edit saved broker modal with dynamic broker-specific forms
     */
    showEditSavedBrokerModal(broker) {
        // Generate broker options from supported brokers API
        const brokerOptions = this.supportedBrokers
            .filter(b => b.status === 'active')
            .map(b => `<option value="${b.name}" ${broker.broker_name === b.name ? 'selected' : ''}>${b.display_name}</option>`)
            .join('');

        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Edit Saved Broker Configuration</h4>
                <p class="text-sm text-gray-600 dark:text-slate-400">Update broker credentials and settings in database</p>

                <div class="space-y-3 max-h-96 overflow-y-auto">
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Broker *</label>
                            <select id="editSavedBrokerType" onchange="userDetailsManager.showEditBrokerSpecificForm(this.value, ${broker.id})" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                                <option value="">Select Broker...</option>
                                ${brokerOptions}
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Setup Name *</label>
                            <input type="text" id="editSavedSetupName" value="${broker.trading_setup_name || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                        </div>
                    </div>

                    <!-- Dynamic Broker-Specific Form Container -->
                    <div id="editBrokerSpecificForm"></div>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.updateSavedBrokerConfig(${broker.id})" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                        <i class="fas fa-save mr-2"></i>Update Configuration
                    </button>
                </div>
            </div>
        `;

        this.showModal('Edit Saved Broker Configuration', modalContent);

        // Automatically show the broker-specific form after modal is shown
        setTimeout(() => {
            this.showEditBrokerSpecificForm(broker.broker_name, broker.id);
        }, 100);
    }

    /**
     * Update saved broker configuration in database (all fields)
     */
    async updateSavedBrokerConfig(brokerId) {
        const brokerType = document.getElementById('editSavedBrokerType').value;
        const setupName = document.getElementById('editSavedSetupName').value;

        // Validate basic fields
        if (!brokerType || !setupName) {
            this.showError('Please select a broker and enter a setup name');
            return;
        }

        // Get broker configuration
        const config = this.BROKER_CONFIGS[brokerType];
        if (!config) {
            this.showError('Invalid broker selected');
            return;
        }

        // Extract dynamic form values based on broker configuration
        const formData = {};
        let hasErrors = false;

        for (const field of config.fields) {
            const elementId = this.generateFieldId(field.name, 'editSaved');
            const element = document.getElementById(elementId);

            if (!element) {
                console.error(`❌ Edit form element not found: ${elementId}`);
                hasErrors = true;
                continue;
            }

            const value = element.value.trim();

            if (field.required && !value) {
                this.showError(`Please fill in ${field.label}`);
                return;
            }

            formData[field.name] = value;
        }

        if (hasErrors) {
            this.showError('Form validation failed. Please check all fields.');
            return;
        }

        try {
            console.log('💾 Updating saved broker configuration for ID:', brokerId);

            const updateData = {
                broker_name: brokerType,
                broker_type: brokerType,
                trading_setup_name: setupName,
                ...formData // Spread the dynamic form data
            };

            const response = await fetch(`/api/saved-brokers/${brokerId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.closeModal();
                this.showSuccess('Saved broker configuration updated successfully!');
                await this.loadBrokerData(); // Refresh broker data
                await this.loadSavedBrokers(); // Refresh saved brokers
            } else {
                throw new Error(result.message || 'Failed to update saved broker configuration');
            }

        } catch (error) {
            console.error('❌ Error updating saved broker:', error);
            this.showError('Failed to update saved broker configuration: ' + error.message);
        }
    }

    /**
     * Delete saved broker configuration from database
     */
    async deleteSavedBroker(brokerId) {
        // Find the broker to get details for confirmation
        const broker = this.savedBrokers.find(b => b.id === brokerId);
        if (!broker) {
            this.showError('Broker configuration not found');
            return;
        }

        // Show app-level confirmation modal
        this.showDeleteConfirmationModal(broker);
    }

    /**
     * Show delete confirmation modal
     */
    showDeleteConfirmationModal(broker) {
        const modalContent = `
            <div class="space-y-4">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-trash text-red-600 dark:text-red-400 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-slate-100">Delete Broker Configuration</h4>
                        <p class="text-sm text-gray-600 dark:text-slate-400">This action cannot be undone</p>
                    </div>
                </div>

                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700 dark:text-slate-300">Broker:</span>
                            <span class="text-sm text-gray-900 dark:text-slate-100">${broker.broker_name}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700 dark:text-slate-300">Setup Name:</span>
                            <span class="text-sm text-gray-900 dark:text-slate-100">${broker.trading_setup_name || 'Unnamed'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700 dark:text-slate-300">Client ID:</span>
                            <span class="text-sm text-gray-900 dark:text-slate-100">${broker.client_id || 'N/A'}</span>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-exclamation-triangle text-yellow-500 mt-0.5 text-sm"></i>
                        <div class="text-xs text-yellow-700 dark:text-yellow-300">
                            <p class="font-medium mb-1">Warning:</p>
                            <p>• This will permanently delete the saved configuration</p>
                            <p>• Any running instances using this config will continue to work</p>
                            <p>• You cannot undo this action</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.confirmDeleteSavedBroker(${broker.id})" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg">
                        <i class="fas fa-trash mr-2"></i>Delete Configuration
                    </button>
                </div>
            </div>
        `;

        this.showModal('Delete Broker Configuration', modalContent);
    }

    /**
     * Confirm and execute broker deletion
     */
    async confirmDeleteSavedBroker(brokerId) {
        try {
            this.closeModal();
            console.log('🗑️ Deleting saved broker configuration for ID:', brokerId);

            // Find broker for audit logging
            const broker = this.savedBrokers.find(b => b.id === brokerId);

            // Log deletion attempt
            await this.logAuditAction('BROKER_CONFIG_DELETE_ATTEMPT', {
                user_id: this.userId,
                admin_id: this.adminId,
                broker_id: brokerId,
                broker_name: broker?.broker_name || 'unknown',
                setup_name: broker?.trading_setup_name || 'unknown',
                action: 'Delete saved broker configuration',
                timestamp: new Date().toISOString()
            });

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            const response = await fetch(`/api/saved-brokers/${brokerId}`, {
                method: 'DELETE',
                headers: {
                    'X-Auth-Token': token,
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (response.ok && result.success) {
                // Log successful deletion
                await this.logAuditAction('BROKER_CONFIG_DELETE_SUCCESS', {
                    user_id: this.userId,
                    admin_id: this.adminId,
                    broker_id: brokerId,
                    broker_name: broker?.broker_name || 'unknown',
                    setup_name: broker?.trading_setup_name || 'unknown',
                    action: 'Saved broker configuration deleted successfully',
                    timestamp: new Date().toISOString()
                });

                this.closeModal();
                this.showSuccess('✅ Saved broker configuration deleted successfully!');
                await this.loadBrokerData(); // Refresh broker data
                await this.loadSavedBrokers(); // Refresh saved brokers
            } else {
                // Log failed deletion
                await this.logAuditAction('BROKER_CONFIG_DELETE_FAILED', {
                    user_id: this.userId,
                    admin_id: this.adminId,
                    broker_id: brokerId,
                    error: result.message || 'Unknown error',
                    action: 'Failed to delete saved broker configuration',
                    timestamp: new Date().toISOString()
                });

                throw new Error(result.message || 'Failed to delete saved broker configuration');
            }

        } catch (error) {
            console.error('❌ Error deleting saved broker:', error);
            this.showError('Failed to delete saved broker configuration: ' + error.message);
        }
    }

    /**
     * Edit broker configuration (from top Edit Broker button)
     */
    async editBrokerConfig(brokerId) {
        try {
            console.log('📝 Editing broker config for ID:', brokerId);

            // Get broker details
            const response = await fetch(`/api/broker-details/${brokerId}`);
            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error('Failed to get broker details');
            }

            const broker = result.data;
            console.log('📋 Broker config data:', broker);

            // Show edit configuration modal
            this.showEditBrokerConfigModal(broker);

        } catch (error) {
            console.error('❌ Error loading broker config:', error);
            this.showError('Failed to load broker configuration: ' + error.message);
        }
    }

    /**
     * Show edit broker configuration modal
     */
    showEditBrokerConfigModal(broker) {
        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Edit Broker Configuration</h4>
                <p class="text-sm text-gray-600 dark:text-slate-400">Update broker settings and credentials</p>

                <div class="space-y-3 max-h-96 overflow-y-auto">
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Broker</label>
                            <input type="text" id="editConfigBrokerName" value="${broker.broker_name || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Setup Name</label>
                            <input type="text" id="editConfigSetupName" value="${broker.setup_name || broker.trading_setup_name || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Client ID</label>
                        <input type="text" id="editConfigClientId" value="${broker.broker_client_id || broker.client_id || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                    </div>

                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">API Key</label>
                            <input type="text" id="editConfigApiKey" value="${broker.broker_api_key || broker.api_key || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">API Secret</label>
                            <input type="password" id="editConfigApiSecret" value="${broker.broker_api_secret || broker.api_secret || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Trading PIN</label>
                            <input type="password" id="editConfigTradingPin" value="${broker.trading_pin || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">TOTP Secret</label>
                            <input type="text" id="editConfigTotpSecret" value="${broker.totp_secret || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                        </div>
                    </div>

                    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-exclamation-triangle text-yellow-500 mt-0.5 text-sm"></i>
                            <div class="text-xs text-yellow-700 dark:text-yellow-300">
                                <p class="font-medium mb-1">Note:</p>
                                <p>Changing these settings will update the broker configuration. If the broker is currently running, you may need to restart it for changes to take effect.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.saveBrokerConfig(${broker.id})" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                        <i class="fas fa-save mr-2"></i>Save Configuration
                    </button>
                </div>
            </div>
        `;

        this.showModal('Edit Broker Configuration', modalContent);
    }

    /**
     * Save broker configuration changes
     */
    async saveBrokerConfig(brokerId) {
        const setupName = document.getElementById('editConfigSetupName').value;
        const clientId = document.getElementById('editConfigClientId').value;
        const apiKey = document.getElementById('editConfigApiKey').value;
        const apiSecret = document.getElementById('editConfigApiSecret').value;
        const tradingPin = document.getElementById('editConfigTradingPin').value;
        const totpSecret = document.getElementById('editConfigTotpSecret').value;

        if (!setupName || !clientId || !apiKey || !apiSecret || !tradingPin) {
            this.showError('Please fill in all required fields');
            return;
        }

        try {
            console.log('💾 Saving broker configuration for ID:', brokerId);

            const configData = {
                trading_setup_name: setupName,
                client_id: clientId,
                api_key: apiKey,
                api_secret: apiSecret,
                trading_pin: tradingPin,
                totp_secret: totpSecret
            };

            // Make API call to update saved broker configuration
            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            const response = await fetch(`/api/saved-brokers/${brokerId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Auth-Token': token
                },
                body: JSON.stringify(configData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.closeModal();
                this.showSuccess('✅ Broker configuration updated successfully!');

                // Refresh data to show changes
                await this.loadSavedBrokers();
            } else {
                throw new Error(result.message || 'Failed to update broker configuration');
            }

        } catch (error) {
            console.error('❌ Error saving broker configuration:', error);
            this.showError('Failed to save configuration: ' + error.message);
        }
    }

    /**
     * Setup Broker - Show setup options using saved broker configurations
     */
    async setupBroker() {
        try {
            console.log('⚙️ Loading saved broker configurations for setup...');

            // Get saved broker configurations for setup - try both admin IDs
            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            let response = await fetch(`/api/saved-brokers?user_id=${this.userId}&admin_id=${this.adminId}`, {
                headers: {
                    'X-Auth-Token': token,
                    'Content-Type': 'application/json'
                }
            });
            let result = await response.json();

            // If no data with current admin_id, try Admin_1 (fallback for existing data)
            if (response.ok && result.success && result.data.length === 0) {
                console.log('🔄 No saved brokers with current admin, trying Admin_1...');
                response = await fetch(`/api/saved-brokers?user_id=${this.userId}&admin_id=Admin_1`, {
                    headers: {
                        'X-Auth-Token': token,
                        'Content-Type': 'application/json'
                    }
                });
                result = await response.json();
            }

            if (response.ok && result.success) {
                const savedBrokers = result.data || [];
                if (savedBrokers.length === 0) {
                    this.showError('No saved broker configurations found. Please add a broker first using "Add Broker" button.');
                    return;
                }

                console.log('📋 Found saved brokers for setup:', savedBrokers);
                this.showSetupBrokerModal(savedBrokers);
            } else {
                this.showError('Failed to load saved broker configurations');
            }
        } catch (error) {
            console.error('❌ Error loading saved brokers for setup:', error);
            this.showError('Error loading saved broker configurations: ' + error.message);
        }
    }

    /**
     * Show setup broker modal with saved broker configurations
     */
    showSetupBrokerModal(savedBrokers) {
        console.log('🔧 Showing setup broker modal with saved brokers:', savedBrokers);

        const brokerOptions = savedBrokers.map(broker =>
            `<option value="${broker.id}">${broker.broker_name} - ${broker.trading_setup_name}</option>`
        ).join('');

        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Setup Broker</h4>
                <p class="text-sm text-gray-600 dark:text-slate-400">Create new broker instance from saved configuration</p>

                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-2">Select Saved Broker Configuration (${savedBrokers.length} available)</label>
                        <select id="setupBrokerSelect" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100" required>
                            <option value="">Select Saved Broker Configuration</option>
                            ${brokerOptions}
                        </select>
                    </div>

                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                        <div class="flex items-start space-x-3">
                            <i class="fas fa-info-circle text-green-500 mt-0.5"></i>
                            <div class="text-sm text-green-700 dark:text-green-300">
                                <p class="font-medium mb-1">Setup Process:</p>
                                <ul class="list-disc list-inside space-y-1 text-xs">
                                    <li>Download OpenAlgo instance</li>
                                    <li>Configure environment variables</li>
                                    <li>Prepare broker for connection</li>
                                    <li>After setup, use Connect buttons for authentication</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.executeSetup()" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg">
                        <i class="fas fa-cog mr-2"></i>Setup Broker
                    </button>
                </div>
            </div>
        `;

        this.showModal('Setup Broker', modalContent);
    }

    /**
     * Execute broker setup - Create new broker instance from saved configuration
     */
    async executeSetup() {
        const savedBrokerId = document.getElementById('setupBrokerSelect').value;

        if (!savedBrokerId) {
            this.showError('Please select a saved broker configuration');
            return;
        }

        try {
            console.log('⚙️ Executing broker setup for saved broker ID:', savedBrokerId);

            // Create broker setup using the saved configuration
            const setupData = {
                user_id: this.userId,
                admin_id: this.adminId,
                saved_broker_id: savedBrokerId
            };

            const response = await fetch('/api/broker-setup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(setupData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.closeModal();
                this.showSuccess('Broker setup created successfully! New broker instance is being prepared.');
                await this.loadBrokerData(); // Refresh broker data to show new setup
            } else {
                throw new Error(result.message || 'Failed to create broker setup');
            }

        } catch (error) {
            console.error('❌ Setup failed:', error);
            this.showError('Failed to setup broker: ' + error.message);
        }
    }

    /**
     * Refresh Data - Reload all page data
     */
    async refreshData() {
        try {
            console.log('🔄 Refreshing all data...');
            this.showNotification('Refreshing data...', 'info');

            await this.loadUserData();
            await this.loadBrokerData();
            await this.loadSavedBrokers();
            this.hideLoadingSpinners();

            this.showSuccess('Data refreshed successfully!');
        } catch (error) {
            console.error('❌ Error refreshing data:', error);
            this.showError('Failed to refresh data: ' + error.message);
        }
    }

    /**
     * Edit User - Show edit user modal
     */
    editUser() {
        const user = this.userData || { username: this.userId, email: `user${this.userId}@algofactory.in` };

        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Edit User Details</h4>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Username</label>
                        <input type="text" id="editUsername" value="${user.username || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100" readonly>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Full Name</label>
                        <input type="text" id="editFullName" value="${user.full_name || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Email</label>
                        <input type="email" id="editEmail" value="${user.email || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Phone</label>
                        <input type="text" id="editPhone" value="${user.phone || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.saveUserChanges()" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">Save Changes</button>
                </div>
            </div>
        `;

        this.showModal('Edit User', modalContent);
    }

    /**
     * Save user changes
     */
    async saveUserChanges() {
        const fullName = document.getElementById('editFullName').value;
        const email = document.getElementById('editEmail').value;
        const phone = document.getElementById('editPhone').value;

        try {
            console.log('💾 Saving user changes...');

            // For now, just update local data and show success
            if (this.userData) {
                this.userData.full_name = fullName;
                this.userData.email = email;
                this.userData.phone = phone;
                this.updateUserInfo();
            }

            this.closeModal();
            this.showSuccess('User details updated successfully!');

        } catch (error) {
            console.error('❌ Error saving user changes:', error);
            this.showError('Failed to save changes: ' + error.message);
        }
    }

    /**
     * Toggle user status (activate/deactivate)
     */
    async toggleStatus() {
        const currentStatus = this.userData?.is_active ? 'active' : 'inactive';
        const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
        const action = newStatus === 'active' ? 'activate' : 'deactivate';

        if (!confirm(`Are you sure you want to ${action} this user?`)) {
            return;
        }

        try {
            console.log(`🔄 ${action}ing user...`);

            // For now, just update UI and show success
            if (this.userData) {
                this.userData.is_active = newStatus === 'active' ? 1 : 0;
            }

            // Update button text
            const toggleBtn = document.getElementById('toggleStatusBtn');
            if (toggleBtn) {
                const icon = toggleBtn.querySelector('i');
                const textSpan = toggleBtn.querySelector('.hidden.sm\\:inline');
                const mobileSpan = toggleBtn.querySelector('.sm\\:hidden');

                if (newStatus === 'active') {
                    icon.className = 'fas fa-pause mr-2';
                    textSpan.textContent = 'Deactivate';
                    mobileSpan.textContent = 'Deactivate';
                    toggleBtn.className = toggleBtn.className.replace('bg-green-100 hover:bg-green-200 text-green-700', 'bg-gray-100 hover:bg-gray-200 text-gray-700');
                } else {
                    icon.className = 'fas fa-play mr-2';
                    textSpan.textContent = 'Activate';
                    mobileSpan.textContent = 'Activate';
                    toggleBtn.className = toggleBtn.className.replace('bg-gray-100 hover:bg-gray-200 text-gray-700', 'bg-green-100 hover:bg-green-200 text-green-700');
                }
            }

            this.showSuccess(`User ${action}d successfully!`);

        } catch (error) {
            console.error('❌ Error toggling status:', error);
            this.showError(`Failed to ${action} user: ` + error.message);
        }
    }

    /**
     * Reset user password
     */
    resetPassword() {
        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Reset Password</h4>
                <p class="text-sm text-gray-600 dark:text-slate-400">Generate a new password for user ${this.userId}</p>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-exclamation-triangle text-yellow-500 mt-0.5"></i>
                        <div class="text-sm text-yellow-700 dark:text-yellow-300">
                            <p class="font-medium mb-1">Warning:</p>
                            <p>This will generate a new random password and invalidate the current password. The user will need to use the new password to login.</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.executePasswordReset()" class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg">
                        <i class="fas fa-key mr-2"></i>Reset Password
                    </button>
                </div>
            </div>
        `;

        this.showModal('Reset Password', modalContent);
    }

    /**
     * Execute password reset
     */
    async executePasswordReset() {
        try {
            console.log('🔑 Resetting password for user:', this.userId);

            // Generate random password for demo
            const newPassword = 'temp' + Math.random().toString(36).substring(2, 8);

            this.closeModal();

            // Show new password in a modal
            const passwordModal = `
                <div class="space-y-4">
                    <h4 class="font-medium text-gray-900 dark:text-slate-100">Password Reset Successful</h4>
                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                        <p class="text-sm text-green-700 dark:text-green-300 mb-2">New password generated:</p>
                        <div class="bg-white dark:bg-slate-800 border rounded p-3 font-mono text-lg text-center">
                            ${newPassword}
                        </div>
                        <p class="text-xs text-green-600 dark:text-green-400 mt-2">Please save this password and share it with the user securely.</p>
                    </div>
                    <div class="flex justify-end pt-4">
                        <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg">Close</button>
                    </div>
                </div>
            `;

            this.showModal('New Password', passwordModal);

        } catch (error) {
            console.error('❌ Error resetting password:', error);
            this.showError('Failed to reset password: ' + error.message);
        }
    }

    /**
     * Generate form element ID from field name
     */
    generateFieldId(fieldName, prefix = 'add') {
        // Convert snake_case to PascalCase: api_key -> ApiKey, client_id -> ClientId
        return prefix + fieldName.split('_').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join('');
    }

    /**
     * Show broker-specific form for editing with pre-filled values
     */
    showEditBrokerSpecificForm(brokerName, brokerId) {
        const formContainer = document.getElementById('editBrokerSpecificForm');

        console.log('🔧 Selected broker for edit:', brokerName);
        console.log('🔧 Broker ID:', brokerId);

        if (!brokerName || !this.BROKER_CONFIGS[brokerName]) {
            if (formContainer) formContainer.innerHTML = '';
            return;
        }

        const config = this.BROKER_CONFIGS[brokerName];
        console.log(`🔧 Showing ${config.name} edit form with ${config.fields.length} fields`);

        // Get current broker data to pre-fill values
        const broker = this.savedBrokers.find(b => b.id === brokerId);

        const fieldsHTML = config.fields.map(field => {
            const currentValue = broker ? (broker[field.name] || '') : '';
            return `
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-2">
                        ${field.label} ${field.required ? '<span class="text-red-500">*</span>' : ''}
                    </label>
                    <input
                        type="${field.type}"
                        name="${field.name}"
                        id="${this.generateFieldId(field.name, 'editSaved')}"
                        value="${currentValue}"
                        ${field.required ? 'required' : ''}
                        placeholder="${field.placeholder}"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-${config.color}-500 dark:bg-slate-700 dark:text-slate-100"
                    >
                </div>
            `;
        }).join('');

        if (formContainer) {
            formContainer.innerHTML = `
                <div class="bg-${config.color}-50 dark:bg-${config.color}-900/20 p-4 rounded-lg">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-slate-100 mb-4">
                        <i class="fas fa-edit mr-2 text-${config.color}-600"></i>${config.name} Configuration
                    </h4>
                    <div class="space-y-4">
                        ${fieldsHTML}
                    </div>
                    <div class="mt-4 p-3 bg-${config.color}-100 dark:bg-${config.color}-900/30 rounded-lg">
                        <p class="text-sm text-${config.color}-700 dark:text-${config.color}-300">
                            <i class="fas fa-info-circle mr-2"></i>${config.info}
                        </p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Show broker-specific form based on selected broker
     */
    showBrokerSpecificForm(brokerName) {
        const formContainer = document.getElementById('brokerSpecificForm');
        const submitButtons = document.getElementById('submitButtons');

        console.log('🔧 Selected broker:', brokerName);
        console.log('🔧 Available configs:', Object.keys(this.BROKER_CONFIGS));

        if (!brokerName || !this.BROKER_CONFIGS[brokerName]) {
            if (formContainer) formContainer.innerHTML = '';
            if (submitButtons) submitButtons.style.display = 'none';
            return;
        }

        const config = this.BROKER_CONFIGS[brokerName];
        console.log(`🔧 Showing ${config.name} specific form with ${config.fields.length} fields`);

        const fieldsHTML = config.fields.map(field => `
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-2">
                    ${field.label} ${field.required ? '<span class="text-red-500">*</span>' : ''}
                </label>
                <input
                    type="${field.type}"
                    name="${field.name}"
                    id="${this.generateFieldId(field.name, 'add')}"
                    ${field.required ? 'required' : ''}
                    placeholder="${field.placeholder}"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-${config.color}-500 dark:bg-slate-700 dark:text-slate-100"
                >
            </div>
        `).join('');

        if (formContainer) {
            formContainer.innerHTML = `
                <div class="bg-${config.color}-50 dark:bg-${config.color}-900/20 p-4 rounded-lg">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-slate-100 mb-4">
                        <i class="fas fa-cog mr-2 text-${config.color}-600"></i>${config.name} Configuration
                    </h4>
                    <div class="space-y-4">
                        ${fieldsHTML}
                    </div>
                    <div class="mt-4 p-3 bg-${config.color}-100 dark:bg-${config.color}-900/30 rounded-lg">
                        <p class="text-sm text-${config.color}-700 dark:text-${config.color}-300">
                            <i class="fas fa-info-circle mr-2"></i>${config.info}
                        </p>
                    </div>
                </div>
            `;
        }

        if (submitButtons) {
            submitButtons.style.display = 'block';
        }
    }

    /**
     * Add Broker - Show add broker modal with dynamic broker-specific forms
     */
    addBroker() {
        // Generate broker options from supported brokers API
        const brokerOptions = this.supportedBrokers
            .filter(broker => broker.status === 'active')
            .map(broker => `<option value="${broker.name}">${broker.display_name}</option>`)
            .join('');

        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Add New Broker</h4>
                <div class="space-y-3 max-h-96 overflow-y-auto">
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Broker *</label>
                            <select id="addBrokerType" onchange="userDetailsManager.showBrokerSpecificForm(this.value)" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                                <option value="">Select Broker...</option>
                                ${brokerOptions}
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Setup Name *</label>
                            <input type="text" id="addSetupName" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100" placeholder="Enter setup name">
                        </div>
                    </div>

                    <!-- Dynamic Broker-Specific Form Container -->
                    <div id="brokerSpecificForm"></div>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-slate-600" id="submitButtons" style="display: none;">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.saveBroker()" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                        <i class="fas fa-plus mr-2"></i>Add Broker
                    </button>
                </div>
            </div>
        `;

        this.showModal('Add Broker', modalContent);
    }

    /**
     * Get broker type based on broker name (for OpenAlgo compatibility)
     */
    getBrokerType(brokerName) {
        const xtsbrokers = ['fivepaisaxts', 'compositedge', 'tradejini', 'wisdom'];
        return xtsbrokers.includes(brokerName) ? 'XTS' : 'REST';
    }

    /**
     * Save new broker with all required fields (OpenAlgo compatible)
     */
    async saveBroker() {
        console.log('🚀 Starting saveBroker...');

        const brokerName = document.getElementById('addBrokerType').value;
        const setupName = document.getElementById('addSetupName').value;

        console.log('📋 Form values:', { brokerName, setupName });

        // Validate basic fields
        if (!brokerName || !setupName) {
            this.showError('Please select a broker and enter a setup name');
            return;
        }

        // Get broker configuration
        const config = this.BROKER_CONFIGS[brokerName];
        console.log('🔧 Broker config:', config);

        if (!config) {
            this.showError('Invalid broker selected');
            return;
        }

        // Extract dynamic form values based on broker configuration
        const formData = {};
        let hasErrors = false;

        console.log('🔍 Available form elements:', Array.from(document.querySelectorAll('[id^="add"]')).map(el => `${el.id}: "${el.value}"`));

        for (const field of config.fields) {
            const elementId = this.generateFieldId(field.name, 'add');
            console.log(`🔍 Looking for element: ${elementId} (field: ${field.name})`);

            let element = document.getElementById(elementId);

            // Fallback: try alternative ID formats if the generated one doesn't work
            if (!element) {
                const alternativeIds = [
                    `add${field.name}`,
                    `add${field.name.charAt(0).toUpperCase() + field.name.slice(1)}`,
                    `add${field.name.replace('_', '')}`,
                    `add${field.name.replace(/_/g, '')}`
                ];

                for (const altId of alternativeIds) {
                    element = document.getElementById(altId);
                    if (element) {
                        console.log(`✅ Found element with alternative ID: ${altId}`);
                        break;
                    }
                }
            }

            if (!element) {
                console.error(`❌ Form element not found: ${elementId}`);
                console.error(`Tried alternatives:`, [elementId, `add${field.name}`, `add${field.name.charAt(0).toUpperCase() + field.name.slice(1)}`]);
                hasErrors = true;
                continue;
            }

            console.log(`✅ Found element: ${element.id} with value: "${element.value}"`);

            const value = element.value.trim();

            if (field.required && !value) {
                this.showError(`Please fill in ${field.label}`);
                return;
            }

            formData[field.name] = value;
        }

        if (hasErrors) {
            this.showError('Form validation failed. Please check all fields.');
            return;
        }

        try {
            console.log(`💾 Saving broker ${brokerName} for user ${this.userId}...`);

            // Determine broker type based on OpenAlgo standards
            const brokerType = this.getBrokerType(brokerName);

            // Ensure all required database fields are present with defaults
            const brokerData = {
                user_id: this.userId,
                admin_id: this.adminId,
                broker_name: brokerName.toLowerCase(),
                broker_type: brokerType,
                trading_setup_name: setupName,
                // Default values for all possible fields
                client_id: '',
                api_key: '',
                api_secret: '',
                trading_pin: '',
                totp_secret: '',
                // Override with actual form data
                ...formData
            };

            console.log('📤 Sending broker data to API:', brokerData);

            console.log(`📋 Broker data:`, {
                user: this.userId,
                broker: brokerName,
                type: brokerType,
                setup: setupName
            });

            // Get auth token for API request
            const token = localStorage.getItem('authToken');
            const headers = {
                'Content-Type': 'application/json'
            };

            if (token) {
                headers['X-Auth-Token'] = token;
            }

            console.log('🚀 Making API request to /api/broker-details');
            console.log('📤 Request headers:', headers);
            console.log('📤 Request body:', JSON.stringify(brokerData, null, 2));

            const response = await fetch('/api/broker-details', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(brokerData)
            });

            console.log('📥 API Response status:', response.status, response.statusText);
            console.log('📥 API Response headers:', Object.fromEntries(response.headers.entries()));

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            console.log('📥 Content-Type:', contentType);

            let result;
            if (contentType && contentType.includes('application/json')) {
                result = await response.json();
                console.log('📥 API Response data:', result);
            } else {
                const text = await response.text();
                console.log('📥 API Response text:', text);
                throw new Error(`Non-JSON response: ${text}`);
            }

            if (response.ok && result.success) {
                this.closeModal();
                this.showSuccess(`✅ Broker ${setupName} (${brokerName}) saved for user ${this.userId}!`);
                await this.loadBrokerData(); // Refresh broker data
                await this.loadSavedBrokers(); // Refresh saved brokers
            } else {
                throw new Error(result.message || 'Failed to add broker');
            }

        } catch (error) {
            console.error('❌ Error saving broker:', error);
            console.error('❌ Error details:', {
                name: error.name,
                message: error.message,
                stack: error.stack
            });

            // Check if it's a network error
            if (error instanceof TypeError && error.message.includes('fetch')) {
                this.showError('Network error: Unable to connect to server. Please check if the API server is running.');
            } else {
                this.showError('Failed to add broker: ' + error.message);
            }
        }
    }

    /**
     * Delete Broker Instance
     */
    async deleteBroker(brokerId) {
        // Find the broker to get details for confirmation
        const broker = this.brokers.find(b => b.id === brokerId || b.setup_id === brokerId);
        if (!broker) {
            this.showError('Broker instance not found');
            return;
        }

        // Show app-level confirmation modal
        this.showDeleteBrokerInstanceModal(broker);
    }

    /**
     * Show delete broker instance confirmation modal
     */
    showDeleteBrokerInstanceModal(broker) {
        const modalContent = `
            <div class="space-y-4">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-trash text-red-600 dark:text-red-400 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-slate-100">Delete Broker Instance</h4>
                        <p class="text-sm text-gray-600 dark:text-slate-400">This will permanently remove the running broker</p>
                    </div>
                </div>

                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700 dark:text-slate-300">Broker:</span>
                            <span class="text-sm text-gray-900 dark:text-slate-100">${broker.broker_name}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700 dark:text-slate-300">Setup Name:</span>
                            <span class="text-sm text-gray-900 dark:text-slate-100">${broker.setup_name || broker.trading_setup_name || 'N/A'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700 dark:text-slate-300">Instance:</span>
                            <span class="text-sm text-gray-900 dark:text-slate-100">${broker.instance_name || 'N/A'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700 dark:text-slate-300">Status:</span>
                            <span class="text-sm text-gray-900 dark:text-slate-100">${broker.status || 'Unknown'}</span>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-exclamation-triangle text-yellow-500 mt-0.5 text-sm"></i>
                        <div class="text-xs text-yellow-700 dark:text-yellow-300">
                            <p class="font-medium mb-1">Warning:</p>
                            <p>• This will permanently delete the broker instance</p>
                            <p>• All trading data and connections will be lost</p>
                            <p>• The saved configuration will remain available</p>
                            <p>• You cannot undo this action</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.confirmDeleteBrokerInstance(${broker.id || broker.setup_id})" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg">
                        <i class="fas fa-trash mr-2"></i>Delete Instance
                    </button>
                </div>
            </div>
        `;

        this.showModal('Delete Broker Instance', modalContent);
    }

    /**
     * Confirm and execute broker instance deletion
     */
    async confirmDeleteBrokerInstance(brokerId) {
        try {
            this.closeModal();

            console.log('🗑️ Deleting broker instance:', brokerId);

            // Find broker for immediate removal and backup
            const broker = this.brokers.find(b => b.id === brokerId || b.setup_id === brokerId);
            if (!broker) {
                this.showError('❌ Broker not found');
                return;
            }

            // 🗑️ IMMEDIATE UI UPDATE - Remove from table instantly
            const brokerIndex = this.brokers.findIndex(b => b.id === brokerId || b.setup_id === brokerId);
            const removedBroker = this.brokers.splice(brokerIndex, 1)[0]; // Remove and backup

            this.showNotification('🗑️ Deleting broker instance...', 'info');
            this.updateBrokerTable(); // Remove from table immediately

            // Log deletion attempt (use removedBroker since it's no longer in this.brokers)
            await this.logAuditAction('BROKER_INSTANCE_DELETE_ATTEMPT', {
                setup_id: brokerId,
                broker_name: removedBroker?.broker_name || 'unknown',
                instance_name: removedBroker?.instance_name || 'unknown',
                action: 'Delete broker instance',
                timestamp: new Date().toISOString()
            });

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Make API call to delete broker (instance + systemd service + database entry)
            const apiCall = async () => {
                try {
                    const startTime = Date.now();
                    const response = await fetch(`/api/broker-setup/${brokerId}`, {
                        method: 'DELETE',
                        headers: {
                            'X-Auth-Token': token,
                            'Content-Type': 'application/json'
                        }
                    });
                    const responseTime = Date.now() - startTime;
                    console.log(`⏱️ Delete broker API response time: ${responseTime}ms`);

                    const result = await response.json();
                    console.log('📋 Delete broker API response:', result);

                    if (response.ok && result.success) {
                        // Log successful deletion
                        await this.logAuditAction('BROKER_INSTANCE_DELETE_SUCCESS', {
                            setup_id: brokerId,
                            broker_name: removedBroker?.broker_name || 'unknown',
                            instance_name: removedBroker?.instance_name || 'unknown',
                            action: 'Broker instance deleted successfully',
                            response_time_ms: responseTime,
                            details: result.message || 'Instance and systemd service removed',
                            timestamp: new Date().toISOString()
                        });

                        this.showSuccess('✅ Broker instance deleted successfully');

                        // Refresh saved brokers in case it affects them
                        setTimeout(() => this.loadSavedBrokers(), 1000);
                    } else {
                        // API returned success: false - this is an actual error
                        throw new Error(result.message || 'Failed to delete broker instance');
                    }
                } catch (apiError) {
                    console.error('❌ API Error deleting broker:', apiError);

                    // Restore broker to table on API error
                    this.brokers.splice(brokerIndex, 0, removedBroker);
                    this.updateBrokerTable();

                    // Log failed deletion
                    await this.logAuditAction('BROKER_INSTANCE_DELETE_FAILED', {
                        setup_id: brokerId,
                        error: apiError.message || 'Unknown error',
                        action: 'Failed to delete broker instance',
                        timestamp: new Date().toISOString()
                    });

                    this.showError('❌ Failed to delete broker instance: ' + apiError.message);
                }
            };

            // Start API call but don't wait for it (table already updated)
            apiCall();
        } catch (error) {
            console.error('❌ Error deleting broker instance:', error);

            // Restore broker to table on general error
            if (removedBroker && brokerIndex >= 0) {
                this.brokers.splice(brokerIndex, 0, removedBroker);
                this.updateBrokerTable();
            }

            // Log error
            await this.logAuditAction('BROKER_INSTANCE_DELETE_ERROR', {
                setup_id: brokerId,
                error: error.message,
                action: 'Error deleting broker instance',
                timestamp: new Date().toISOString()
            });

            this.showError('❌ Failed to delete broker instance: ' + error.message);
        }
    }

    /**
     * Start broker
     */
    async startBroker(setupId) {
        console.log('🚀 Starting broker:', setupId);

        // Find the broker in our data
        const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
        if (!broker) {
            this.showError('❌ Broker not found');
            return;
        }

        try {
            // 🚀 SHOW FAST PROGRESS INDICATOR
            const steps = ['🔧 Environment', '🚀 Starting', '👤 Registering', '✅ Running'];
            this.showProgressIndicator(setupId, 'starting', steps);
            this.showNotification('🚀 Starting broker...', 'info');

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Make API call with progress updates
            const apiCall = async () => {
                try {
                    // Step 1: Environment setup (fast)
                    this.updateProgress(setupId, 1, '🔧 Setting up environment...');
                    await new Promise(resolve => setTimeout(resolve, 150));

                    const response = await fetch(`/api/broker-setup/${setupId}/start`, {
                        method: 'POST',
                        headers: {
                            'X-Auth-Token': token,
                            'Content-Type': 'application/json'
                        }
                    });

                    // Step 2: Starting service (fast)
                    this.updateProgress(setupId, 2, '🚀 Starting Algo service...');

                    const result = await response.json();
                    console.log('📋 Start API response:', result);

                    if (response.ok && result.success) {
                        // Step 3: Registering user (fast)
                        this.updateProgress(setupId, 3, '👤 Registering user...');
                        await new Promise(resolve => setTimeout(resolve, 300));

                        // Step 4: Complete (fast)
                        this.updateProgress(setupId, 4, '✅ Ready for connection');
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // Clear progress and set final status directly
                        this.clearProgress(setupId);
                        broker.setup_status = 'running'; // Direct to running, no intermediate states

                        this.showSuccess('✅ Broker started successfully!');
                        this.updateBrokerTable();

                        // Refresh data to sync with backend
                        setTimeout(() => this.loadBrokerData(), 1000);
                    } else {
                        throw new Error(result.message || 'Failed to start broker');
                    }
                } catch (apiError) {
                    console.error('❌ API Error starting broker:', apiError);
                    // Clear progress and reset to stopped on error
                    this.clearProgress(setupId);
                    broker.setup_status = 'stopped';
                    this.updateBrokerTable();
                    this.showError('❌ Failed to start broker: ' + apiError.message);
                }
            };

            // Start API call but don't wait for it
            apiCall();

        } catch (error) {
            console.error('❌ Error starting broker:', error);

            // Clear loading state and reset to original state on error
            delete broker.loading_state;
            delete broker.loading_message;
            broker.setup_status = 'stopped';
            this.updateBrokerTable();
            this.showError('❌ Failed to start broker: ' + error.message);
        }
    }

    /**
     * Stop broker
     */
    async stopBroker(setupId) {
        console.log('🛑 Stopping broker:', setupId);

        // Find the broker in our data
        const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
        if (!broker) {
            this.showError('❌ Broker not found');
            return;
        }

        try {
            // 🛑 IMMEDIATE UI UPDATE - Show loading state
            broker.loading_state = 'stopping';
            broker.loading_message = '🔄 Stopping...';
            this.showNotification('🛑 Stopping broker...', 'info');
            this.updateBrokerTable(); // Show loading button immediately

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Make API call in background (don't block UI)
            const apiCall = async () => {
                try {
                    const response = await fetch(`/api/broker-setup/${setupId}/stop`, {
                        method: 'POST',
                        headers: {
                            'X-Auth-Token': token,
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();
                    console.log('📋 Stop API response:', result);

                    if (response.ok && result.success) {
                        // Clear loading state and update with actual status
                        delete broker.loading_state;
                        delete broker.loading_message;
                        broker.setup_status = 'stopped';
                        broker.connection_status = 'not_connected'; // Also disconnect when stopped

                        this.showSuccess('✅ Broker stopped successfully!');
                        this.updateBrokerTable(); // Update UI with correct status

                        // Refresh data to sync with backend (shorter delay)
                        setTimeout(() => this.loadBrokerData(), 1000);
                    } else {
                        throw new Error(result.message || 'Failed to stop broker');
                    }
                } catch (apiError) {
                    console.error('❌ API Error stopping broker:', apiError);
                    // Reset to running state on API error
                    broker.setup_status = 'running';
                    this.updateBrokerTable();
                    this.showError('❌ Failed to stop broker: ' + apiError.message);
                }
            };

            // Start API call but don't wait for it
            apiCall();

        } catch (error) {
            console.error('❌ Error stopping broker:', error);

            // Reset to original state on error
            broker.setup_status = 'running';
            this.updateBrokerTable();
            this.showError('❌ Failed to stop broker: ' + error.message);
        }
    }

    /**
     * Connect broker
     */
    async connectBroker(setupId) {
        console.log('🔗 Connecting broker:', setupId);

        // Find the broker in our data
        const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
        if (!broker) {
            this.showError('❌ Broker not found');
            return;
        }

        // BROKER-SPECIFIC CONNECTION LOGIC
        const brokerName = broker.broker_name?.toLowerCase();

        if (brokerName === 'dhan') {
            // Dhan: Show choice between auto and manual OpenAlgo-style
            console.log('🏦 Dhan detected - showing connection options');
            return this.showDhanConnectionOptions(setupId);
        }
        else if (brokerName === 'flattrade') {
            // Flattrade: Show choice between auto and manual
            console.log('🏦 Flattrade detected - showing connection options');
            return this.showFlattradeConnectionOptions(setupId);
        }
        else if (brokerName === 'angel') {
            // Angel: Show choice between auto and manual OpenAlgo-style
            console.log('🏦 Angel detected - showing connection options');
            return this.showAngelConnectionOptions(setupId);
        }
        else {
            // Other brokers: Use existing manual flow
            console.log('🏦 Other broker - using manual connect');
            return this.manualConnectBroker(setupId);
        }
    }

    /**
     * Show Flattrade connection options (Auto vs Manual)
     */
    showFlattradeConnectionOptions(setupId) {
        const modalContent = `
            <div class="space-y-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-link text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Connect Flattrade</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Choose your connection method</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Auto Connect Option -->
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer" onclick="userDetails.autoConnectFlattrade(${setupId})">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-magic text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">Auto Connect</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Automatic OAuth flow</p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="text-xs text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded">
                                ✅ Recommended - No manual steps
                            </div>
                        </div>
                    </div>

                    <!-- Manual Connect Option -->
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer" onclick="userDetails.manualConnectFlattrade(${setupId})">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">Manual Connect</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">OAuth with manual steps</p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded">
                                🔧 Manual - You control the process
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-info-circle text-yellow-600 dark:text-yellow-400 mt-0.5"></i>
                        <div class="text-sm">
                            <p class="text-yellow-800 dark:text-yellow-200 font-medium">Flattrade OAuth Flow</p>
                            <p class="text-yellow-700 dark:text-yellow-300 mt-1">
                                Both methods use OAuth but Auto Connect handles the browser automation for you.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.showModal('Connect Flattrade', modalContent, [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => this.closeModal()
            }
        ]);
    }

    /**
     * Auto connect Flattrade using automation
     */
    async autoConnectFlattrade(setupId) {
        console.log('🤖 Auto connecting Flattrade:', setupId);
        this.closeModal();

        // Use the existing autoConnectBroker method
        return this.autoConnectBroker(setupId);
    }

    /**
     * Manual connect Flattrade with login → OAuth flow (like Angel manual setup)
     */
    async manualConnectFlattrade(setupId) {
        console.log('👤 Manual connecting Flattrade with login → OAuth flow:', setupId);
        this.closeModal();

        try {
            const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
            broker.connection_status = 'connecting';
            this.showNotification('🔧 Starting Flattrade manual authentication...', 'info');
            this.updateBrokerTable();

            // STEP 1: Login to OpenAlgo via backend API (avoids CORS issues)
            this.showNotification('🔐 Step 1: Logging into OpenAlgo instance...', 'info');

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Use backend API to login to OpenAlgo (like Angel manual setup)
            const loginResponse = await fetch(`/api/broker-setup/${setupId}/login`, {
                method: 'POST',
                headers: {
                    'X-Auth-Token': token,
                    'Content-Type': 'application/json'
                }
            });

            if (!loginResponse.ok) {
                throw new Error(`OpenAlgo login failed: ${loginResponse.status}`);
            }

            const loginResult = await loginResponse.json();
            if (!loginResult.success) {
                throw new Error(loginResult.message || 'OpenAlgo login failed');
            }

            console.log('✅ OpenAlgo login completed via backend');
            this.showNotification('✅ Step 1 completed: Logged into OpenAlgo', 'success');

            // Wait a moment for login to complete
            await new Promise(resolve => setTimeout(resolve, 1000));

            // STEP 2: Generate OAuth URL and show manual OAuth form
            this.showNotification('🔗 Step 2: Preparing OAuth authentication...', 'info');

            // Generate OAuth URL from broker data
            const apiKey = broker.broker_api_key || broker.api_key; // Try both field names
            let oauthApiKey = apiKey;

            if (apiKey && apiKey.includes(':::')) {
                oauthApiKey = apiKey.split(':::')[1];
            }

            console.log('🔧 OAuth URL generation:');
            console.log('   Full API Key:', apiKey);
            console.log('   Extracted API Key:', oauthApiKey);

            if (!oauthApiKey || oauthApiKey === 'undefined') {
                throw new Error('API key is undefined or invalid');
            }

            const oauthUrl = `https://auth.flattrade.in/?app_key=${oauthApiKey}`;
            const credentials = {
                client_code: broker.broker_client_id || broker.client_id,
                password: broker.trading_pin,
                dob: broker.totp_secret
            };

            // Show OAuth modal with login completed confirmation
            const port = broker.instance_name ? broker.instance_name.split('-').pop() : '5096';
            const openalgoUrl = `http://127.0.0.1:${port}`;
            this.showFlattradeManualOAuthWithLogin(oauthUrl, credentials, setupId, openalgoUrl);

        } catch (error) {
            console.error('❌ Manual Flattrade setup failed:', error);
            const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
            broker.connection_status = 'not_connected';
            this.updateBrokerTable();
            this.showError(`❌ Setup failed: ${error.message}`);
        }
    }

    /**
     * Show Flattrade OAuth guidance modal
     */
    showFlattradeOAuthGuidance(oauthUrl, credentials, setupId) {
        const modalContent = `
            <div class="space-y-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-external-link-alt text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Flattrade OAuth Login</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Complete authentication on Flattrade's website</p>
                    </div>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-3">📋 Step-by-step instructions:</h4>
                    <ol class="list-decimal list-inside space-y-2 text-sm text-blue-800 dark:text-blue-200">
                        <li>Click "Open Flattrade Login" below</li>
                        <li>Enter your credentials on Flattrade's website:</li>
                        <ul class="list-disc list-inside ml-4 mt-1 space-y-1">
                            <li><strong>Client Code:</strong> ${credentials.client_code}</li>
                            <li><strong>Password:</strong> ${credentials.password}</li>
                            <li><strong>DOB:</strong> ${credentials.dob}</li>
                        </ul>
                        <li>Click "Login" on Flattrade's website</li>
                        <li>You'll be redirected back to OpenAlgo automatically</li>
                        <li>Your broker will be connected!</li>
                    </ol>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mt-0.5"></i>
                        <div class="text-sm">
                            <p class="text-yellow-800 dark:text-yellow-200 font-medium">Important:</p>
                            <p class="text-yellow-700 dark:text-yellow-300 mt-1">
                                Keep this window open. After logging in on Flattrade, you'll be redirected back here.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-center">
                    <button onclick="window.open('${oauthUrl}', '_blank')" class="btn btn-primary flex items-center space-x-2">
                        <i class="fas fa-external-link-alt"></i>
                        <span>Open Flattrade Login</span>
                    </button>
                </div>
            </div>
        `;

        this.showModal('Flattrade OAuth', modalContent, [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => {
                    this.closeModal();
                    // Reset broker status
                    const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
                    broker.connection_status = 'not_connected';
                    this.updateBrokerTable();
                }
            },
            {
                text: 'I completed the login',
                class: 'btn-success',
                action: () => {
                    this.closeModal();
                    this.checkFlattradeConnection(setupId);
                }
            }
        ]);
    }

    /**
     * Show Flattrade manual OAuth with login completed (like Angel manual setup)
     */
    showFlattradeManualOAuthWithLogin(oauthUrl, credentials, setupId, openalgoUrl) {
        // Auto-open OAuth URL immediately
        const oauthWindow = window.open(oauthUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

        const modalContent = `
            <div class="space-y-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Flattrade Manual Authentication</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Login ✅ → OAuth Ready</p>
                    </div>
                </div>

                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400 mt-0.5"></i>
                        <div>
                            <h4 class="font-medium text-green-800 dark:text-green-200">OpenAlgo Login Completed</h4>
                            <div class="text-sm text-green-700 dark:text-green-300 mt-1">
                                <p>✅ Logged into OpenAlgo instance: ${openalgoUrl}</p>
                                <p>✅ OAuth window opened automatically</p>
                                <p>✅ Ready for Flattrade authentication</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                        <i class="fas fa-key text-blue-600 dark:text-blue-400 mr-2"></i>
                        Complete OAuth Authentication
                    </h4>
                    <div class="grid grid-cols-1 gap-3">
                        <div class="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded border">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Client Code:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-sm text-gray-900 dark:text-white">${credentials.client_code}</span>
                                <button onclick="navigator.clipboard.writeText('${credentials.client_code}')" class="btn btn-xs btn-ghost">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded border">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Password:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-sm text-gray-900 dark:text-white">${credentials.password}</span>
                                <button onclick="navigator.clipboard.writeText('${credentials.password}')" class="btn btn-xs btn-ghost">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded border">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">DOB:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-sm text-gray-900 dark:text-white">${credentials.dob}</span>
                                <button onclick="navigator.clipboard.writeText('${credentials.dob}')" class="btn btn-xs btn-ghost">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-info-circle text-orange-600 dark:text-orange-400 mt-0.5"></i>
                        <div class="text-sm">
                            <p class="text-orange-800 dark:text-orange-200 font-medium">Complete OAuth in the opened window:</p>
                            <ol class="text-orange-700 dark:text-orange-300 mt-1 list-decimal list-inside space-y-1">
                                <li>Fill in the credentials above</li>
                                <li>Click "Login" on Flattrade's website</li>
                                <li>You'll be redirected back to OpenAlgo</li>
                                <li>Authentication will be detected automatically</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- Auto-check status -->
                <div class="alert alert-info">
                    <i class="fas fa-sync fa-spin"></i>
                    <div>
                        <div class="font-bold">Auto-detecting authentication...</div>
                        <div class="text-sm">We'll automatically detect when you complete the OAuth</div>
                    </div>
                </div>
            </div>
        `;

        this.showModal('Complete Flattrade Authentication', modalContent, [
            {
                text: 'Reopen OAuth',
                class: 'btn-secondary',
                action: () => {
                    window.open(oauthUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                }
            },
            {
                text: 'Check Connection',
                class: 'btn-primary',
                action: () => {
                    this.closeModal();
                    this.checkFlattradeConnection(setupId);
                }
            }
        ]);

        // Start auto-checking for connection every 5 seconds for 5 minutes
        this.startAutoConnectionCheck(setupId, 60); // 60 checks = 5 minutes
    }

    /**
     * Show complete Flattrade manual auth with register → login → OAuth flow
     */
    showCompleteFlattradeManuaAuth(oauthUrl, credentials, setupId) {
        // Auto-open OAuth URL immediately
        const oauthWindow = window.open(oauthUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

        const modalContent = `
            <div class="space-y-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Flattrade Setup Complete</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Register ✅ → Login ✅ → OAuth Ready</p>
                    </div>
                </div>

                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400 mt-0.5"></i>
                        <div>
                            <h4 class="font-medium text-green-800 dark:text-green-200">Setup Completed Successfully</h4>
                            <div class="text-sm text-green-700 dark:text-green-300 mt-1">
                                <p>✅ User registered in OpenAlgo</p>
                                <p>✅ Logged into OpenAlgo instance</p>
                                <p>✅ OAuth window opened automatically</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                        <i class="fas fa-key text-blue-600 dark:text-blue-400 mr-2"></i>
                        Complete OAuth Authentication
                    </h4>
                    <div class="grid grid-cols-1 gap-3">
                        <div class="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded border">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Client Code:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-sm text-gray-900 dark:text-white">${credentials.client_code}</span>
                                <button onclick="navigator.clipboard.writeText('${credentials.client_code}')" class="btn btn-xs btn-ghost">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded border">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Password:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-sm text-gray-900 dark:text-white">${credentials.password}</span>
                                <button onclick="navigator.clipboard.writeText('${credentials.password}')" class="btn btn-xs btn-ghost">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded border">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">DOB:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-sm text-gray-900 dark:text-white">${credentials.dob}</span>
                                <button onclick="navigator.clipboard.writeText('${credentials.dob}')" class="btn btn-xs btn-ghost">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-info-circle text-orange-600 dark:text-orange-400 mt-0.5"></i>
                        <div class="text-sm">
                            <p class="text-orange-800 dark:text-orange-200 font-medium">Complete OAuth in the opened window:</p>
                            <ol class="text-orange-700 dark:text-orange-300 mt-1 list-decimal list-inside space-y-1">
                                <li>Fill in the credentials above</li>
                                <li>Click "Login" on Flattrade's website</li>
                                <li>You'll be redirected back automatically</li>
                                <li>Authentication will be detected automatically</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- Auto-check status -->
                <div class="alert alert-info">
                    <i class="fas fa-sync fa-spin"></i>
                    <div>
                        <div class="font-bold">Auto-detecting authentication...</div>
                        <div class="text-sm">We'll automatically detect when you complete the OAuth</div>
                    </div>
                </div>
            </div>
        `;

        this.showModal('Complete Flattrade Authentication', modalContent, [
            {
                text: 'Reopen OAuth',
                class: 'btn-secondary',
                action: () => {
                    window.open(oauthUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                }
            },
            {
                text: 'I completed OAuth',
                class: 'btn-success',
                action: () => {
                    this.closeModal();
                    this.completeFlattradeOAuth(setupId);
                }
            },
            {
                text: 'Check Connection',
                class: 'btn-primary',
                action: () => {
                    this.closeModal();
                    this.checkFlattradeConnection(setupId);
                }
            }
        ]);

        // Start auto-checking for connection every 5 seconds for 5 minutes
        this.startAutoConnectionCheck(setupId, 60); // 60 checks = 5 minutes
    }

    /**
     * Complete Flattrade OAuth by calling the backend API
     */
    async completeFlattradeOAuth(setupId) {
        try {
            console.log('🔐 Completing Flattrade OAuth for setup:', setupId);

            const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
            if (!broker) {
                throw new Error('Broker not found');
            }

            broker.connection_status = 'connecting';
            this.showNotification('🔐 Finalizing Flattrade authentication...', 'info');
            this.updateBrokerTable();

            // Show modal to get OAuth code from user
            const oauthCode = await this.getOAuthCodeFromUser();
            if (!oauthCode) {
                throw new Error('OAuth code is required');
            }

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Call the OpenAlgo instance directly with the OAuth code
            const openalgoUrl = `http://127.0.0.1:5096/flattrade/callback?code=${encodeURIComponent(oauthCode)}&client=FT045353`;

            // Use a hidden iframe to trigger the callback
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = openalgoUrl;
            document.body.appendChild(iframe);

            // Wait a moment for the callback to process
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Remove the iframe
            document.body.removeChild(iframe);

            // Check if authentication was successful
            const response = await fetch(`/api/broker-setup/${setupId}/status`, {
                method: 'GET',
                headers: {
                    'X-Auth-Token': token
                }
            });

            const result = await response.json();

            if (response.ok && result.success) {
                broker.connection_status = 'connected';
                this.updateBrokerTable();
                this.showSuccess('✅ Flattrade authentication completed successfully!');

                // Refresh data to sync with backend
                setTimeout(() => this.loadBrokerData(), 1000);
            } else {
                throw new Error(result.detail || result.message || 'Failed to complete OAuth');
            }

        } catch (error) {
            console.error('❌ OAuth completion failed:', error);
            const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
            if (broker) {
                broker.connection_status = 'not_connected';
                this.updateBrokerTable();
            }
            this.showError(`❌ OAuth completion failed: ${error.message}`);
        }
    }

    /**
     * Get OAuth code from user via modal
     */
    async getOAuthCodeFromUser() {
        return new Promise((resolve) => {
            const modalContent = `
                <div class="space-y-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                            <i class="fas fa-key text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Enter OAuth Code</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Copy the code from the callback URL</p>
                        </div>
                    </div>

                    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <h4 class="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
                            <i class="fas fa-info-circle text-yellow-600 dark:text-yellow-400 mr-2"></i>
                            How to get the OAuth code:
                        </h4>
                        <ol class="text-sm text-yellow-800 dark:text-yellow-200 space-y-1">
                            <li>1. After completing OAuth, you'll see a URL like:</li>
                            <li class="font-mono text-xs bg-white dark:bg-gray-800 p-2 rounded border">
                                https://app.algofactory.in/flattrade/callback?code=<span class="text-red-600">COPY_THIS_PART</span>&client=FT045353
                            </li>
                            <li>2. Copy the code parameter (the long string after "code=")</li>
                            <li>3. Paste it in the field below</li>
                        </ol>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-2">OAuth Code:</label>
                        <input type="text" id="oauthCodeInput" placeholder="Paste the OAuth code here..."
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                    </div>
                </div>
            `;

            this.showModal('Enter OAuth Code', modalContent, [
                {
                    text: 'Cancel',
                    class: 'btn-secondary',
                    action: () => {
                        this.closeModal();
                        resolve(null);
                    }
                },
                {
                    text: 'Submit Code',
                    class: 'btn-primary',
                    action: () => {
                        const code = document.getElementById('oauthCodeInput').value.trim();
                        this.closeModal();
                        resolve(code);
                    }
                }
            ]);

            // Focus on the input field
            setTimeout(() => {
                const input = document.getElementById('oauthCodeInput');
                if (input) input.focus();
            }, 100);
        });
    }

    /**
     * Show improved Flattrade OAuth with auto-open and better UX
     */
    showImprovedFlattradeOAuth(oauthUrl, credentials, setupId) {
        // Auto-open OAuth URL immediately
        const oauthWindow = window.open(oauthUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

        const modalContent = `
            <div class="space-y-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-external-link-alt text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Flattrade OAuth Opened</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Complete authentication in the new window</p>
                    </div>
                </div>

                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400 mt-0.5"></i>
                        <div>
                            <h4 class="font-medium text-green-800 dark:text-green-200">OAuth Window Opened</h4>
                            <p class="text-sm text-green-700 dark:text-green-300 mt-1">
                                A new window has opened with Flattrade's login page. Complete the authentication there.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                        <i class="fas fa-key text-blue-600 dark:text-blue-400 mr-2"></i>
                        Your Credentials
                    </h4>
                    <div class="grid grid-cols-1 gap-3">
                        <div class="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded border">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Client Code:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-sm text-gray-900 dark:text-white">${credentials.client_code}</span>
                                <button onclick="navigator.clipboard.writeText('${credentials.client_code}')" class="btn btn-xs btn-ghost">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded border">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Password:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-sm text-gray-900 dark:text-white">${credentials.password}</span>
                                <button onclick="navigator.clipboard.writeText('${credentials.password}')" class="btn btn-xs btn-ghost">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded border">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">DOB:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-sm text-gray-900 dark:text-white">${credentials.dob}</span>
                                <button onclick="navigator.clipboard.writeText('${credentials.dob}')" class="btn btn-xs btn-ghost">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-info-circle text-yellow-600 dark:text-yellow-400 mt-0.5"></i>
                        <div class="text-sm">
                            <p class="text-yellow-800 dark:text-yellow-200 font-medium">Quick Steps:</p>
                            <ol class="text-yellow-700 dark:text-yellow-300 mt-1 list-decimal list-inside space-y-1">
                                <li>Enter credentials in the OAuth window</li>
                                <li>Click "Login" on Flattrade's website</li>
                                <li>You'll be redirected back automatically</li>
                                <li>Click "Check Connection" below</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- Auto-check connection -->
                <div id="auto-check-status" class="hidden">
                    <div class="alert alert-info">
                        <i class="fas fa-sync fa-spin"></i>
                        <div>
                            <div class="font-bold">Auto-checking connection...</div>
                            <div class="text-sm">We'll automatically detect when you complete the OAuth</div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.showModal('Flattrade OAuth', modalContent, [
            {
                text: 'Reopen OAuth',
                class: 'btn-secondary',
                action: () => {
                    window.open(oauthUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                }
            },
            {
                text: 'Check Connection',
                class: 'btn-primary',
                action: () => {
                    this.closeModal();
                    this.checkFlattradeConnection(setupId);
                }
            }
        ]);

        // Start auto-checking for connection every 10 seconds
        this.startAutoConnectionCheck(setupId, 30); // Check for 5 minutes
    }

    /**
     * Auto-check connection status periodically
     */
    startAutoConnectionCheck(setupId, maxChecks) {
        let checkCount = 0;

        const checkInterval = setInterval(async () => {
            checkCount++;

            if (checkCount >= maxChecks) {
                clearInterval(checkInterval);
                return;
            }

            try {
                const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
                if (!broker) return;

                const port = broker.instance_name ? broker.instance_name.split('-').pop() : '5096';

                // Test actual authentication
                const authResponse = await fetch(`http://127.0.0.1:${port}/api/v1/auth`, {
                    method: 'GET'
                });

                if (authResponse.ok) {
                    const authResult = await authResponse.json();

                    if (authResult.status === 'success' || authResult.message === 'success') {
                        // Connected!
                        clearInterval(checkInterval);

                        broker.connection_status = 'connected';
                        this.updateBrokerTable();
                        this.showNotification('✅ Flattrade connected automatically!', 'success');

                        // Close modal if still open
                        this.closeModal();

                        // Update database
                        const token = localStorage.getItem('authToken');
                        await fetch(`/api/broker-setup/${setupId}/update-status`, {
                            method: 'POST',
                            headers: {
                                'X-Auth-Token': token,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ connection_status: 'connected' })
                        });
                    }
                }
            } catch (error) {
                // Continue checking
            }
        }, 10000); // Check every 10 seconds
    }

    /**
     * Check if Flattrade connection was successful by testing actual auth
     */
    async checkFlattradeConnection(setupId) {
        try {
            this.showNotification('🔍 Checking connection status...', 'info');

            const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
            if (!broker) {
                throw new Error('Broker not found');
            }

            // Get the broker's port from the instance name
            const port = broker.instance_name ? broker.instance_name.split('-').pop() : '5096';

            // Test actual authentication by calling the broker's auth API
            const authResponse = await fetch(`http://127.0.0.1:${port}/api/v1/auth`, {
                method: 'GET'
            });

            if (authResponse.ok) {
                const authResult = await authResponse.json();

                if (authResult.status === 'success' || authResult.message === 'success') {
                    // Actually authenticated
                    broker.connection_status = 'connected';
                    this.showNotification('✅ Flattrade connected successfully!', 'success');

                    // Update database status
                    const token = localStorage.getItem('authToken');
                    await fetch(`/api/broker-setup/${setupId}/update-status`, {
                        method: 'POST',
                        headers: {
                            'X-Auth-Token': token,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ connection_status: 'connected' })
                    });
                } else {
                    // Not authenticated
                    broker.connection_status = 'not_connected';
                    this.showNotification('⚠️ Authentication not completed. Please complete OAuth login.', 'warning');
                }
            } else {
                // Auth API failed - not authenticated
                broker.connection_status = 'not_connected';
                this.showNotification('⚠️ Connection not detected. Please complete OAuth login.', 'warning');
            }

            this.updateBrokerTable();

        } catch (error) {
            console.error('❌ Failed to check connection:', error);
            const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
            if (broker) {
                broker.connection_status = 'not_connected';
                this.updateBrokerTable();
            }
            this.showError(`❌ Failed to check connection: ${error.message}`);
        }
    }

    /**
     * Auto connect broker using automation system
     */
    async autoConnectBroker(setupId) {
        console.log('🤖 Auto connecting broker:', setupId);
        this.closeModal();

        // Find the broker in our data
        const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
        if (!broker) {
            this.showError('❌ Broker not found');
            return;
        }

        try {
            // 🔗 SHOW FAST PROGRESS INDICATOR FOR CONNECTION
            const steps = ['🔐 Auth', '🔗 Connecting', '📊 Symbols', '✅ Connected'];
            this.showProgressIndicator(setupId, 'connecting', steps);
            this.showNotification('🤖 Auto connecting broker...', 'info');

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Use the automation system endpoint that we know works
            const apiCall = async () => {
                try {
                    const response = await fetch(`/api/automation/connect-broker/${setupId}`, {
                        method: 'POST',
                        headers: {
                            'X-Auth-Token': token,
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();
                    console.log('📋 Auto Connect API response:', result);

                    if (response.ok && result.success) {
                        const data = result.data || {};
                        const step = data.step;

                        if (step === 'oauth_manual' || data.manual_steps) {
                            // OAuth manual steps required - show improved UX
                            this.clearProgress(setupId);
                            broker.connection_status = 'oauth_pending';
                            this.updateBrokerTable();

                            this.showNotification('🔗 Opening Flattrade OAuth...', 'info');

                            // Show improved OAuth modal with auto-open
                            const oauthUrl = data.oauth_url;
                            const credentials = data.instructions;
                            this.showImprovedFlattradeOAuth(oauthUrl, credentials, setupId);

                        } else if (step === 'oauth_completed' || data.connection_status === 'connected') {
                            // Actually connected
                            this.updateProgress(setupId, 3, '📊 Loading symbols...');

                            setTimeout(() => {
                                this.updateProgress(setupId, 4, '✅ Connected');
                                this.clearProgress(setupId);

                                broker.connection_status = 'connected';
                                this.updateBrokerTable();
                            }, 1000);

                            this.showSuccess('✅ Broker connected automatically!');
                            setTimeout(() => this.loadBrokerData(), 1000);
                        } else {
                            // Unknown state
                            throw new Error('Unknown connection state: ' + step);
                        }
                    } else {
                        throw new Error(result.message || 'Failed to auto connect broker');
                    }
                } catch (apiError) {
                    console.error('❌ API Error auto connecting broker:', apiError);
                    this.clearProgress(setupId);
                    broker.connection_status = 'not_connected';
                    this.updateBrokerTable();
                    this.showError('❌ Failed to auto connect broker: ' + apiError.message);
                }
            };

            // Start API call but don't wait for it
            apiCall();

        } catch (error) {
            console.error('❌ Error auto connecting broker:', error);
            this.clearProgress(setupId);
            broker.connection_status = 'not_connected';
            this.updateBrokerTable();
            this.showError('❌ Failed to auto connect broker: ' + error.message);
        }
    }

    /**
     * Show connect options (manual/auto)
     */
    showConnectOptions(setupId) {
        const modalContent = `
            <div class="space-y-4">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-link text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-slate-100">Connect to Broker</h4>
                        <p class="text-sm text-gray-600 dark:text-slate-400">Choose connection method</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Auto Connect -->
                    <div class="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer"
                         onclick="userDetailsManager.autoConnectBroker(${setupId})">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-magic text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900 dark:text-slate-100">Auto Connect</h5>
                                <p class="text-xs text-gray-600 dark:text-slate-400">Use saved credentials from database</p>
                            </div>
                        </div>
                    </div>

                    <!-- Manual Connect -->
                    <div class="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer"
                         onclick="userDetailsManager.manualConnectBroker(${setupId})">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-hand-paper text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900 dark:text-slate-100">Manual Connect</h5>
                                <p class="text-xs text-gray-600 dark:text-slate-400">Enter credentials manually for authentication</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-info-circle text-yellow-500 mt-0.5 text-sm"></i>
                        <div class="text-xs text-yellow-700 dark:text-yellow-300">
                            <p class="font-medium mb-1">Connection Methods:</p>
                            <p>• <strong>Auto Connect:</strong> Uses saved API credentials automatically</p>
                            <p>• <strong>Manual Connect:</strong> Allows custom connection parameters</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                </div>
            </div>
        `;

        this.showModal('Connect to Broker', modalContent);
    }

    /**
     * Auto connect broker using saved credentials
     */
    async autoConnectBroker(setupId) {
        console.log('🤖 Auto connecting broker:', setupId);
        this.closeModal();

        // Find the broker in our data
        const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
        if (!broker) {
            this.showError('❌ Broker not found');
            return;
        }

        try {
            // 🔗 SHOW FAST PROGRESS INDICATOR FOR CONNECTION
            const steps = ['🔐 Auth', '🔗 Connecting', '📊 Symbols', '✅ Connected'];
            this.showProgressIndicator(setupId, 'connecting', steps);
            this.showNotification('🤖 Auto connecting broker...', 'info');

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Make API call with progress updates
            const apiCall = async () => {
                try {
                    // Step 1: Authenticating (fast)
                    this.updateProgress(setupId, 1, '🔐 Authenticating...');
                    await new Promise(resolve => setTimeout(resolve, 100));

                    const response = await fetch(`/api/broker-setup/${setupId}/connect`, {
                        method: 'POST',
                        headers: {
                            'X-Auth-Token': token,
                            'Content-Type': 'application/json'
                        }
                    });

                    // Step 2: Connecting (fast)
                    this.updateProgress(setupId, 2, '🔗 Connecting to broker...');

                    const result = await response.json();
                    console.log('📋 Auto Connect API response:', result);

                    if (response.ok && result.success) {
                        // Step 3: Loading symbols (fast)
                        this.updateProgress(setupId, 3, '📊 Loading symbols...');
                        await new Promise(resolve => setTimeout(resolve, 200));

                        // Step 4: Complete (fast)
                        this.updateProgress(setupId, 4, '✅ Connected');
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // Clear progress and set final status directly
                        this.clearProgress(setupId);
                        broker.connection_status = 'connected'; // Direct to connected, no intermediate states
                        this.updateBrokerTable();

                        this.showSuccess('✅ Broker connected automatically!');
                        // Refresh data to sync with backend
                        setTimeout(() => this.loadBrokerData(), 1000);
                    } else {
                        throw new Error(result.message || 'Failed to auto connect broker');
                    }
                } catch (apiError) {
                    console.error('❌ API Error auto connecting broker:', apiError);
                    // Clear progress and reset to not_connected state on API error
                    this.clearProgress(setupId);
                    broker.connection_status = 'not_connected';
                    this.updateBrokerTable();
                    this.showError('❌ Failed to auto connect broker: ' + apiError.message);
                }
            };

            // Start API call but don't wait for it
            apiCall();

        } catch (error) {
            console.error('❌ Error auto connecting broker:', error);

            // Reset to original state on error
            broker.connection_status = 'not_connected';
            this.updateBrokerTable();
            this.showError('❌ Failed to auto connect broker: ' + error.message);
        }
    }

    /**
     * Manual connect broker with custom parameters
     */
    async manualConnectBroker(setupId) {
        console.log('👤 Manual connecting broker:', setupId);
        this.closeModal();

        // Find the broker to determine type
        const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
        if (!broker) {
            this.showError('❌ Broker not found');
            return;
        }

        const brokerName = broker.broker_name?.toLowerCase();

        if (brokerName === 'flattrade') {
            // Flattrade: Use OAuth flow
            console.log('🏦 Flattrade manual connect - using OAuth');
            return this.manualConnectFlattrade(setupId);
        } else if (brokerName === 'angel') {
            // Angel: Use OpenAlgo-style form with clientid, pin, totp
            console.log('🏦 Angel manual connect - using OpenAlgo-style form');
            this.showAngelManualConnectForm(setupId);
        } else if (brokerName === 'dhan') {
            // Dhan: Use OpenAlgo-style direct connection (no form needed)
            console.log('🏦 Dhan manual connect - using OpenAlgo-style direct connection');
            this.connectDhanManualOpenAlgoStyle(setupId);
        } else {
            // Other brokers: Use TOTP form
            console.log('🏦 Other broker manual connect - using TOTP form');
            this.showManualConnectForm(setupId);
        }
    }

    /**
     * Show manual connect form
     */
    showManualConnectForm(setupId) {
        const modalContent = `
            <div class="space-y-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-hand-paper text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-slate-100">Manual Broker Authentication</h4>
                        <p class="text-sm text-gray-600 dark:text-slate-400">Enter your broker credentials for authentication only</p>
                    </div>
                </div>

                <form id="manualConnectForm" class="space-y-6">
                    <!-- Info about saved credentials -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-info-circle text-blue-600 dark:text-blue-400"></i>
                            <span class="text-sm font-medium text-blue-800 dark:text-blue-200">Using Saved Credentials</span>
                        </div>
                        <p class="text-xs text-blue-600 dark:text-blue-300 mt-1">
                            Client ID, API Key, Secret, and PIN are already saved. You only need to enter the OTP.
                        </p>
                    </div>

                    <!-- OTP Input - The only field needed -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-2">
                            <i class="fas fa-mobile-alt mr-2"></i>OTP (One-Time Password) <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="manualOtp" required maxlength="6"
                               class="w-full px-4 py-3 text-lg text-center border border-gray-300 dark:border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100 font-mono tracking-widest"
                               placeholder="000000"
                               autocomplete="off">
                        <div class="mt-2 space-y-1">
                            <p class="text-xs text-gray-600 dark:text-slate-400">
                                <i class="fas fa-sms mr-1"></i>Enter the 6-digit OTP sent to your mobile
                            </p>
                            <p class="text-xs text-gray-500 dark:text-slate-500">
                                <i class="fas fa-clock mr-1"></i>OTP is usually valid for 30-60 seconds
                            </p>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="userDetailsManager.closeModal()"
                                class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-link mr-2"></i>Connect Manually
                        </button>
                    </div>
                </form>
            </div>
        `;

        this.showModal('Manual Broker Connection', modalContent);

        // Add form submit handler with proper context binding
        const form = document.getElementById('manualConnectForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                console.log('🔥 Form submitted, calling submitManualConnect');
                console.log('🔍 this context:', this);
                console.log('🔍 submitManualConnect method:', this.submitManualConnect);
                this.submitManualConnect(setupId);
            });
            console.log('✅ Form submit handler added successfully');
        } else {
            console.error('❌ manualConnectForm not found');
        }
    }

    /**
     * Show Angel connection options (Auto vs Manual OpenAlgo-style)
     */
    showAngelConnectionOptions(setupId) {
        const modalContent = `
            <div class="space-y-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-feather-alt text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Connect Angel One</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Choose your connection method</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-4">
                    <!-- Auto Connect -->
                    <div class="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer"
                         onclick="userDetailsManager.autoConnectBroker(${setupId})">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-bolt text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900 dark:text-slate-100">Auto Connect</h5>
                                <p class="text-xs text-gray-600 dark:text-slate-400">Automatic connection using saved credentials</p>
                            </div>
                        </div>
                    </div>

                    <!-- Manual Connect (OpenAlgo Style) -->
                    <div class="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer"
                         onclick="userDetailsManager.showAngelManualConnectForm(${setupId})">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-hand-paper text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900 dark:text-slate-100">Manual Connect (OpenAlgo Style)</h5>
                                <p class="text-xs text-gray-600 dark:text-slate-400">Enter credentials manually using OpenAlgo authentication flow</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mt-0.5"></i>
                        <div class="text-sm text-blue-800 dark:text-blue-300">
                            <p class="font-medium mb-1">Connection Methods</p>
                            <p><strong>Auto Connect:</strong> Uses saved credentials for quick connection</p>
                            <p><strong>Manual Connect:</strong> Follows OpenAlgo instance authentication process</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.showModal('Connect Angel One', modalContent);
    }

    /**
     * Show Dhan connection options (Auto vs Manual OpenAlgo-style)
     */
    showDhanConnectionOptions(setupId) {
        const modalContent = `
            <div class="space-y-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-chart-line text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Connect Dhan</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Choose your connection method</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-4">
                    <!-- Auto Connect -->
                    <div class="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer"
                         onclick="userDetailsManager.autoConnectBroker(${setupId})">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-bolt text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900 dark:text-slate-100">Auto Connect</h5>
                                <p class="text-xs text-gray-600 dark:text-slate-400">Automatic connection using saved credentials</p>
                            </div>
                        </div>
                    </div>

                    <!-- Manual Connect (OpenAlgo Style) -->
                    <div class="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer"
                         onclick="userDetailsManager.connectDhanManualOpenAlgoStyle(${setupId})">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-hand-paper text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900 dark:text-slate-100">Manual Connect (OpenAlgo Style)</h5>
                                <p class="text-xs text-gray-600 dark:text-slate-400">Direct connection using OpenAlgo authentication flow</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mt-0.5"></i>
                        <div class="text-sm text-blue-800 dark:text-blue-300">
                            <p class="font-medium mb-1">Connection Methods</p>
                            <p><strong>Auto Connect:</strong> Uses saved credentials for quick connection</p>
                            <p><strong>Manual Connect:</strong> Follows OpenAlgo instance direct authentication (no OTP needed)</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.showModal('Connect Dhan', modalContent);
    }

    /**
     * Show Angel manual connect form (OpenAlgo style)
     */
    showAngelManualConnectForm(setupId) {
        const modalContent = `
            <div class="space-y-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-feather-alt text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-slate-100">Angel One Manual Connection</h4>
                        <p class="text-sm text-gray-600 dark:text-slate-400">Enter your Angel One credentials following OpenAlgo authentication</p>
                    </div>
                </div>

                <form id="angelManualConnectForm" class="space-y-4">
                    <div>
                        <label for="angelClientId" class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">
                            Client ID
                        </label>
                        <input type="text"
                               id="angelClientId"
                               name="clientid"
                               required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                               placeholder="Enter your Angel One Client ID"
                               pattern="[A-Za-z0-9]+"
                               title="Client ID should only contain letters and numbers">
                        <p class="text-xs text-gray-500 dark:text-slate-400 mt-1">Your Angel One trading account Client ID</p>
                    </div>

                    <div>
                        <label for="angelPin" class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">
                            Trading PIN
                        </label>
                        <input type="password"
                               id="angelPin"
                               name="pin"
                               required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                               placeholder="Enter your trading PIN"
                               minlength="4">
                        <p class="text-xs text-gray-500 dark:text-slate-400 mt-1">Your Angel One trading account PIN</p>
                    </div>

                    <div>
                        <label for="angelTotp" class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">
                            TOTP Code
                        </label>
                        <input type="password"
                               id="angelTotp"
                               name="totp"
                               required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                               placeholder="Enter 6-digit TOTP code"
                               pattern="[0-9]{6}"
                               title="TOTP should be 6 digits"
                               maxlength="6">
                        <p class="text-xs text-gray-500 dark:text-slate-400 mt-1">Enter the 6-digit code from your authenticator app</p>
                    </div>

                    <div class="flex space-x-3 pt-4">
                        <button type="submit"
                                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                            <i class="fas fa-link mr-2"></i>
                            Connect Angel One
                        </button>
                        <button type="button"
                                onclick="userDetailsManager.closeModal()"
                                class="px-4 py-2 border border-gray-300 dark:border-slate-600 text-gray-700 dark:text-slate-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
                            Cancel
                        </button>
                    </div>
                </form>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mt-0.5"></i>
                        <div class="text-sm text-blue-800 dark:text-blue-300">
                            <p class="font-medium mb-1">Security Note</p>
                            <p>This follows the same authentication process as OpenAlgo instances. Your credentials are used only for broker authentication and are not stored.</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.showModal('Angel One Manual Connection', modalContent);

        // Add form submit handler
        const form = document.getElementById('angelManualConnectForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitAngelManualConnect(setupId);
            });
        }
    }

    /**
     * Submit Angel manual connect form
     */
    async submitAngelManualConnect(setupId) {
        console.log('📝 Submitting Angel manual connect for broker:', setupId);

        try {
            // Get form data
            const clientId = document.getElementById('angelClientId')?.value?.trim();
            const pin = document.getElementById('angelPin')?.value?.trim();
            const totp = document.getElementById('angelTotp')?.value?.trim();

            // Validate inputs
            if (!clientId || !pin || !totp) {
                this.showError('❌ All fields are required');
                return;
            }

            if (totp.length !== 6 || !/^\d{6}$/.test(totp)) {
                this.showError('❌ TOTP must be exactly 6 digits');
                return;
            }

            // Show loading state
            const submitBtn = document.querySelector('#angelManualConnectForm button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Connecting...';
            submitBtn.disabled = true;

            console.log('🔐 Performing Angel manual connect for setup:', setupId);

            const response = await fetch(`/api/broker-setup/${setupId}/connect-manual-openalgo`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    clientid: clientId,
                    pin: pin,
                    totp: totp
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.closeModal();
                this.showSuccess('✅ Angel One connected successfully using OpenAlgo style!');
                await this.loadBrokerData(); // Refresh data
            } else {
                throw new Error(result.message || 'Failed to connect Angel One broker');
            }

        } catch (error) {
            console.error('❌ Angel manual connect failed:', error);
            this.showError('Failed to connect: ' + error.message);
        } finally {
            // Reset button state
            const submitBtn = document.querySelector('#angelManualConnectForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-link mr-2"></i>Connect Angel One';
                submitBtn.disabled = false;
            }
        }
    }

    /**
     * Connect Dhan manually using OpenAlgo style (direct connection)
     */
    async connectDhanManualOpenAlgoStyle(setupId) {
        console.log('🏦 Connecting Dhan manually using OpenAlgo style for setup:', setupId);

        try {
            // Show loading notification
            this.showInfo('🔗 Connecting Dhan broker using OpenAlgo style...');

            const response = await fetch(`/api/broker-setup/${setupId}/connect-manual-openalgo`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({}) // Dhan doesn't need additional credentials
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showSuccess('✅ Dhan broker connected successfully using OpenAlgo style!');
                await this.loadBrokerData(); // Refresh data
            } else {
                throw new Error(result.message || 'Failed to connect Dhan broker');
            }

        } catch (error) {
            console.error('❌ Dhan manual connect failed:', error);
            this.showError('Failed to connect Dhan: ' + error.message);
        }
    }

    /**
     * Submit manual connect form - Only OTP needed
     */
    async submitManualConnect(setupId) {
        console.log('📝 Submitting manual OTP for broker:', setupId);

        // Debug: Check what elements exist
        console.log('🔍 Looking for OTP elements...');
        const manualOtpElement1 = document.getElementById('manualOtp');
        const manualOtpElement2 = document.getElementById('manualTotp');
        console.log('manualOtp element:', manualOtpElement1);
        console.log('manualTotp element:', manualOtpElement2);

        // Get OTP from form with error checking (try both possible IDs)
        let manualOtpElement = manualOtpElement1 || manualOtpElement2;
        if (!manualOtpElement) {
            console.error('❌ manualOtp/manualTotp element not found in DOM');
            this.showError('❌ Form error - please try again');
            return;
        }
        const manualOtp = manualOtpElement.value.trim();
        console.log('📱 OTP value:', manualOtp);

        // Validate OTP
        if (!manualOtp) {
            this.showError('❌ Please enter the OTP');
            return;
        }

        if (manualOtp.length !== 6 || !/^\d{6}$/.test(manualOtp)) {
            this.showError('❌ OTP must be exactly 6 digits');
            return;
        }

        // Find the broker in our data
        const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
        if (!broker) {
            this.showError('❌ Broker not found');
            return;
        }

        try {
            this.closeModal();

            // 🚀 SHOW PROGRESS INDICATOR FOR MANUAL CONNECTION
            const steps = ['👤 Registering', '🔐 Authenticating', '📊 Symbols', '🔑 API Keys', '✅ Connected'];
            this.showProgressIndicator(setupId, 'manual_connecting', steps);
            this.showNotification('👤 Manual connection in progress...', 'info');

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Prepare manual OTP data (only OTP - use saved credentials)
            const authData = {
                manual_otp: manualOtp
            };

            // Make API call with progress updates
            const apiCall = async () => {
                try {
                    // Step 1: Registration (for new instances)
                    this.updateProgress(setupId, 1, '👤 Registering user...');
                    await new Promise(resolve => setTimeout(resolve, 200));

                    // Step 2: Authentication
                    this.updateProgress(setupId, 2, '🔐 Authenticating with OTP...');

                    const response = await fetch(`/api/broker-setup/${setupId}/connect-manual`, {
                        method: 'POST',
                        headers: {
                            'X-Auth-Token': token,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(authData)
                    });

                    const result = await response.json();
                    console.log('📋 Manual Auth API response:', result);

                    if (response.ok) {
                        // Step 3: Symbols download
                        this.updateProgress(setupId, 3, '📊 Downloading symbols...');
                        await new Promise(resolve => setTimeout(resolve, 300));

                        // Step 4: API keys
                        this.updateProgress(setupId, 4, '🔑 Generating API keys...');
                        await new Promise(resolve => setTimeout(resolve, 200));

                        // Step 5: Complete
                        this.updateProgress(setupId, 5, '✅ Connected');
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // Clear progress and set final status
                        this.clearProgress(setupId);
                        broker.connection_status = 'connected';
                        this.updateBrokerTable();

                        this.showSuccess('✅ Manual connection completed successfully!');
                        // Refresh data to sync with backend
                        setTimeout(() => this.loadBrokerData(), 1000);
                    } else {
                        // Error response - use 'detail' field from FastAPI
                        throw new Error(result.detail || result.message || 'Failed to authenticate broker manually');
                    }
                } catch (apiError) {
                    console.error('❌ API Error manual auth broker:', apiError);
                    // Clear progress and reset to not_connected state on API error
                    this.clearProgress(setupId);
                    broker.connection_status = 'not_connected';
                    this.updateBrokerTable();
                    this.showError('❌ Failed to authenticate broker manually: ' + apiError.message);
                }
            };

            // Start API call but don't wait for it
            apiCall();

        } catch (error) {
            console.error('❌ Error manual auth broker:', error);

            // Clear progress and reset to original state on error
            this.clearProgress(setupId);
            broker.connection_status = 'not_connected';
            this.updateBrokerTable();
            this.showError('❌ Failed to authenticate broker manually: ' + error.message);
        }
    }

    // REMOVED DUPLICATE submitManualConnect FUNCTION
    // The correct OTP-only version is above

    /**
     * Disconnect broker
     */
    async disconnectBroker(setupId) {
        console.log('🔌 Disconnecting broker:', setupId);

        // Find the broker in our data
        const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
        if (!broker) {
            this.showError('❌ Broker not found');
            return;
        }

        try {
            // 🔌 SHOW PROGRESS INDICATOR FOR DISCONNECT (LOGOUT)
            const steps = ['🔌 Logging out', '🔐 Revoking tokens', '✅ Disconnected'];
            this.showProgressIndicator(setupId, 'disconnecting', steps);
            this.showNotification('🔌 Logging out from broker...', 'info');

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Make API call to disconnect (logout) with progress
            const apiCall = async () => {
                try {
                    // Step 1: Logging out
                    this.updateProgress(setupId, 1, '🔌 Logging out from broker...');
                    await new Promise(resolve => setTimeout(resolve, 100));

                    const response = await fetch(`/api/broker-setup/${setupId}/disconnect`, {
                        method: 'POST',
                        headers: {
                            'X-Auth-Token': token,
                            'Content-Type': 'application/json'
                        }
                    });

                    // Step 2: Revoking tokens
                    this.updateProgress(setupId, 2, '🔐 Revoking auth tokens...');

                    const result = await response.json();
                    console.log('📋 Disconnect API response:', result);

                    if (response.ok && result.success) {
                        // Step 3: Complete
                        this.updateProgress(setupId, 3, '✅ Disconnected');
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // Clear progress and update status
                        this.clearProgress(setupId);
                        broker.connection_status = 'disconnected'; // Keep as disconnected, not not_connected
                        broker.setup_status = 'running'; // Algo still running
                        this.updateBrokerTable();

                        this.showSuccess('✅ Broker logged out successfully! Algo service still running.');
                        // Refresh data to sync with backend
                        setTimeout(() => this.loadBrokerData(), 1000);
                    } else {
                        throw new Error(result.message || 'Failed to disconnect broker');
                    }
                } catch (apiError) {
                    console.error('❌ API Error disconnecting broker:', apiError);
                    // Clear progress and reset to connected state on API error
                    this.clearProgress(setupId);
                    broker.connection_status = 'connected';
                    this.updateBrokerTable();
                    this.showError('❌ Failed to disconnect broker: ' + apiError.message);
                }
            };

            // Start API call but don't wait for it
            apiCall();

        } catch (error) {
            console.error('❌ Error disconnecting broker:', error);

            // Reset to original state on error
            broker.connection_status = 'connected';
            this.updateBrokerTable();
            this.showError('❌ Failed to disconnect broker: ' + error.message);
        }
    }

    /**
     * Edit broker .env file (individual broker instance) - Only API Key and Secret
     */
    async editBrokerEnv(setupId) {
        try {
            console.log('⚙️ Editing API credentials for setup ID:', setupId);

            // Get current broker data
            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            const response = await fetch(`/api/broker-setup/${setupId}`, {
                headers: {
                    'X-Auth-Token': token
                }
            });
            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error('Failed to get broker data');
            }

            const brokerData = result.data;
            console.log('📄 Broker data loaded for editing:', brokerData.instance_name);
            this.showEditApiCredentialsModal(setupId, brokerData);

        } catch (error) {
            console.error('❌ Error loading broker data:', error);
            this.showError('Failed to load broker data: ' + error.message);
        }
    }

    /**
     * Show edit API credentials modal (only API key and secret)
     */
    showEditApiCredentialsModal(setupId, brokerData) {
        const modalContent = `
            <div class="space-y-4">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-key text-purple-600 dark:text-purple-400 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-slate-100">Edit API Credentials</h4>
                        <p class="text-sm text-gray-600 dark:text-slate-400">Instance: ${brokerData.instance_name}</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">API Key</label>
                        <input type="text" id="editApiKey" value="${brokerData.broker_api_key || ''}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-slate-700 dark:text-slate-100"
                               placeholder="Enter API Key">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">API Secret</label>
                        <input type="password" id="editApiSecret" value="${brokerData.broker_api_secret || ''}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-slate-700 dark:text-slate-100"
                               placeholder="Enter API Secret">
                        <div class="flex items-center mt-1">
                            <input type="checkbox" id="showApiSecret" class="mr-2" onchange="userDetailsManager.togglePasswordVisibility('editApiSecret', this.checked)">
                            <label for="showApiSecret" class="text-xs text-gray-600 dark:text-slate-400">Show API Secret</label>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-info-circle text-blue-500 mt-0.5 text-sm"></i>
                        <div class="text-xs text-blue-700 dark:text-blue-300">
                            <p class="font-medium mb-1">Important Notes:</p>
                            <ul class="list-disc list-inside space-y-0.5">
                                <li>Only API Key and API Secret can be edited</li>
                                <li>Changes will update the .env file in the broker instance</li>
                                <li>Restart the broker service after making changes</li>
                                <li>Reconnect to broker after restart for changes to take effect</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">
                        Cancel
                    </button>
                    <button onclick="userDetailsManager.saveApiCredentials(${setupId})" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                </div>
            </div>
        `;

        this.showModal('Edit API Credentials', modalContent);
    }

    /**
     * Toggle password visibility
     */
    togglePasswordVisibility(inputId, show) {
        const input = document.getElementById(inputId);
        if (input) {
            input.type = show ? 'text' : 'password';
        }
    }

    /**
     * Save API credentials to .env file
     */
    async saveApiCredentials(setupId) {
        try {
            const apiKey = document.getElementById('editApiKey').value.trim();
            const apiSecret = document.getElementById('editApiSecret').value.trim();

            if (!apiKey || !apiSecret) {
                this.showError('Please enter both API Key and API Secret');
                return;
            }

            console.log('💾 Saving API credentials for setup:', setupId);

            // Log audit action
            await this.logAuditAction('BROKER_CREDENTIALS_UPDATE_ATTEMPT', {
                setup_id: setupId,
                action: 'Update API credentials in .env file',
                timestamp: new Date().toISOString()
            });

            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            const updateData = {
                api_key: apiKey,
                api_secret: apiSecret
            };

            const response = await fetch(`/api/broker-setup/${setupId}/update-credentials`, {
                method: 'POST',
                headers: {
                    'X-Auth-Token': token,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                // Log successful update
                await this.logAuditAction('BROKER_CREDENTIALS_UPDATE_SUCCESS', {
                    setup_id: setupId,
                    action: 'API credentials updated successfully',
                    timestamp: new Date().toISOString()
                });

                this.closeModal();
                this.showSuccess('✅ API credentials updated successfully! Restart the broker service for changes to take effect.');

                // Refresh broker data
                await this.loadBrokerData();
            } else {
                // Log failed update
                await this.logAuditAction('BROKER_CREDENTIALS_UPDATE_FAILED', {
                    setup_id: setupId,
                    error: result.message || 'Unknown error',
                    action: 'Failed to update API credentials',
                    timestamp: new Date().toISOString()
                });

                throw new Error(result.message || 'Failed to update API credentials');
            }
        } catch (error) {
            console.error('❌ Error saving API credentials:', error);

            // Log error
            await this.logAuditAction('BROKER_CREDENTIALS_UPDATE_ERROR', {
                setup_id: setupId,
                error: error.message,
                action: 'Error updating API credentials',
                timestamp: new Date().toISOString()
            });

            this.showError('❌ Failed to save API credentials: ' + error.message);
        }
    }

    /**
     * Show edit .env file modal
     */
    showEditEnvModal(setupId, instanceName, envContent) {
        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Edit .env File</h4>
                <p class="text-sm text-gray-600 dark:text-slate-400">Edit environment variables for: <strong>${instanceName}</strong></p>

                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-2">Environment Variables</label>
                        <textarea id="envFileContent" rows="15" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100 font-mono text-sm" placeholder="KEY=value">${envContent}</textarea>
                    </div>

                    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-exclamation-triangle text-yellow-500 mt-0.5 text-sm"></i>
                            <div class="text-xs text-yellow-700 dark:text-yellow-300">
                                <p class="font-medium mb-1">Important:</p>
                                <ul class="list-disc list-inside space-y-0.5">
                                    <li>Changes will be applied to the .env file of this broker instance</li>
                                    <li>Restart the broker service for changes to take effect</li>
                                    <li>Be careful with sensitive information like API keys and secrets</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.saveEnvFile(${setupId})" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg">
                        <i class="fas fa-save mr-2"></i>Save .env File
                    </button>
                </div>
            </div>
        `;

        this.showModal('Edit .env File', modalContent);
    }

    /**
     * View Audit Logs - Show system activity tracking
     */
    async viewAuditLogs() {
        try {
            console.log('📋 Loading audit logs...');

            const response = await fetch(`/api/audit-logs?user_id=${this.userId}&admin_id=${this.adminId}&limit=50`);
            const result = await response.json();

            if (response.ok && result.success) {
                const logs = result.data || [];
                this.showAuditLogsModal(logs);
            } else {
                this.showError('Failed to load audit logs');
            }
        } catch (error) {
            console.error('❌ Error loading audit logs:', error);
            this.showError('Error loading audit logs: ' + error.message);
        }
    }

    /**
     * Show audit logs modal
     */
    showAuditLogsModal(logs) {
        const logsHtml = logs.map(log => {
            const details = log.details || {};
            const timestamp = new Date(log.created_at).toLocaleString();

            // Format action description
            let actionDesc = '';
            if (log.action === 'create') {
                actionDesc = `<span class="text-green-600">Created</span>`;
            } else if (log.action === 'update') {
                actionDesc = `<span class="text-blue-600">Updated</span>`;
            } else if (log.action === 'delete') {
                actionDesc = `<span class="text-red-600">Deleted</span>`;
            } else {
                actionDesc = `<span class="text-gray-600">${log.action}</span>`;
            }

            // Format user role badge
            let roleBadge = '';
            if (log.user_role === 'superadmin') {
                roleBadge = '<span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">SuperAdmin</span>';
            } else if (log.user_role === 'admin') {
                roleBadge = '<span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Admin</span>';
            } else {
                roleBadge = '<span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">User</span>';
            }

            return `
                <div class="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
                    <div class="flex justify-between items-start mb-2">
                        <div class="flex items-center space-x-2">
                            ${roleBadge}
                            <span class="font-medium text-gray-900 dark:text-slate-100">${log.user_id}</span>
                            ${actionDesc}
                            <span class="text-gray-600 dark:text-slate-400">${log.resource_type}</span>
                            ${log.resource_id ? `<span class="text-gray-500 dark:text-slate-500">#${log.resource_id}</span>` : ''}
                        </div>
                        <span class="text-xs text-gray-500 dark:text-slate-500">${timestamp}</span>
                    </div>

                    ${log.target_user_id && log.target_user_id !== log.user_id ?
                        `<div class="text-sm text-gray-600 dark:text-slate-400 mb-2">
                            Target: <span class="font-medium">${log.target_user_id}</span>
                        </div>` : ''
                    }

                    ${details.original_details && Object.keys(details.original_details).length > 0 ?
                        `<div class="text-xs text-gray-500 dark:text-slate-500 bg-gray-50 dark:bg-slate-800 rounded p-2 mt-2">
                            <strong>Details:</strong> ${JSON.stringify(details.original_details, null, 2)}
                        </div>` : ''
                    }

                    ${log.ip_address ?
                        `<div class="text-xs text-gray-400 dark:text-slate-600 mt-1">
                            IP: ${log.ip_address}
                        </div>` : ''
                    }
                </div>
            `;
        }).join('');

        const modalContent = `
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <h4 class="font-medium text-gray-900 dark:text-slate-100">System Activity Audit Logs</h4>
                    <span class="text-sm text-gray-500 dark:text-slate-400">${logs.length} entries</span>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-info-circle text-blue-500 mt-0.5 text-sm"></i>
                        <div class="text-xs text-blue-700 dark:text-blue-300">
                            <p class="font-medium mb-1">Audit Trail:</p>
                            <p>This shows all system activities including who created, updated, or deleted what. All actions are tracked for security and compliance.</p>
                        </div>
                    </div>
                </div>

                <div class="space-y-3 max-h-96 overflow-y-auto">
                    ${logs.length > 0 ? logsHtml :
                        '<div class="text-center text-gray-500 dark:text-slate-400 py-8">No audit logs found</div>'
                    }
                </div>

                <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Close</button>
                </div>
            </div>
        `;

        this.showModal('System Activity Audit Logs', modalContent);
    }

    /**
     * Save .env file changes
     */
    async saveEnvFile(setupId) {
        const envContent = document.getElementById('envFileContent').value;

        try {
            console.log('💾 Saving .env file for setup ID:', setupId);

            const response = await fetch(`/api/broker-setup/${setupId}/env`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    env_content: envContent
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.closeModal();
                this.showSuccess('.env file updated successfully! Restart the broker for changes to take effect.');
            } else {
                throw new Error(result.message || 'Failed to save .env file');
            }

        } catch (error) {
            console.error('❌ Error saving .env file:', error);
            this.showError('Failed to save .env file: ' + error.message);
        }
    }

    /**
     * Show modal
     */
    showModal(title, content, maxWidthOrButtons = 'max-w-md') {
        // Check if third parameter is buttons array or maxWidth string
        const isButtonsArray = Array.isArray(maxWidthOrButtons);
        const maxWidth = isButtonsArray ? 'max-w-4xl' : maxWidthOrButtons;
        const buttons = isButtonsArray ? maxWidthOrButtons : [];

        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

        // Create buttons HTML
        let buttonsHtml = '';
        if (buttons.length > 0) {
            buttonsHtml = `
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-slate-600">
                    ${buttons.map((button, index) => `
                        <button id="modalBtn${index}" class="px-4 py-2 rounded-lg font-medium ${button.class === 'btn-primary' ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-slate-600 dark:hover:bg-slate-500 dark:text-slate-200'}">
                            ${button.text}
                        </button>
                    `).join('')}
                </div>
            `;
        }

        modal.innerHTML = `
            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl ${maxWidth} w-full mx-4 max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-slate-100">${title}</h3>
                        <button id="modalCloseBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-slate-300">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div>${content}</div>
                    ${buttonsHtml}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Add event listeners for buttons
        const closeBtn = modal.querySelector('#modalCloseBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.remove();
            });
        }

        // Add event listeners for custom buttons
        buttons.forEach((button, index) => {
            const btn = modal.querySelector(`#modalBtn${index}`);
            if (btn && button.action) {
                btn.addEventListener('click', button.action);
            }
        });

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * Close modal
     */
    closeModal() {
        try {
            // Remove all modal overlays
            const modals = document.querySelectorAll('.fixed.inset-0');
            modals.forEach(modal => {
                // Add fade out animation
                modal.style.opacity = '0';
                modal.style.transition = 'opacity 0.2s ease-out';

                // Remove after animation
                setTimeout(() => {
                    if (modal.parentNode) {
                        modal.remove();
                    }
                }, 200);
            });

            // Also remove any modal backdrops or overlays that might be lingering
            const backdrops = document.querySelectorAll('[class*="modal"], [class*="overlay"]');
            backdrops.forEach(backdrop => {
                if (backdrop.classList.contains('fixed') || backdrop.classList.contains('absolute')) {
                    backdrop.remove();
                }
            });

        } catch (error) {
            console.warn('⚠️ Error closing modal:', error);
            // Fallback: force remove all fixed positioned elements that look like modals
            const allFixed = document.querySelectorAll('.fixed');
            allFixed.forEach(el => {
                if (el.style.zIndex > 40 || el.classList.contains('inset-0')) {
                    el.remove();
                }
            });
        }
    }

    /**
     * Set button to loading state with custom message
     */
    setButtonLoading(setupId, state, message) {
        try {
            console.log('🔄 Setting button loading for setup:', setupId, 'state:', state, 'message:', message);

            // Find the broker in our data
            const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
            if (!broker) {
                console.warn('⚠️ Broker not found for button loading:', setupId);
                return;
            }

            // Set loading state
            broker.loading_state = state;
            broker.loading_message = message;

            // Re-render the table to show loading buttons
            this.updateBrokerTable();

        } catch (error) {
            console.error('❌ Error setting button loading:', error);
        }
    }

    /**
     * Clear button loading state
     */
    clearButtonLoading(setupId) {
        try {
            const broker = this.brokers.find(b => b.id === setupId || b.setup_id === setupId);
            if (broker) {
                delete broker.loading_state;
                delete broker.loading_message;
                this.updateBrokerTable();
            }
        } catch (error) {
            console.error('❌ Error clearing button loading:', error);
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification-toast');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification
        const notification = document.createElement('div');
        notification.className = `notification-toast fixed top-4 right-4 z-50 max-w-sm w-full transform transition-all duration-300 translate-x-full`;

        const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        const icon = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';

        notification.innerHTML = `
            <div class="${bgColor} text-white p-4 rounded-lg shadow-lg">
                <div class="flex items-center">
                    <i class="fas ${icon} mr-3"></i>
                    <p class="flex-1">${message}</p>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 5000);
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showNotification(message, 'error');
    }

    /**
     * Show manual connect modal
     */
    showManualConnectModal(setupId) {
        const broker = this.brokers.find(b => b.setup_id === setupId || b.id === setupId);
        const brokerName = broker ? broker.broker_name : 'Unknown';

        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Connect to ${brokerName}</h4>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Client ID</label>
                        <input type="text" id="manualClientId" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100" placeholder="Enter Client ID">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Trading PIN</label>
                        <input type="password" id="manualTradingPin" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100" placeholder="Enter Trading PIN">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">TOTP Code</label>
                        <input type="text" id="manualTotp" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100" placeholder="Enter 6-digit TOTP" maxlength="6">
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.performManualConnect(${setupId})" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">Connect</button>
                </div>
            </div>
        `;

        this.showModal('Manual Connect', modalContent);
    }

    /**
     * Perform manual connect
     */
    async performManualConnect(setupId) {
        const clientId = document.getElementById('manualClientId').value;
        const tradingPin = document.getElementById('manualTradingPin').value;
        const totp = document.getElementById('manualTotp').value;

        if (!clientId || !tradingPin || !totp) {
            this.showError('Please fill in all fields');
            return;
        }

        if (totp.length !== 6) {
            this.showError('TOTP must be 6 digits');
            return;
        }

        try {
            console.log('🔐 Performing manual connect for setup:', setupId);

            const response = await fetch(`/api/broker-setup/${setupId}/connect-manual`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: clientId,
                    password: tradingPin,
                    totp: totp
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.closeModal();
                this.showSuccess('Broker connected successfully!');
                await this.loadBrokerData(); // Refresh data
            } else {
                throw new Error(result.message || 'Failed to connect broker');
            }
        } catch (error) {
            console.error('❌ Manual connect failed:', error);
            this.showError('Failed to connect: ' + error.message);
        }
    }

    /**
     * Show edit broker modal
     */
    showEditBrokerModal(broker) {
        const modalContent = `
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-slate-100">Edit Broker Configuration</h4>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Setup Name</label>
                        <input type="text" id="editSetupName" value="${broker.trading_setup_name || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Client ID</label>
                        <input type="text" id="editClientId" value="${broker.client_id || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Broker</label>
                        <input type="text" id="editBrokerName" value="${broker.broker_name || ''}" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100" readonly>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">Cancel</button>
                    <button onclick="userDetailsManager.updateBroker(${broker.id})" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">Update</button>
                </div>
            </div>
        `;

        this.showModal('Edit Broker', modalContent);
    }

    /**
     * Update broker
     */
    async updateBroker(brokerId) {
        const setupName = document.getElementById('editSetupName').value;
        const clientId = document.getElementById('editClientId').value;

        if (!setupName || !clientId) {
            this.showError('Please fill in all required fields');
            return;
        }

        try {
            console.log('📝 Updating broker:', brokerId);

            // This would need to be implemented in the API
            this.showSuccess('Broker updated successfully!');
            this.closeModal();
            await this.loadBrokerData(); // Refresh data

        } catch (error) {
            console.error('❌ Error updating broker:', error);
            this.showError('Failed to update broker: ' + error.message);
        }
    }

    /**
     * Copy broker configuration for easy duplication with minor changes
     */
    async copyBroker(brokerId) {
        try {
            console.log(`📋 Copying broker configuration ${brokerId}`);
            console.log('🔍 Copy function called successfully!');

            // Find the broker in saved brokers list
            const broker = this.savedBrokers.find(b => b.id === brokerId);
            if (!broker) {
                this.showError('Broker configuration not found');
                return;
            }

            this.showCopyBrokerModal(broker);
        } catch (error) {
            console.error('Error copying broker:', error);
            this.showError('Error copying broker configuration');
        }
    }

    /**
     * Show copy broker modal with pre-filled data
     */
    showCopyBrokerModal(originalBroker) {
        const modalContent = `
            <div class="space-y-4">
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-copy text-blue-500 mr-2"></i>
                        <span class="text-sm text-blue-800 dark:text-blue-300">
                            Copying from: <strong>${originalBroker.trading_setup_name || 'Unnamed'}</strong> (${originalBroker.broker_name})
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Broker *</label>
                        <select id="copyBrokerType" class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100">
                            <optgroup label="Popular Brokers">
                                <option value="angel" ${originalBroker.broker_name === 'angel' ? 'selected' : ''}>Angel One</option>
                                <option value="zerodha" ${originalBroker.broker_name === 'zerodha' ? 'selected' : ''}>Zerodha</option>
                                <option value="upstox" ${originalBroker.broker_name === 'upstox' ? 'selected' : ''}>Upstox</option>
                                <option value="fyers" ${originalBroker.broker_name === 'fyers' ? 'selected' : ''}>Fyers</option>
                                <option value="aliceblue" ${originalBroker.broker_name === 'aliceblue' ? 'selected' : ''}>Alice Blue</option>
                                <option value="iifl" ${originalBroker.broker_name === 'iifl' ? 'selected' : ''}>IIFL</option>
                                <option value="kotak" ${originalBroker.broker_name === 'kotak' ? 'selected' : ''}>Kotak Securities</option>
                                <option value="dhan" ${originalBroker.broker_name === 'dhan' ? 'selected' : ''}>Dhan</option>
                            </optgroup>
                            <optgroup label="XTS Brokers">
                                <option value="fivepaisaxts" ${originalBroker.broker_name === 'fivepaisaxts' ? 'selected' : ''}>5Paisa XTS</option>
                                <option value="compositedge" ${originalBroker.broker_name === 'compositedge' ? 'selected' : ''}>Composite Edge</option>
                                <option value="tradejini" ${originalBroker.broker_name === 'tradejini' ? 'selected' : ''}>Trade Jini</option>
                                <option value="wisdom" ${originalBroker.broker_name === 'wisdom' ? 'selected' : ''}>Wisdom Capital</option>
                            </optgroup>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Setup Name *</label>
                        <input type="text" id="copySetupName" value="${originalBroker.trading_setup_name || ''}_copy"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100"
                               placeholder="Enter setup name">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Client ID *</label>
                        <input type="text" id="copyClientId" value="${originalBroker.client_id || ''}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100"
                               placeholder="Enter client ID">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">API Key *</label>
                        <input type="text" id="copyApiKey" value="${originalBroker.api_key || ''}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100"
                               placeholder="Enter API key">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">API Secret *</label>
                        <input type="password" id="copyApiSecret" value=""
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100"
                               placeholder="Enter new API secret">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">Trading PIN *</label>
                        <input type="password" id="copyTradingPin" value=""
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100"
                               placeholder="Enter new trading PIN">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">TOTP Secret</label>
                        <input type="text" id="copyTotpSecret" value="${originalBroker.totp_secret || ''}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-slate-100"
                               placeholder="Enter TOTP secret (optional)">
                    </div>
                    <div class="col-span-2">
                        <label class="flex items-center">
                            <input type="checkbox" id="copyAsTemplate" class="mr-2">
                            <span class="text-sm text-gray-700 dark:text-slate-300">Save as template for future use</span>
                        </label>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-slate-600">
                    <button onclick="userDetailsManager.closeModal()" class="px-4 py-2 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-200">
                        Cancel
                    </button>
                    <button onclick="userDetailsManager.saveCopiedBroker()" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                        <i class="fas fa-copy mr-2"></i>Create Copy
                    </button>
                </div>
            </div>
        `;

        this.showModal('Copy Broker Configuration', modalContent);
    }

    /**
     * Save copied broker configuration
     */
    async saveCopiedBroker() {
        const brokerName = document.getElementById('copyBrokerType').value;
        const setupName = document.getElementById('copySetupName').value;
        const clientId = document.getElementById('copyClientId').value;
        const apiKey = document.getElementById('copyApiKey').value;
        const apiSecret = document.getElementById('copyApiSecret').value;
        const tradingPin = document.getElementById('copyTradingPin').value;
        const totpSecret = document.getElementById('copyTotpSecret').value;
        const isTemplate = document.getElementById('copyAsTemplate').checked;

        if (!brokerName || !setupName || !clientId || !apiKey || !apiSecret || !tradingPin) {
            this.showError('Please fill in all required fields');
            return;
        }

        try {
            console.log(`💾 Saving copied broker configuration...`);

            const token = localStorage.getItem('authToken');

            const brokerData = {
                user_id: this.userId,
                admin_id: this.adminId,
                broker_name: brokerName.toLowerCase(),
                broker_type: 'REST',
                trading_setup_name: setupName,
                client_id: clientId,
                api_key: apiKey,
                api_secret: apiSecret,
                trading_pin: tradingPin,
                totp_secret: totpSecret || '',
                is_template: isTemplate ? 1 : 0
            };

            const response = await fetch('/api/saved-brokers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Auth-Token': token
                },
                body: JSON.stringify(brokerData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.closeModal();
                this.showSuccess(`✅ Broker configuration copied successfully as "${setupName}"!`);
                await this.loadBrokerData();
                await this.loadSavedBrokers();
            } else {
                throw new Error(result.message || 'Failed to save copied broker');
            }
        } catch (error) {
            console.error('Error saving copied broker:', error);
            this.showError('Error saving copied broker configuration');
        }
    }

    /**
     * View broker dashboard with funds and trading data
     */
    viewBrokerDashboard(brokerId) {
        console.log('📊 Opening broker dashboard for:', brokerId);

        const broker = this.brokers.find(b => b.id === brokerId || b.setup_id === brokerId);
        if (!broker) {
            this.showError('❌ Broker not found');
            return;
        }

        this.showBrokerDashboard(broker);
    }

    /**
     * Show broker dashboard modal with funds data
     */
    async showBrokerDashboard(broker) {
        try {
            console.log('📊 Loading dashboard for broker:', broker.broker_name);

            // Show loading modal first
            this.showModal('Broker Dashboard', `
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-3 text-gray-600 dark:text-gray-400">Loading dashboard data...</span>
                </div>
            `, []);

            // Get auth token
            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            // Fetch funds data from the broker
            const setupId = broker.id || broker.setup_id;

            // Fetch multiple data sources in parallel
            const [fundsResponse, positionsResponse, holdingsResponse] = await Promise.allSettled([
                fetch(`/api/broker/${setupId}/funds`, {
                    method: 'GET',
                    headers: { 'X-Auth-Token': token }
                }),
                fetch(`/api/broker/${setupId}/positions`, {
                    method: 'GET',
                    headers: { 'X-Auth-Token': token }
                }),
                fetch(`/api/broker/${setupId}/holdings`, {
                    method: 'GET',
                    headers: { 'X-Auth-Token': token }
                })
            ]);

            const response = fundsResponse.value;

            let fundsData;

            if (!response.ok) {
                // Handle different error cases
                if (response.status === 401) {
                    throw new Error('Authentication required. Please login again.');
                } else if (response.status === 404) {
                    throw new Error('Broker not found or not configured.');
                } else {
                    // Try to get error details from response
                    try {
                        const errorData = await response.json();
                        if (errorData.detail === 'Broker not connected') {
                            // Show dashboard with connection status instead of error
                            fundsData = this.createMockFundsData('not_connected');
                        } else if (errorData.message && errorData.message.includes && errorData.message.includes('apikey')) {
                            // API key missing - broker needs registration
                            fundsData = this.createMockFundsData('registration_required');
                        } else if (errorData.detail && errorData.detail.includes && errorData.detail.includes('apikey')) {
                            // API key missing - broker needs registration
                            fundsData = this.createMockFundsData('registration_required');
                        } else {
                            throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}`);
                        }
                    } catch (parseError) {
                        throw new Error(`Failed to fetch funds data: HTTP ${response.status}`);
                    }
                }
            } else {
                fundsData = await response.json();
            }

            console.log('💰 Funds data received:', fundsData);

            // Process additional data sources
            let positionsData = null;
            let holdingsData = null;

            if (positionsResponse.status === 'fulfilled' && positionsResponse.value.ok) {
                try {
                    positionsData = await positionsResponse.value.json();
                    console.log('📊 Positions data received:', positionsData);
                } catch (e) {
                    console.warn('⚠️ Failed to parse positions data:', e);
                }
            }

            if (holdingsResponse.status === 'fulfilled' && holdingsResponse.value.ok) {
                try {
                    holdingsData = await holdingsResponse.value.json();
                    console.log('📈 Holdings data received:', holdingsData);
                } catch (e) {
                    console.warn('⚠️ Failed to parse holdings data:', e);
                }
            }

            // Create dashboard content with all data
            const dashboardContent = this.createDashboardContent(broker, fundsData, positionsData, holdingsData);

            // Update modal with dashboard
            this.showModal(`${broker.broker_name} Dashboard`, dashboardContent, [
                {
                    text: 'Refresh Data',
                    class: 'btn-primary',
                    action: () => {
                        // Close current modal first, then reload
                        this.closeModal();
                        setTimeout(() => {
                            this.showBrokerDashboard(broker);
                        }, 100);
                    }
                },
                {
                    text: 'Close',
                    class: 'btn-secondary',
                    action: () => {
                        // Immediate close without any delays or animations
                        const modals = document.querySelectorAll('.fixed.inset-0');
                        modals.forEach(modal => modal.remove());
                    }
                }
            ]);

        } catch (error) {
            console.error('❌ Error loading broker dashboard:', error);
            this.showModal('Dashboard Error', `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Failed to Load Dashboard</h3>
                    <p class="text-gray-600 dark:text-gray-400">${error.message}</p>
                </div>
            `, [
                {
                    text: 'Close',
                    class: 'btn-secondary',
                    action: () => {
                        this.closeModal();
                    }
                }
            ]);
        }
    }

    /**
     * Create dashboard content HTML with funds, positions, and holdings data
     */
    createDashboardContent(broker, fundsData, positionsData = null, holdingsData = null) {
        const brokerName = broker.broker_name || 'Unknown';
        const clientId = broker.broker_client_id || broker.client_id || 'N/A';
        const connectionStatus = broker.connection_status || 'unknown';

        // Format currency values
        const formatCurrency = (value) => {
            if (value === null || value === undefined || isNaN(value)) return '₹0.00';
            return `₹${parseFloat(value).toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
        };

        // Format percentage
        const formatPercentage = (value) => {
            if (value === null || value === undefined || isNaN(value)) return '0.00%';
            return `${parseFloat(value).toFixed(2)}%`;
        };

        // Get status color
        const getStatusColor = (status) => {
            switch (status) {
                case 'connected': return 'green';
                case 'connecting': return 'yellow';
                case 'not_connected': return 'red';
                default: return 'gray';
            }
        };

        const statusColor = getStatusColor(connectionStatus);

        // Extract funds data
        const totalFunds = fundsData.total_funds || 0;
        const availableCash = fundsData.available_cash || 0;
        const usedMargin = fundsData.used_margin || 0;
        const netPnl = fundsData.net_pnl || 0;
        const realizedPnl = fundsData.realized_pnl || 0;
        const unrealizedPnl = fundsData.unrealized_pnl || 0;

        // Check if broker is not connected or needs registration
        const isNotConnected = fundsData.status === 'not_connected' || connectionStatus !== 'connected';
        const needsRegistration = fundsData.status === 'registration_required';

        return `
            <div class="space-y-6">
                ${isNotConnected || needsRegistration ? `
                <!-- Connection/Registration Warning -->
                <div class="bg-${needsRegistration ? 'red' : 'yellow'}-50 dark:bg-${needsRegistration ? 'red' : 'yellow'}-900/20 border border-${needsRegistration ? 'red' : 'yellow'}-200 dark:border-${needsRegistration ? 'red' : 'yellow'}-800 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-${needsRegistration ? 'exclamation-circle' : 'exclamation-triangle'} text-${needsRegistration ? 'red' : 'yellow'}-600 dark:text-${needsRegistration ? 'red' : 'yellow'}-400 text-xl mr-3"></i>
                        <div>
                            <h4 class="font-medium text-${needsRegistration ? 'red' : 'yellow'}-900 dark:text-${needsRegistration ? 'red' : 'yellow'}-100">
                                ${needsRegistration ? 'Broker Registration Required' : 'Broker Not Connected'}
                            </h4>
                            <p class="text-sm text-${needsRegistration ? 'red' : 'yellow'}-800 dark:text-${needsRegistration ? 'red' : 'yellow'}-200 mt-1">
                                ${fundsData.message || 'Connect the broker to view live trading data and funds information.'}
                            </p>
                        </div>
                    </div>
                </div>
                ` : ''}

                <!-- Broker Info Header -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                <i class="fas fa-chart-line text-blue-600 dark:text-blue-400 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${brokerName}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Client ID: ${clientId}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${statusColor}-100 text-${statusColor}-800 dark:bg-${statusColor}-900/20 dark:text-${statusColor}-400">
                                <span class="w-2 h-2 bg-${statusColor}-400 rounded-full mr-1"></span>
                                ${connectionStatus.replace('_', ' ').toUpperCase()}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Funds Overview -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Total Funds -->
                    <div class="bg-white dark:bg-slate-800 p-4 rounded-lg border border-gray-200 dark:border-slate-700">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                                <i class="fas fa-wallet text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Funds</p>
                                <p class="text-lg font-semibold text-gray-900 dark:text-white">${formatCurrency(totalFunds)}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Available Cash -->
                    <div class="bg-white dark:bg-slate-800 p-4 rounded-lg border border-gray-200 dark:border-slate-700">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                                <i class="fas fa-money-bill-wave text-green-600 dark:text-green-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Available Cash</p>
                                <p class="text-lg font-semibold text-gray-900 dark:text-white">${formatCurrency(availableCash)}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Used Margin -->
                    <div class="bg-white dark:bg-slate-800 p-4 rounded-lg border border-gray-200 dark:border-slate-700">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-pie text-orange-600 dark:text-orange-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Used Margin</p>
                                <p class="text-lg font-semibold text-gray-900 dark:text-white">${formatCurrency(usedMargin)}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- P&L Section -->
                <div class="bg-white dark:bg-slate-800 p-6 rounded-lg border border-gray-200 dark:border-slate-700">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-chart-bar text-purple-600 dark:text-purple-400 mr-2"></i>
                        Profit & Loss
                    </h4>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Net P&L -->
                        <div class="text-center p-4 rounded-lg ${netPnl >= 0 ? 'bg-green-50 dark:bg-green-900/20' : 'bg-red-50 dark:bg-red-900/20'}">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Net P&L</p>
                            <p class="text-2xl font-bold ${netPnl >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">
                                ${formatCurrency(netPnl)}
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                ${netPnl >= 0 ? '↗' : '↘'} ${formatPercentage(totalFunds > 0 ? (netPnl / totalFunds) * 100 : 0)}
                            </p>
                        </div>

                        <!-- Realized P&L -->
                        <div class="text-center p-4 rounded-lg ${realizedPnl >= 0 ? 'bg-green-50 dark:bg-green-900/20' : 'bg-red-50 dark:bg-red-900/20'}">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Realized P&L</p>
                            <p class="text-xl font-semibold ${realizedPnl >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">
                                ${formatCurrency(realizedPnl)}
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">Booked</p>
                        </div>

                        <!-- Unrealized P&L -->
                        <div class="text-center p-4 rounded-lg ${unrealizedPnl >= 0 ? 'bg-green-50 dark:bg-green-900/20' : 'bg-red-50 dark:bg-red-900/20'}">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Unrealized P&L</p>
                            <p class="text-xl font-semibold ${unrealizedPnl >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">
                                ${formatCurrency(unrealizedPnl)}
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">Open</p>
                        </div>
                    </div>
                </div>

                <!-- Positions & Holdings Summary -->
                ${(positionsData || holdingsData) ? `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    ${positionsData ? `
                    <!-- Positions Summary -->
                    <div class="bg-white dark:bg-slate-800 p-6 rounded-lg border border-gray-200 dark:border-slate-700">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-chart-area text-blue-600 dark:text-blue-400 mr-2"></i>
                            Open Positions
                        </h4>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Total Positions:</span>
                                <span class="font-medium text-gray-900 dark:text-white">${positionsData.positions?.length || 0}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Total P&L:</span>
                                <span class="font-medium ${(positionsData.total_pnl || 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">
                                    ${formatCurrency(positionsData.total_pnl || 0)}
                                </span>
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    ${holdingsData ? `
                    <!-- Holdings Summary -->
                    <div class="bg-white dark:bg-slate-800 p-6 rounded-lg border border-gray-200 dark:border-slate-700">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-briefcase text-purple-600 dark:text-purple-400 mr-2"></i>
                            Holdings
                        </h4>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Total Holdings:</span>
                                <span class="font-medium text-gray-900 dark:text-white">${holdingsData.holdings?.length || 0}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Total Value:</span>
                                <span class="font-medium text-gray-900 dark:text-white">
                                    ${formatCurrency(holdingsData.total_value || 0)}
                                </span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Total P&L:</span>
                                <span class="font-medium ${(holdingsData.total_pnl || 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">
                                    ${formatCurrency(holdingsData.total_pnl || 0)}
                                </span>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>
                ` : ''}

                <!-- Additional Data -->
                ${fundsData.additional_data ? `
                <div class="bg-gray-50 dark:bg-slate-900 p-4 rounded-lg border border-gray-200 dark:border-slate-700">
                    <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                        <i class="fas fa-info-circle text-gray-600 dark:text-gray-400 mr-2"></i>
                        Additional Information
                    </h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        ${Object.entries(fundsData.additional_data).map(([key, value]) => `
                            <div>
                                <span class="text-gray-600 dark:text-gray-400">${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span>
                                <span class="ml-2 font-medium text-gray-900 dark:text-white">${typeof value === 'number' ? formatCurrency(value) : value}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}

                <!-- Last Updated -->
                <div class="text-center text-xs text-gray-500 dark:text-gray-500">
                    <i class="fas fa-clock mr-1"></i>
                    Last updated: ${new Date().toLocaleString()}
                </div>
            </div>
        `;
    }

    /**
     * Create mock funds data for disconnected brokers
     */
    createMockFundsData(status = 'not_connected') {
        let message = 'No data available';

        switch (status) {
            case 'not_connected':
                message = 'Broker is not connected. Connect the broker to view live data.';
                break;
            case 'registration_required':
                message = 'Broker registration incomplete. The broker needs to be properly registered with OpenAlgo to access funds data.';
                break;
            default:
                message = 'No data available';
        }

        return {
            total_funds: 0,
            available_cash: 0,
            used_margin: 0,
            net_pnl: 0,
            realized_pnl: 0,
            unrealized_pnl: 0,
            status: status,
            message: message
        };
    }
}

// Create and initialize
const userDetailsManager = new UserDetailsManager();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('🎯 DOM loaded, initializing user details manager...');
        userDetailsManager.init();
    });
} else {
    console.log('🎯 DOM already ready, initializing user details manager...');
    userDetailsManager.init();
}

// Handle URL changes (for navigation between users)
window.addEventListener('popstate', function() {
    console.log('🔄 URL changed, reinitializing user details...');
    userDetailsManager.init();
});

// Force reinitialization when page is shown (for browser back/forward)
window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
        console.log('🔄 Page restored from cache, reinitializing...');
        userDetailsManager.init();
    }
});

// Global access
window.userDetailsManager = userDetailsManager;

// Test function accessibility
window.testBrokerFunctions = function() {
    console.log('🧪 Testing broker function accessibility...');
    console.log('editSavedBrokerConfig:', typeof userDetailsManager.editSavedBrokerConfig);
    console.log('copyBroker:', typeof userDetailsManager.copyBroker);
    console.log('setupFromSaved:', typeof userDetailsManager.setupFromSaved);
    console.log('deleteSavedBroker:', typeof userDetailsManager.deleteSavedBroker);
};

console.log('✅ User Details Manager ready');
console.log('🔧 Test broker functions with: testBrokerFunctions()');
